# 🐻 BearJia Admin - Vue3 管理后台

<div align="center">

![Vue](https://img.shields.io/badge/Vue-3.4.21-brightgreen.svg)
![Ant Design Vue](https://img.shields.io/badge/Ant%20Design%20Vue-4.1.2-blue.svg)
![Vite](https://img.shields.io/badge/Vite-5.1.4-646CFF.svg)
![License](https://img.shields.io/badge/License-MIT-green.svg)

**基于 Vue3 + Ant Design Vue + Vite 的现代化管理后台系统**

[在线预览](https://admin.javaxiaobear.cn) | [技术文档](https://javaxiaobear.cn) | [更新日志](#更新日志)

</div>

## 📖 项目介绍

BearJia Admin 是一个基于 Vue3 + Composition API + Vite + Ant Design Vue 实现的现代化管理后台系统。采用最新的前端技术栈，提供完整的权限管理、代码生成、系统监控等功能，助力快速开发企业级管理系统。

### ✨ 核心特色

- 🚀 **最新技术栈**：Vue3 + Composition API + Vite + Pinia
- 🎨 **现代化UI**：Ant Design Vue 4.x，原汁原味的设计语言
- 📱 **响应式设计**：完美适配桌面端、平板、手机等设备
- 🔐 **完整权限**：基于RBAC的权限管理，精确到按钮级别
- 🛠️ **代码生成**：一键生成CRUD代码，提升开发效率
- 📊 **系统监控**：服务器监控、缓存监控、在线用户等
- 🎯 **组件封装**：高度封装的业务组件，开箱即用
- 🌙 **主题切换**：支持亮色/暗色主题，多种布局模式

## 🏗️ 技术架构

### 前端技术栈

| 技术 | 版本 | 描述 |
|------|------|------|
| Vue | 3.4.21 | 渐进式JavaScript框架 |
| Vite | 5.1.4 | 下一代前端构建工具 |
| Ant Design Vue | 4.1.2 | 企业级UI组件库 |
| Vue Router | 4.3.0 | Vue.js官方路由管理器 |
| Pinia | 2.1.7 | Vue状态管理库 |
| Axios | 1.6.7 | HTTP客户端 |
| WangEditor | 5.1.23 | 富文本编辑器 |
| ECharts | 5.6.0 | 数据可视化图表库 |

### 后端技术栈

| 技术 | 描述 |
|------|------|
| Spring Boot | Java应用开发框架 |
| Spring Security | 安全框架 |
| MyBatis Plus | 持久层框架 |
| MySQL | 关系型数据库 |
| Redis | 内存数据库 |
| JWT | JSON Web Token |

## 🎯 核心功能

### 🔐 权限管理
- **用户管理**：用户信息维护、角色分配、状态管理
- **角色管理**：角色权限配置、数据权限设置
- **菜单管理**：动态菜单配置、权限控制
- **部门管理**：组织架构管理、树形结构展示
- **岗位管理**：岗位信息维护、人员分配

### 📊 系统监控
- **在线用户**：实时在线用户监控、强制下线
- **服务监控**：服务器性能监控、JVM监控
- **缓存监控**：Redis缓存监控、缓存管理
- **操作日志**：用户操作记录、系统访问日志
- **登录日志**：用户登录记录、异常登录监控

### 🛠️ 系统工具
- **代码生成**：一键生成前后端代码、支持自定义模板
- **系统配置**：动态配置管理、参数设置
- **字典管理**：数据字典维护、下拉选项配置
- **通知公告**：系统通知发布、富文本编辑

### 🎨 界面特色
- **多布局模式**：侧边栏、顶部菜单、混合布局、分栏布局、抽屉布局
- **主题切换**：亮色/暗色主题、主题色自定义
- **工作台**：数据统计、快捷操作、更新日志、网站展示
- **历史导航**：智能标签页管理、快速页面切换

## 🚀 快速开始

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0 或 yarn >= 1.22.0

### 安装依赖

```bash
# 克隆项目
git clone https://gitee.com/javaxiaobear/BearJia_Antdv.git

# 进入项目目录
cd BearJia_Antdv

# 安装依赖
npm install
# 或
yarn install
```

### 开发环境

```bash
# 启动开发服务器
npm run dev
# 或
yarn dev
```

### 生产构建

```bash
# 构建生产版本
npm run build
# 或
yarn build
```

## 📁 项目结构

```
bear-jia-vue3/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API接口
│   ├── assets/            # 资源文件
│   ├── components/        # 公共组件
│   │   ├── BearJiaProTable/  # 表格组件
│   │   ├── editor/           # 富文本编辑器
│   │   └── layout/           # 布局组件
│   ├── composables/       # 组合式函数
│   ├── layout/            # 布局文件
│   ├── router/            # 路由配置
│   ├── stores/            # 状态管理
│   ├── styles/            # 样式文件
│   ├── utils/             # 工具函数
│   ├── views/             # 页面组件
│   └── main.js            # 入口文件
├── .env.development       # 开发环境配置
├── .env.production        # 生产环境配置
├── vite.config.js         # Vite配置
└── package.json           # 项目配置
```

## 🎨 界面预览

### 工作台
- 📊 **数据统计**：用户、角色、菜单、在线用户等核心数据展示
- 🚀 **快捷操作**：常用功能快速入口，提升操作效率
- 📝 **更新日志**：系统版本更新记录，支持折叠展开
- 🌐 **网站展示**：个人网站集合展示，技术栈标签

### 布局模式
- 🔲 **侧边栏布局**：经典的左侧菜单布局
- 🔝 **顶部菜单布局**：顶部水平菜单布局
- 🔀 **混合布局**：顶部一级菜单 + 侧边二级菜单
- 📊 **分栏布局**：多栏式菜单布局
- 📱 **抽屉布局**：移动端友好的抽屉式布局

### 主题特色
- 🌞 **亮色主题**：清新明亮的视觉体验
- 🌙 **暗色主题**：护眼的深色模式
- 🎨 **主题色**：多种主题色可选
- 📱 **响应式**：完美适配各种屏幕尺寸

## 🔧 核心组件

### ProTable 表格组件
```vue
<ProTable
  :api="tableApi"
  :columns="columns"
  :searchFields="searchFields"
  :isTreeTable="true"
  :exportConfig="exportConfig"
  rowKey="id"
>
  <template #bodyCell="{ column, record }">
    <!-- 自定义单元格内容 -->
  </template>
</ProTable>
```

**特性：**
- ✅ 统一的表格布局和样式
- ✅ 集成搜索、分页、导出功能
- ✅ 支持树形表格和可展开行
- ✅ 自定义操作按钮和单元格渲染
- ✅ 响应式设计，移动端友好

### WangEditor 富文本编辑器
```vue
<WangEditor
  v-model:value="content"
  :height="400"
  :imageSize="5"
  :videoSize="50"
  placeholder="请输入内容..."
/>
```

**特性：**
- ✅ 基于 WangEditor v5 最新版本
- ✅ 支持图片、视频上传
- ✅ 丰富的文本格式化功能
- ✅ 自定义工具栏配置
- ✅ 完美的Vue3集成

### HistoryNav 历史导航
```vue
<HistoryNav />
```

**特性：**
- ✅ 智能标签页管理
- ✅ 工作台默认不显示
- ✅ 支持标签页关闭和刷新
- ✅ 右键菜单操作
- ✅ 路由状态同步

## 📈 更新日志

### v1.2.0 (2024-01-15) - 重大功能更新
- ✨ 新增ProTable组件，统一表格布局
- ✨ 完善工作台页面，增加统计功能
- ✨ 优化HistoryNav组件，支持动态显示
- 🐛 修复部门管理树形结构显示问题
- 🐛 修复字典管理可展开行功能
- 🎨 优化导航模式选择样式

### v1.1.5 (2024-01-10) - 功能优化
- ✨ 增强TableActionBar组件扩展性
- ✨ 改进SearchForm组件用户体验
- 🐛 修复导航模式选择样式问题
- 🎨 优化表格操作按钮样式
- 📱 提升移动端适配效果

### v1.1.0 (2024-01-05) - 界面美化
- 🎨 重新设计登录页面
- 🎨 优化主题色彩搭配
- ✨ 增加暗色主题支持
- 📱 提升移动端适配效果
- 🔧 优化构建配置

### v1.0.0 (2024-01-01) - 正式发布
- ✨ 完成基础框架搭建
- ✨ 实现用户权限管理
- ✨ 集成代码生成功能
- ✨ 建立完整的监控体系
- 📚 完善项目文档

## 🔮 后续优化计划

### 🎯 短期计划 (1-2个月)

#### 🔧 功能增强
- [ ] **国际化支持**：多语言切换功能
- [ ] **消息中心**：站内消息、通知推送
- [ ] **文件管理**：文件上传、预览、管理
- [ ] **数据导入**：Excel数据批量导入
- [ ] **API文档**：集成Swagger UI
- [ ] **系统备份**：数据备份与恢复

#### 🎨 界面优化
- [ ] **组件库扩展**：更多业务组件封装
- [ ] **图标库**：自定义图标库集成
- [ ] **动画效果**：页面切换动画优化
- [ ] **加载状态**：全局Loading优化
- [ ] **空状态页**：404、500等错误页面美化
- [ ] **引导页**：新用户引导功能

#### 📱 移动端优化
- [ ] **PWA支持**：渐进式Web应用
- [ ] **触摸优化**：移动端手势操作
- [ ] **离线缓存**：离线数据缓存
- [ ] **推送通知**：移动端消息推送

### 🚀 中期计划 (3-6个月)

#### 🔍 高级功能
- [ ] **全文搜索**：ElasticSearch集成
- [ ] **工作流引擎**：Activiti工作流
- [ ] **报表系统**：动态报表生成
- [ ] **大屏展示**：数据可视化大屏
- [ ] **微服务支持**：Spring Cloud集成
- [ ] **容器化部署**：Docker + K8s

#### 🛡️ 安全增强
- [ ] **单点登录**：SSO集成
- [ ] **OAuth2**：第三方登录
- [ ] **API限流**：接口访问限制
- [ ] **数据加密**：敏感数据加密
- [ ] **审计日志**：完整的操作审计
- [ ] **安全扫描**：代码安全检测

#### 🔧 开发工具
- [ ] **代码模板**：更多代码生成模板
- [ ] **接口测试**：集成API测试工具
- [ ] **性能监控**：APM性能监控
- [ ] **错误追踪**：Sentry错误监控
- [ ] **CI/CD**：自动化部署流水线

### 🌟 长期规划 (6个月以上)

#### 🏗️ 架构升级
- [ ] **微前端**：qiankun微前端架构
- [ ] **Serverless**：无服务器架构
- [ ] **GraphQL**：API查询语言
- [ ] **WebAssembly**：高性能计算
- [ ] **边缘计算**：CDN边缘部署

#### 🤖 智能化
- [ ] **AI助手**：智能代码生成
- [ ] **智能推荐**：个性化推荐
- [ ] **自动化测试**：AI驱动测试
- [ ] **智能运维**：AIOps集成
- [ ] **机器学习**：数据分析预测

#### 🌍 生态建设
- [ ] **插件系统**：可扩展插件架构
- [ ] **主题市场**：主题模板商店
- [ ] **组件市场**：业务组件库
- [ ] **开发者社区**：技术交流平台
- [ ] **培训体系**：在线学习平台

## 🤝 参与贡献

我们欢迎所有形式的贡献，无论是新功能、bug修复、文档改进还是其他任何形式的帮助。

### 贡献指南

1. **Fork 项目**：点击右上角的 Fork 按钮
2. **创建分支**：`git checkout -b feature/your-feature-name`
3. **提交代码**：`git commit -m 'Add some feature'`
4. **推送分支**：`git push origin feature/your-feature-name`
5. **提交PR**：创建 Pull Request

### 开发规范

- 🔧 **代码规范**：遵循 ESLint 配置
- 📝 **提交规范**：使用 Conventional Commits
- 🧪 **测试覆盖**：新功能需要添加测试
- 📚 **文档更新**：重要变更需要更新文档

### 问题反馈

- 🐛 **Bug报告**：[提交Issue](https://gitee.com/javaxiaobear/BearJia_Antdv/issues)
- 💡 **功能建议**：[功能请求](https://gitee.com/javaxiaobear/BearJia_Antdv/issues)
- 💬 **技术交流**：[讨论区](https://gitee.com/javaxiaobear/BearJia_Antdv/issues)

## 📄 开源协议

本项目基于 [MIT License](LICENSE) 开源协议，您可以自由使用、修改和分发。

## 🙏 致谢

感谢以下优秀的开源项目：

- [Vue.js](https://vuejs.org/) - 渐进式JavaScript框架
- [Ant Design Vue](https://antdv.com/) - 企业级UI组件库
- [Vite](https://vitejs.dev/) - 下一代前端构建工具
- [WangEditor](https://www.wangeditor.com/) - 轻量级富文本编辑器
- [ECharts](https://echarts.apache.org/) - 数据可视化图表库

## 📞 联系方式

- 🌐 **个人网站**：[https://javaxiaobear.cn](https://javaxiaobear.cn)
- 📧 **邮箱**：<EMAIL>
- 💬 **QQ群**：123456789
- 🐙 **GitHub**：[JavaXiaoBear](https://github.com/javaxiaobear)
- 🦄 **Gitee**：[JavaXiaoBear](https://gitee.com/javaxiaobear)

## ⭐ Star History

如果这个项目对您有帮助，请给我们一个 ⭐ Star 支持一下！

[![Star History Chart](https://api.star-history.com/svg?repos=javaxiaobear/BearJia_Antdv&type=Date)](https://star-history.com/#javaxiaobear/BearJia_Antdv&Date)

---

<div align="center">

**🐻 Made with ❤️ by JavaXiaoBear**

[⬆ 回到顶部](#-bearjia-admin---vue3-管理后台)

</div>
