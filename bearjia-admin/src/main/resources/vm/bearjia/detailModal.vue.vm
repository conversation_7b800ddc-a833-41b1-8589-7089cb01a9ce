<template>
  <a-modal 
    v-model:visible="pageDataObj.visible" 
    :title="pageDataObj.title" 
    width="60%" 
    :footer="null"
  >
    <a-descriptions bordered :column="2">
#foreach($column in $columns)
#if($column.list)
#set($javaField=$column.javaField)
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#if($column.htmlType == "datetime")
      <a-descriptions-item label="${comment}">
        {{ formatTime(detailFormObj.data.${javaField}) }}
      </a-descriptions-item>
#elseif($column.htmlType == "imageUpload")
      <a-descriptions-item label="${comment}" :span="2">
        <div v-if="detailFormObj.data.${javaField}">
          <a-image
            :width="100"
            :src="detailFormObj.data.${javaField}"
            :preview="{ src: detailFormObj.data.${javaField} }"
          />
        </div>
        <span v-else>-</span>
      </a-descriptions-item>
#elseif($column.htmlType == "fileUpload")
      <a-descriptions-item label="${comment}" :span="2">
        <div v-if="detailFormObj.data.${javaField}">
          <a :href="detailFormObj.data.${javaField}" target="_blank">
            <BearJiaIcon icon="download-outlined" />
            下载文件
          </a>
        </div>
        <span v-else>-</span>
      </a-descriptions-item>
#elseif($column.htmlType == "editor")
      <a-descriptions-item label="${comment}" :span="2">
        <div class="editor-content" v-html="detailFormObj.data.${javaField}"></div>
      </a-descriptions-item>
#elseif($column.htmlType == "textarea")
      <a-descriptions-item label="${comment}" :span="2">
        <div class="textarea-content">
          {{ detailFormObj.data.${javaField} || '-' }}
        </div>
      </a-descriptions-item>
#elseif("" != $column.dictType)
      <a-descriptions-item label="${comment}">
        <dict-tag :options="${javaField}Options" :value="detailFormObj.data.${javaField}" />
      </a-descriptions-item>
#else
      <a-descriptions-item label="${comment}">
        {{ detailFormObj.data.${javaField} || '-' }}
      </a-descriptions-item>
#end
#end
#end
    </a-descriptions>
  </a-modal>
</template>

<script setup>
import { getCurrentInstance, reactive } from 'vue';
import { BearJiaIcon } from '@/utils/BearJiaIcon.js';
import { formatTime } from '@/utils/common.js';

// 获取当前实例
const { proxy } = getCurrentInstance();

// 字典数据
#if(${dicts} != '')
const { ${dicts} } = proxy.useDict(${dicts});
#end
#foreach ($column in $columns)
#if(${column.dictType} != '')
const ${column.javaField}Options = ref(${column.javaField});
#end
#end

// 页面数据对象
const pageDataObj = reactive({
  visible: false,
  title: '${functionName}详情'
});

// 详情数据对象
const detailFormObj = reactive({
  data: {
#foreach ($column in $columns)
#if($column.list)
    ${column.javaField}: '',
#end
#end
  }
});

// 打开弹窗
const open = (record) => {
  pageDataObj.visible = true;
  Object.assign(detailFormObj.data, record);
};

// 暴露方法
defineExpose({
  open
});
</script>

<style lang="less" scoped>
.editor-content {
  max-height: 300px;
  overflow-y: auto;
  padding: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: #fafafa;

  :deep(img) {
    max-width: 100%;
    height: auto;
  }

  :deep(video) {
    max-width: 100%;
    height: auto;
  }
}

.textarea-content {
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 200px;
  overflow-y: auto;
}
</style>
