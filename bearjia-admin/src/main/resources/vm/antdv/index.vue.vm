<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <!-- 条件搜索 -->
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
#set($isInsert=0)
#set($isEdit=0)
#set($queryCount=0)
#set($querySpace="")
#foreach($column in $columns)
#if($column.insert)
#set($isInsert=1)
#end
#if($column.edit)
#set($isEdit=1)
#end
#if($column.query)
#set($queryCount=$queryCount+1)
#set($dictType=$column.dictType)
#set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#if($queryCount == 3)
            <template v-if="advanced">
#set($querySpace="  ")
#end
#if($column.htmlType == "input")
            ${querySpace}<a-col :md="8" :sm="24">
              ${querySpace}<a-form-item label="${comment}" prop="${column.javaField}">
                ${querySpace}<a-input v-model="queryParam.${column.javaField}" placeholder="请输入${comment}" allow-clear/>
              ${querySpace}</a-form-item>
            ${querySpace}</a-col>
#elseif(($column.htmlType == "select" || $column.htmlType == "radio") && "" != $dictType)
            ${querySpace}<a-col :md="8" :sm="24">
              ${querySpace}<a-form-item label="${comment}" prop="${column.javaField}">
                ${querySpace}<a-select placeholder="请选择${comment}" v-model="queryParam.${column.javaField}" style="width: 100%" allow-clear>
                  ${querySpace}<a-select-option v-for="(d, index) in dict.type.${dictType}" :key="index" :value="d.value">{{ d.label }}</a-select-option>
                ${querySpace}</a-select>
              ${querySpace}</a-form-item>
            ${querySpace}</a-col>
#elseif(($column.htmlType == "select" || $column.htmlType == "radio") && $dictType)
            ${querySpace}<a-col :md="8" :sm="24">
              ${querySpace}<a-form-item label="${comment}" prop="${column.javaField}">
                ${querySpace}<a-select placeholder="请选择${comment}" v-model="queryParam.${column.javaField}" style="width: 100%" allow-clear>
                  ${querySpace}<a-select-option>请选择字典生成</a-select-option>
                ${querySpace}</a-select>
              ${querySpace}</a-form-item>
            ${querySpace}</a-col>
#elseif($column.htmlType == "datetime" && $column.queryType != "BETWEEN")
            ${querySpace}<a-col :md="8" :sm="24">
              ${querySpace}<a-form-item label="${comment}" prop="${column.javaField}">
                ${querySpace}<a-date-picker style="width: 100%" v-model="queryParam.${column.javaField}" format="YYYY-MM-DD HH:mm:ss" allow-clear/>
              ${querySpace}</a-form-item>
            ${querySpace}</a-col>
#elseif($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
            ${querySpace}<a-col :md="8" :sm="24">
              ${querySpace}<a-form-item label="${comment}">
                ${querySpace}<a-range-picker style="width: 100%" v-model="daterange${AttrName}" valueFormat="YYYY-MM-DD" format="YYYY-MM-DD" allow-clear/>
              ${querySpace}</a-form-item>
            ${querySpace}</a-col>
#end
#end
#end
#if($queryCount > 2)
            </template>
#end
            <a-col :md="!advanced && 8 || 24" :sm="24">
              <span class="table-page-search-submitButtons" :style="advanced && { float: 'right', overflow: 'hidden' } || {} ">
                <a-button type="primary" @click="handleQuery"><a-icon type="search" />查询</a-button>
                <a-button style="margin-left: 8px" @click="resetQuery"><a-icon type="redo" />重置</a-button>
#if($queryCount > 2)
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
#end
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <!-- 操作 -->
      <div class="table-operations">
#if($isInsert == 1)
        <a-button type="primary" @click="$refs.createForm.handleAdd()" v-hasPermi="['${moduleName}:${businessName}:add']">
          <a-icon type="plus" />新增
        </a-button>
#end
#if($isEdit == 1)
        <a-button type="primary" :disabled="single" @click="$refs.createForm.handleUpdate(undefined, ids)" v-hasPermi="['${moduleName}:${businessName}:edit']">
          <a-icon type="edit" />修改
        </a-button>
#end
        <a-button type="danger" :disabled="multiple" @click="handleDelete" v-hasPermi="['${moduleName}:${businessName}:remove']">
          <a-icon type="delete" />删除
        </a-button>
        <a-button type="primary" @click="handleExport" v-hasPermi="['${moduleName}:${businessName}:export']">
          <a-icon type="download" />导出
        </a-button>
        <table-setting
          :style="{float: 'right'}"
          :table-size.sync="tableSize"
          v-model="columns"
          :refresh-loading="loading"
          @refresh="getList" />
      </div>
#if($isInsert == 1 || $isEdit == 1)
      <!-- 增加修改 -->
      <create-form
        ref="createForm"
#foreach ($column in $columns)
#if(${column.dictType} != '')
        :${column.javaField}Options="dict.type.${column.dictType}"
#end
#end
#if($tplCategory == 'tree')
        :${businessName}Options="${businessName}Options"
        @select-tree="getTreeselect"
#end
        @ok="getList"
      />
#end
      <!-- 数据展示 -->
      <a-table
        :loading="loading"
        :size="tableSize"
        rowKey="${pkColumn.javaField}"
        :columns="columns"
        :data-source="list"
        :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        :pagination="false"
        :bordered="tableBordered"
      >
#foreach($column in $columns)
#set($javaField=$column.javaField)
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#if($column.list && $column.htmlType == "datetime")
        <span slot="${javaField}" slot-scope="text, record">
          {{ parseTime(record.${javaField}) }}
        </span>
#elseif($column.list && "" != $column.dictType)
        <span slot="${javaField}" slot-scope="text, record">
          <dict-tag :options="dict.type['${column.dictType}']" :value="record.${javaField}"/>
        </span>
#end
#end
        <span slot="operation" slot-scope="text, record">
#if($isEdit == 1)
          <a-divider type="vertical" v-hasPermi="['${moduleName}:${businessName}:edit']" />
          <a @click="$refs.createForm.handleUpdate(record, undefined)" v-hasPermi="['${moduleName}:${businessName}:edit']">
            <a-icon type="edit" />修改
          </a>
#end
#if($tplCategory == 'tree' && ($isInsert == 1 || $isEdit == 1))
          <a-divider type="vertical" v-hasPermi="['${moduleName}:${businessName}:add']" />
          <a @click="$refs.createForm.handleAdd(record)" v-hasPermi="['${moduleName}:${businessName}:add']">
            <a-icon type="plus" />新增
          </a>
#end
          <a-divider type="vertical" v-hasPermi="['${moduleName}:${businessName}:remove']" />
          <a @click="handleDelete(record)" v-hasPermi="['${moduleName}:${businessName}:remove']">
            <a-icon type="delete" />删除
          </a>
        </span>
      </a-table>
#if($tplCategory != 'tree')
      <!-- 分页 -->
      <a-pagination
        class="ant-table-pagination"
        show-size-changer
        show-quick-jumper
        :current="queryParam.pageNum"
        :total="total"
        :page-size="queryParam.pageSize"
        :showTotal="total => `共 ${total} 条`"
        @showSizeChange="onShowSizeChange"
        @change="changeSize"
      />
#end
    </a-card>
  </page-header-wrapper>
</template>

<script setup>
import { getCurrentInstance, reactive, ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { list${BusinessName}, del${BusinessName} } from '@/api/${moduleName}/${businessName}'
#if($isInsert == 1 || $isEdit == 1)
import CreateForm from './modules/CreateForm.vue'
#end

// 获取当前实例
const { proxy } = getCurrentInstance()

// 字典数据
#if(${dicts} != '')
const { ${dicts} } = proxy.useDict(${dicts})
#end

// 组件引用
#if($isInsert == 1 || $isEdit == 1)
const createFormRef = ref()
#end

// 响应式数据
const list = ref([])
const selectedRowKeys = ref([])
const selectedRows = ref([])
const advanced = ref(false)
const single = ref(true)
const multiple = ref(true)
const ids = ref([])
const loading = ref(false)
const total = ref(0)
const tableSize = ref('middle')
const tableBordered = ref(false)

#if($tplCategory == 'tree' && ($isInsert == 1 || $isEdit == 1))
const ${businessName}Options = ref([])
#end

#foreach ($column in $columns)
#if($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
#set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
// $comment时间范围
const daterange${AttrName} = ref([])
#end
#end

// 查询参数
const queryParam = reactive({
#foreach ($column in $columns)
#if($column.query)
  $column.javaField: null,
#end
#end
  pageNum: 1,
  pageSize: 10
})

// 表格列配置
const columns = ref([
#foreach($column in $columns)
#set($javaField=$column.javaField)
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#if($column.pk)
  {
    title: '${comment}',
    dataIndex: '${javaField}',
    ellipsis: true,
    align: 'center'
  },
#elseif($column.list && $column.htmlType == "datetime" || $column.list && "" != $column.dictType || $column.list && "" != $column.dictType)
  {
    title: '${comment}',
    dataIndex: '${javaField}',
    customRender: ({ text, record }) => {
#if($column.htmlType == "datetime")
      return proxy.parseTime(record.${javaField})
#elseif("" != $column.dictType)
      return proxy.selectDictLabel(${javaField}.value, record.${javaField})
#else
      return text
#end
    },
    ellipsis: true,
    align: 'center'
  },
#elseif($column.list && "" != $javaField)
  {
    title: '${comment}',
    dataIndex: '${javaField}',
    ellipsis: true,
    align: 'center'
  },
#end
#end
  {
    title: '操作',
    dataIndex: 'operation',
    width: '18%',
    align: 'center'
  }
])
// 方法定义
/** 查询${functionName}列表 */
const getList = () => {
  loading.value = true
#foreach ($column in $columns)
#if($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
  queryParam.params = {}
#break
#end
#end
#foreach ($column in $columns)
#if($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
#set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
  if (daterange${AttrName}.value !== null && daterange${AttrName}.value !== '' && daterange${AttrName}.value.length !== 0) {
    queryParam.params['begin${AttrName}'] = daterange${AttrName}.value[0]
    queryParam.params['end${AttrName}'] = daterange${AttrName}.value[1]
  }
#end
#end
  list${BusinessName}(queryParam).then(response => {
#if($tplCategory == 'tree' && ($isInsert == 1 || $isEdit == 1))
    list.value = proxy.handleTree(response.data, '${treeCode}', '${treeParentCode}')
#else
    list.value = response.rows
#end
    total.value = response.total
    loading.value = false
  })
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParam.pageNum = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
#foreach ($column in $columns)
#if($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
#set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
  daterange${AttrName}.value = []
#end
#end
  Object.assign(queryParam, {
#foreach($column in $columns)
#if($column.query)
#if(!($column.htmlType == "datetime" && $column.queryType == "BETWEEN"))
    ${column.javaField}: undefined,
#end
#end
#end
    pageNum: 1,
    pageSize: 10
  })
  handleQuery()
}

const onShowSizeChange = (current, pageSize) => {
  queryParam.pageSize = pageSize
  getList()
}

const changeSize = (current, pageSize) => {
  queryParam.pageNum = current
  queryParam.pageSize = pageSize
  getList()
}

const onSelectChange = (selectedRowKeysParam, selectedRowsParam) => {
  selectedRowKeys.value = selectedRowKeysParam
  selectedRows.value = selectedRowsParam
  ids.value = selectedRows.value.map(item => item.${pkColumn.javaField})
  single.value = selectedRowKeysParam.length !== 1
  multiple.value = !selectedRowKeysParam.length
}

const toggleAdvanced = () => {
  advanced.value = !advanced.value
}

#if($tplCategory == 'tree' && ($isInsert == 1 || $isEdit == 1))
/** 查询菜单下拉树结构 */
const getTreeselect = () => {
  list${BusinessName}().then(response => {
    ${businessName}Options.value = []
    const data = { ${treeCode}: 0, ${treeName}: '主目录', children: [] }
    data.children = proxy.handleTree(response.data, '${treeCode}', '${treeParentCode}')
    ${businessName}Options.value.push(data)
  })
}
#end

/** 删除按钮操作 */
const handleDelete = (row) => {
  const ${pkColumn.javaField}s = row.${pkColumn.javaField} || ids.value
  proxy.$modal.confirm('确认删除所选中数据?').then(() => {
    return del${BusinessName}(${pkColumn.javaField}s)
  }).then(() => {
    onSelectChange([], [])
    getList()
    message.success('删除成功')
  }).catch(() => {})
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy.$modal.confirm('是否确认导出所有${functionName}数据项?').then(() => {
    proxy.download('${moduleName}/${businessName}/export', {
      ...queryParam
    }, `${businessName}_#[[${new Date().getTime()}]]#.xlsx`)
  }).catch(() => {})
}

// 生命周期
onMounted(() => {
  getList()
#if($tplCategory == 'tree' && ($isInsert == 1 || $isEdit == 1))
  getTreeselect()
#end
})
</script>
