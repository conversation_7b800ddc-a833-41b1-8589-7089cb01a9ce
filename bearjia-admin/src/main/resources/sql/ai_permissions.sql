-- AI Agent 权限配置 SQL
-- 用于在系统菜单表中添加 AI 相关的权限配置

-- 插入 AI 模块菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES 
('AI智能助手', 0, 6, 'ai', NULL, 1, 0, 'M', '0', '0', NULL, 'robot', 'admin', sysdate(), '', NULL, 'AI智能助手模块');

-- 获取刚插入的AI模块菜单ID（需要根据实际情况调整）
SET @ai_menu_id = LAST_INSERT_ID();

-- 插入 AI 子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES 
('AI聊天', @ai_menu_id, 1, 'chat', 'ai/chat/index', 1, 0, 'C', '0', '0', 'ai:chat:use', 'message', 'admin', sysdate(), '', NULL, 'AI聊天界面'),
('函数管理', @ai_menu_id, 2, 'functions', 'ai/functions/index', 1, 0, 'C', '0', '0', 'ai:function:list', 'api', 'admin', sysdate(), '', NULL, 'AI函数管理'),
('使用统计', @ai_menu_id, 3, 'statistics', 'ai/statistics/index', 1, 0, 'C', '0', '0', 'ai:statistics:view', 'bar-chart', 'admin', sysdate(), '', NULL, 'AI使用统计');

-- 插入 AI 功能权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES 
-- AI聊天权限
('AI聊天使用', (SELECT menu_id FROM sys_menu WHERE perms = 'ai:chat:use'), 1, '', '', 1, 0, 'F', '0', '0', 'ai:chat:use', '', 'admin', sysdate(), '', NULL, ''),

-- AI函数权限
('函数列表查看', (SELECT menu_id FROM sys_menu WHERE perms = 'ai:function:list'), 1, '', '', 1, 0, 'F', '0', '0', 'ai:function:list', '', 'admin', sysdate(), '', NULL, ''),
('函数执行', (SELECT menu_id FROM sys_menu WHERE perms = 'ai:function:list'), 2, '', '', 1, 0, 'F', '0', '0', 'ai:function:execute', '', 'admin', sysdate(), '', NULL, ''),
('函数统计', (SELECT menu_id FROM sys_menu WHERE perms = 'ai:function:list'), 3, '', '', 1, 0, 'F', '0', '0', 'ai:function:statistics', '', 'admin', sysdate(), '', NULL, ''),

-- 系统管理相关AI权限
('用户查询AI', 0, 1, '', '', 1, 0, 'F', '0', '0', 'system:user:query', '', 'admin', sysdate(), '', NULL, 'AI查询用户信息'),
('用户列表AI', 0, 2, '', '', 1, 0, 'F', '0', '0', 'system:user:list', '', 'admin', sysdate(), '', NULL, 'AI查询用户列表'),
('用户密码重置AI', 0, 3, '', '', 1, 0, 'F', '0', '0', 'system:user:resetPwd', '', 'admin', sysdate(), '', NULL, 'AI重置用户密码'),
('用户状态修改AI', 0, 4, '', '', 1, 0, 'F', '0', '0', 'system:user:edit', '', 'admin', sysdate(), '', NULL, 'AI修改用户状态');

-- 为管理员角色分配AI权限
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT 1, menu_id FROM sys_menu WHERE perms IN (
    'ai:chat:use',
    'ai:function:list', 
    'ai:function:execute',
    'ai:function:statistics',
    'ai:statistics:view',
    'system:user:query',
    'system:user:list',
    'system:user:resetPwd',
    'system:user:edit'
);

-- 创建AI使用日志表（可选）
CREATE TABLE IF NOT EXISTS ai_usage_log (
    log_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    user_name VARCHAR(30) NOT NULL COMMENT '用户名',
    function_name VARCHAR(100) COMMENT '调用的函数名',
    function_description TEXT COMMENT '函数描述',
    request_content TEXT COMMENT '请求内容',
    response_content TEXT COMMENT '响应内容',
    is_dangerous TINYINT(1) DEFAULT 0 COMMENT '是否危险操作',
    execution_time INT COMMENT '执行时间(毫秒)',
    status TINYINT(1) DEFAULT 1 COMMENT '执行状态(0失败 1成功)',
    error_message TEXT COMMENT '错误信息',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    user_agent VARCHAR(500) COMMENT '用户代理',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_user_id (user_id),
    INDEX idx_function_name (function_name),
    INDEX idx_create_time (create_time),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI使用日志表';

-- 创建AI会话表（可选）
CREATE TABLE IF NOT EXISTS ai_session (
    session_id VARCHAR(64) PRIMARY KEY COMMENT '会话ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    user_name VARCHAR(30) NOT NULL COMMENT '用户名',
    session_title VARCHAR(200) COMMENT '会话标题',
    message_count INT DEFAULT 0 COMMENT '消息数量',
    last_message_time DATETIME COMMENT '最后消息时间',
    status TINYINT(1) DEFAULT 1 COMMENT '会话状态(0结束 1活跃)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI会话表';

-- 创建AI消息表（可选）
CREATE TABLE IF NOT EXISTS ai_message (
    message_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '消息ID',
    session_id VARCHAR(64) NOT NULL COMMENT '会话ID',
    role ENUM('user', 'assistant', 'system', 'function') NOT NULL COMMENT '消息角色',
    content TEXT COMMENT '消息内容',
    function_call JSON COMMENT '函数调用信息',
    token_count INT COMMENT 'Token数量',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_session_id (session_id),
    INDEX idx_role (role),
    INDEX idx_create_time (create_time),
    FOREIGN KEY (session_id) REFERENCES ai_session(session_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI消息表';
