# 项目相关配置
project:
  # 名称
  name: Javaxiaobear_vue-ai
  # 版本
  version: 1.0.0
  # 版权年份
  copyrightYear: 2025
  # 文件上传路径 (默认为项目根目录下的upload文件夹)
  # 生产环境可通过环境变量 UPLOAD_PATH 自定义路径
  profile: ${UPLOAD_PATH:./upload}
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: char

# AI Agent 配置
ai:
  # 系统提示词
  system:
    prompt: |
      你是一个智能的企业管理助手，能够调用系统内部的工具来帮助用户完成各种管理任务。

      核心原则：
      1. 准确理解用户意图，选择合适的工具
      2. 当信息不完整时，主动询问必要的参数
      3. 对于危险操作，清楚说明操作的影响
      4. 用友好、专业的语言与用户交流
      5. 保护用户隐私，不泄露敏感信息

      你可以帮助用户：
      - 查询用户、角色、部门等信息
      - 执行用户管理操作（需要相应权限）
      - 生成报表和统计信息
      - 系统配置和监控

      请始终以用户的最佳利益为出发点，提供准确、有用的帮助。

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.javaxiaobear: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  ai:
    openai:
      # API Key (请在环境变量中设置 OPENAI_API_KEY)
      api-key: sk-3ca95bcca3af443bac56fd38053438c8
      # API 基础URL (可选，用于代理)
      base-url: https://api.deepseek.com
      chat:
        options:
          # 使用的模型
          model: deepseek-chat
          # 温度参数
          temperature: 0.7
          # 最大token数
          max-tokens: 1000
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  data:
    redis:
      host: localhost
      port: 6379
      database: 0
      password:
      timeout: 10s

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥 (生产环境请通过环境变量 JWT_SECRET 设置更复杂的密钥)
  secret: ${JWT_SECRET:javaxiaobear2025SecureTokenKeyForJWTSigningWithHS512AlgorithmRequiresAtLeast512BitsKey}
  # 令牌有效期（默认30分钟）
  expireTime: 30

# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.javaxiaobear.module.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mybatis/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# 代码生成
gen:
  # 作者
  author: javaxiaobear
  # 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool
  packageName: com.javaxiaobear.project.system
  # 自动去除表前缀，默认是true
  autoRemovePre: false
  # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）
  tablePrefix: sys_

# Spring Boot 3 新特性配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,env,configprops,beans,mappings
      base-path: /actuator
  endpoint:
    health:
      show-details: always
      show-components: always
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
        step: 60s
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5, 0.9, 0.95, 0.99
    tags:
      application: ${spring.application.name:bearjia-admin}
      environment: ${spring.profiles.active:dev}

# SpringDoc OpenAPI 配置 (替代 Swagger)
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  info:
    title: 小熊管理系统 API
    description: 基于Spring Boot 3 + Vue3的前后端分离管理系统
    version: ${project.version:1.0.0}
    contact:
      name: javaxiaobear
      url: http://www.javaxiaobear.cn
      email: <EMAIL>
  group-configs:
    - group: 'system'
      paths-to-match: '/system/**'
      display-name: '系统管理'
    - group: 'ai'
      paths-to-match: '/ai/**'
      display-name: 'AI助手'
    - group: 'monitor'
      paths-to-match: '/monitor/**'
      display-name: '系统监控'