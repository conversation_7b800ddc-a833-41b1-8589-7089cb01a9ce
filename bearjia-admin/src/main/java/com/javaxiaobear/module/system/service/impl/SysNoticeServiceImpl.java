package com.javaxiaobear.module.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.javaxiaobear.module.system.domain.SysNotice;
import com.javaxiaobear.module.system.mapper.SysNoticeMapper;
import com.javaxiaobear.module.system.service.ISysNoticeService;
import com.javaxiaobear.base.ai.annotation.AiFunction;

/**
 * 公告 服务层实现
 * 
 * <AUTHOR>
 */
@Service
public class SysNoticeServiceImpl implements ISysNoticeService
{
    @Autowired
    private SysNoticeMapper noticeMapper;

    /**
     * 查询公告信息
     *
     * @param noticeId 公告ID
     * @return 公告信息
     */
    @Override
    @AiFunction(
        description = "根据公告ID查询公告详细信息。返回指定公告的完整内容，包括标题、内容、类型、状态等。",
        category = "query",
        priority = 6,
        requiredRoles = {"system:notice:query"}
    )
    public SysNotice selectNoticeById(Long noticeId)
    {
        return noticeMapper.selectNoticeById(noticeId);
    }

    /**
     * 查询公告列表
     *
     * @param notice 公告信息
     * @return 公告集合
     */
    @Override
    @AiFunction(
        description = "查询系统公告列表。支持按公告标题、类型、状态等条件搜索，返回公告的基本信息和发布状态。",
        category = "query",
        priority = 6,
        requiredRoles = {"system:notice:list"}
    )
    public List<SysNotice> selectNoticeList(SysNotice notice)
    {
        return noticeMapper.selectNoticeList(notice);
    }

    /**
     * 新增公告
     *
     * @param notice 公告信息
     * @return 结果
     */
    @Override
    @AiFunction(
        description = "发布新的系统公告。需要提供公告标题、内容、类型等信息。支持创建通知和公告两种类型。",
        category = "create",
        priority = 7,
        requiredRoles = {"system:notice:add"}
    )
    public int insertNotice(SysNotice notice)
    {
        return noticeMapper.insertNotice(notice);
    }

    /**
     * 修改公告
     *
     * @param notice 公告信息
     * @return 结果
     */
    @Override
    @AiFunction(
        description = "修改系统公告信息。可以更新公告标题、内容、类型、状态等属性。",
        category = "modify",
        priority = 6,
        requiredRoles = {"system:notice:edit"}
    )
    public int updateNotice(SysNotice notice)
    {
        return noticeMapper.updateNotice(notice);
    }

    /**
     * 删除公告对象
     *
     * @param noticeId 公告ID
     * @return 结果
     */
    @Override
    @AiFunction(
        description = "删除指定的系统公告。删除后该公告将不再显示给用户。",
        category = "delete",
        priority = 5,
        requiredRoles = {"system:notice:remove"}
    )
    public int deleteNoticeById(Long noticeId)
    {
        return noticeMapper.deleteNoticeById(noticeId);
    }

    /**
     * 批量删除公告信息
     * 
     * @param noticeIds 需要删除的公告ID
     * @return 结果
     */
    @Override
    public int deleteNoticeByIds(Long[] noticeIds)
    {
        return noticeMapper.deleteNoticeByIds(noticeIds);
    }
}
