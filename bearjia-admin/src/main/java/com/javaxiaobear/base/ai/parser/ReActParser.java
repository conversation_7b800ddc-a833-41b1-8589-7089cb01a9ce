package com.javaxiaobear.base.ai.parser;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.javaxiaobear.base.ai.dto.FunctionCallAnalysis;
import com.javaxiaobear.base.ai.model.ReActTurn;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * ReAct响应解析器
 * 
 * 负责解析AI返回的ReAct格式响应，提取思考、行动和最终答案
 * 
 * <AUTHOR>
 */
@Component
public class ReActParser {
    
    private static final Logger logger = LoggerFactory.getLogger(ReActParser.class);
    
    @Autowired
    private ObjectMapper objectMapper;
    
    // 正则表达式模式
    private static final Pattern THOUGHT_PATTERN = Pattern.compile(
        "Thought:\\s*(.*?)(?=Action:|Final Answer:|$)", 
        Pattern.DOTALL | Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern ACTION_PATTERN = Pattern.compile(
        "Action:\\s*(.*?)(?=Observation:|Final Answer:|Thought:|$)", 
        Pattern.DOTALL | Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern FINAL_ANSWER_PATTERN = Pattern.compile(
        "Final Answer:\\s*(.*?)$", 
        Pattern.DOTALL | Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern JSON_PATTERN = Pattern.compile(
        "\\{.*\\}", 
        Pattern.DOTALL
    );
    
    /**
     * 解析AI的ReAct响应
     * 
     * @param llmOutput AI的原始输出
     * @param turnNumber 回合编号
     * @return 解析后的ReAct回合
     */
    public ReActTurn parse(String llmOutput, int turnNumber) {
        ReActTurn turn = new ReActTurn(turnNumber);
        
        try {
            logger.debug("开始解析ReAct响应，回合: {}", turnNumber);
            logger.debug("原始输出: {}", llmOutput);
            
            // 清理输出文本
            String cleanOutput = cleanOutput(llmOutput);
            
            // 解析思考
            String thought = extractThought(cleanOutput);
            if (thought != null) {
                turn.setThought(thought.trim());
                logger.debug("提取到思考: {}", thought);
            }
            
            // 检查是否有最终答案
            String finalAnswer = extractFinalAnswer(cleanOutput);
            if (finalAnswer != null) {
                turn.setFinalAnswer(finalAnswer.trim());
                logger.debug("提取到最终答案: {}", finalAnswer);
                return turn;
            }
            
            // 解析行动
            FunctionCallAnalysis action = extractAction(cleanOutput);
            if (action != null) {
                turn.setAction(action);
                logger.debug("提取到行动: {}", action.getFunctionName());
            }
            
            // 验证解析结果
            if (!turn.hasThought() && !turn.hasAction() && !turn.isFinalAnswer()) {
                turn.markError("无法从AI响应中提取有效的思考、行动或最终答案");
                logger.warn("解析失败，无有效内容: {}", cleanOutput);
            }
            
        } catch (Exception e) {
            logger.error("解析ReAct响应时发生错误: {}", e.getMessage(), e);
            turn.markError("解析AI响应时发生错误: " + e.getMessage());
        }
        
        return turn;
    }
    
    /**
     * 清理AI输出文本
     */
    private String cleanOutput(String output) {
        if (output == null) {
            return "";
        }
        
        // 移除markdown代码块标记
        output = output.replaceAll("```[a-zA-Z]*\\n?", "");
        output = output.replaceAll("```", "");
        
        // 移除多余的空白字符
        output = output.replaceAll("\\n\\s*\\n", "\n");
        output = output.trim();
        
        return output;
    }
    
    /**
     * 提取思考内容
     */
    private String extractThought(String output) {
        Matcher matcher = THOUGHT_PATTERN.matcher(output);
        if (matcher.find()) {
            return matcher.group(1).trim();
        }
        return null;
    }
    
    /**
     * 提取最终答案
     */
    private String extractFinalAnswer(String output) {
        Matcher matcher = FINAL_ANSWER_PATTERN.matcher(output);
        if (matcher.find()) {
            return matcher.group(1).trim();
        }
        return null;
    }
    
    /**
     * 提取行动（函数调用）
     */
    private FunctionCallAnalysis extractAction(String output) {
        Matcher actionMatcher = ACTION_PATTERN.matcher(output);
        if (!actionMatcher.find()) {
            return null;
        }
        
        String actionContent = actionMatcher.group(1).trim();
        logger.debug("提取到行动内容: {}", actionContent);
        
        // 尝试解析JSON格式的函数调用
        Matcher jsonMatcher = JSON_PATTERN.matcher(actionContent);
        if (jsonMatcher.find()) {
            String jsonStr = jsonMatcher.group();
            return parseJsonAction(jsonStr);
        }
        
        // 如果不是JSON格式，尝试解析简单格式
        return parseSimpleAction(actionContent);
    }
    
    /**
     * 解析JSON格式的行动
     */
    private FunctionCallAnalysis parseJsonAction(String jsonStr) {
        try {
            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            
            // 获取函数名
            String functionName = null;
            if (jsonNode.has("functionName")) {
                functionName = jsonNode.get("functionName").asText();
            } else if (jsonNode.has("function")) {
                functionName = jsonNode.get("function").asText();
            } else if (jsonNode.has("name")) {
                functionName = jsonNode.get("name").asText();
            }
            
            if (functionName == null || functionName.trim().isEmpty()) {
                logger.warn("JSON中未找到有效的函数名: {}", jsonStr);
                return null;
            }
            
            // 获取参数
            Map<String, Object> params = new HashMap<>();
            if (jsonNode.has("params")) {
                JsonNode paramsNode = jsonNode.get("params");
                params = objectMapper.convertValue(paramsNode, Map.class);
            } else if (jsonNode.has("parameters")) {
                JsonNode paramsNode = jsonNode.get("parameters");
                params = objectMapper.convertValue(paramsNode, Map.class);
            } else if (jsonNode.has("arguments")) {
                JsonNode paramsNode = jsonNode.get("arguments");
                params = objectMapper.convertValue(paramsNode, Map.class);
            }
            
            // 创建函数调用分析对象
            FunctionCallAnalysis analysis = new FunctionCallAnalysis(
                true, 
                functionName, 
                0.9, // 默认信心度
                params, 
                "ReAct智能体生成的函数调用"
            );
            
            logger.debug("成功解析JSON行动: 函数={}, 参数={}", functionName, params);
            return analysis;
            
        } catch (Exception e) {
            logger.error("解析JSON行动失败: {}, 错误: {}", jsonStr, e.getMessage());
            return null;
        }
    }
    
    /**
     * 解析简单格式的行动
     */
    private FunctionCallAnalysis parseSimpleAction(String actionContent) {
        // 尝试解析 "functionName(param1=value1, param2=value2)" 格式
        Pattern simplePattern = Pattern.compile("(\\w+)\\s*\\(([^)]*)\\)");
        Matcher matcher = simplePattern.matcher(actionContent);
        
        if (matcher.find()) {
            String functionName = matcher.group(1);
            String paramsStr = matcher.group(2);
            
            Map<String, Object> params = parseSimpleParams(paramsStr);
            
            FunctionCallAnalysis analysis = new FunctionCallAnalysis(
                true,
                functionName,
                0.8, // 简单格式的信心度稍低
                params,
                "ReAct智能体生成的简单格式函数调用"
            );
            
            logger.debug("成功解析简单行动: 函数={}, 参数={}", functionName, params);
            return analysis;
        }
        
        logger.warn("无法解析行动内容: {}", actionContent);
        return null;
    }
    
    /**
     * 解析简单格式的参数
     */
    private Map<String, Object> parseSimpleParams(String paramsStr) {
        Map<String, Object> params = new HashMap<>();
        
        if (paramsStr == null || paramsStr.trim().isEmpty()) {
            return params;
        }
        
        // 分割参数
        String[] paramPairs = paramsStr.split(",");
        for (String pair : paramPairs) {
            String[] keyValue = pair.split("=", 2);
            if (keyValue.length == 2) {
                String key = keyValue[0].trim();
                String value = keyValue[1].trim();
                
                // 移除引号
                if (value.startsWith("\"") && value.endsWith("\"")) {
                    value = value.substring(1, value.length() - 1);
                } else if (value.startsWith("'") && value.endsWith("'")) {
                    value = value.substring(1, value.length() - 1);
                }
                
                params.put(key, value);
            }
        }
        
        return params;
    }
    
    /**
     * 验证解析结果的有效性
     */
    public boolean isValidTurn(ReActTurn turn) {
        if (turn == null) {
            return false;
        }
        
        if (turn.isError()) {
            return false;
        }
        
        // 必须有思考、行动或最终答案中的至少一个
        return turn.hasThought() || turn.hasAction() || turn.isFinalAnswer();
    }
}
