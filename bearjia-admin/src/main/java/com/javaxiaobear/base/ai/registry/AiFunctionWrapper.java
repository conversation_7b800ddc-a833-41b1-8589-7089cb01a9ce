package com.javaxiaobear.base.ai.registry;

import java.lang.reflect.Method;
import java.util.Set;

/**
 * AI 函数包装器
 * 
 * 用于存储一个 AI 函数的所有元数据信息，包括方法引用、描述、权限要求等。
 * 这个类是函数注册和调用的核心数据结构。
 * 
 * <AUTHOR>
 */
public class AiFunctionWrapper {
    
    /** 函数名称 */
    private final String functionName;
    
    /** 函数描述 */
    private final String description;
    
    /** 是否为危险操作 */
    private final boolean isDangerous;
    
    /** 所需角色集合 */
    private final Set<String> requiredRoles;
    
    /** 函数分类 */
    private final String category;
    
    /** 函数优先级 */
    private final int priority;
    
    /** 目标对象实例 */
    private final Object targetBean;
    
    /** 目标方法 */
    private final Method targetMethod;
    
    /** 输入参数类型 */
    private final Class<?> inputType;
    
    /** 输出参数类型 */
    private final Class<?> outputType;

    public AiFunctionWrapper(String functionName, String description, boolean isDangerous, 
                           Set<String> requiredRoles, String category, int priority,
                           Object targetBean, Method targetMethod, Class<?> inputType, Class<?> outputType) {
        this.functionName = functionName;
        this.description = description;
        this.isDangerous = isDangerous;
        this.requiredRoles = requiredRoles;
        this.category = category;
        this.priority = priority;
        this.targetBean = targetBean;
        this.targetMethod = targetMethod;
        this.inputType = inputType;
        this.outputType = outputType;
    }

    /**
     * 调用函数
     * 
     * @param arguments 函数参数
     * @return 函数执行结果
     * @throws Exception 执行异常
     */
    public Object invoke(Object arguments) throws Exception {
        try {
            targetMethod.setAccessible(true);

            // 获取方法的实际参数类型
            Class<?> actualParamType = targetMethod.getParameterTypes()[0];
            Object actualArgument = convertArgument(arguments, actualParamType);

            return targetMethod.invoke(targetBean, actualArgument);
        } catch (Exception e) {
            throw new RuntimeException("AI函数调用失败: " + functionName, e);
        }
    }

    /**
     * 转换参数类型
     */
    private Object convertArgument(Object arguments, Class<?> targetType) {
        if (arguments == null) {
            return null;
        }

        // 如果类型匹配，直接返回
        if (targetType.isAssignableFrom(arguments.getClass())) {
            return arguments;
        }

        // 处理基本类型转换
        if (arguments instanceof java.util.Map) {
            java.util.Map<?, ?> map = (java.util.Map<?, ?>) arguments;

            if (targetType == Long.class || targetType == long.class) {
                // 从Map中提取userId、id等字段
                Object value = map.get("userId");
                if (value == null) value = map.get("id");
                if (value == null) value = map.get("value");

                if (value instanceof Number) {
                    return ((Number) value).longValue();
                } else if (value instanceof String) {
                    return Long.parseLong((String) value);
                }
            } else if (targetType == Integer.class || targetType == int.class) {
                Object value = map.get("value");
                if (value == null) value = map.get("id");

                if (value instanceof Number) {
                    return ((Number) value).intValue();
                } else if (value instanceof String) {
                    return Integer.parseInt((String) value);
                }
            } else if (targetType == String.class) {
                Object value = map.get("value");
                if (value == null) value = map.get("keyword");
                if (value == null) value = map.get("name");

                return value != null ? value.toString() : null;
            }
        }

        // 如果无法转换，返回原始参数
        return arguments;
    }

    // Getters
    public String getFunctionName() {
        return functionName;
    }

    public String getDescription() {
        return description;
    }

    public boolean isDangerous() {
        return isDangerous;
    }

    public Set<String> getRequiredRoles() {
        return requiredRoles;
    }

    public String getCategory() {
        return category;
    }

    public int getPriority() {
        return priority;
    }

    public Object getTargetBean() {
        return targetBean;
    }

    public Method getTargetMethod() {
        return targetMethod;
    }

    public Class<?> getInputType() {
        return inputType;
    }

    public Class<?> getOutputType() {
        return outputType;
    }

    @Override
    public String toString() {
        return "AiFunctionWrapper{" +
                "functionName='" + functionName + '\'' +
                ", description='" + description + '\'' +
                ", isDangerous=" + isDangerous +
                ", requiredRoles=" + requiredRoles +
                ", category='" + category + '\'' +
                ", priority=" + priority +
                ", inputType=" + inputType.getSimpleName() +
                ", outputType=" + outputType.getSimpleName() +
                '}';
    }
}
