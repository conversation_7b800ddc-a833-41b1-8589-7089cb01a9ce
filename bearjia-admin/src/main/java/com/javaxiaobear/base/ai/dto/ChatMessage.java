package com.javaxiaobear.base.ai.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * 聊天消息数据传输对象
 * 
 * 用于表示对话历史中的单条消息，包括用户消息、AI回复和系统消息。
 * 
 * <AUTHOR>
 */
public class ChatMessage {

    /** 消息角色 */
    private MessageRole role;
    
    /** 消息内容 */
    private String content;
    
    /** 消息时间戳 */
    private Instant timestamp;
    
    /** 消息ID（可选，用于消息追踪） */
    private String messageId;
    
    /** 函数调用信息（仅当消息涉及函数调用时） */
    private FunctionCall functionCall;

    /**
     * 消息角色枚举
     */
    public enum MessageRole {
        /** 用户消息 */
        USER("user"),
        /** AI助手消息 */
        ASSISTANT("assistant"),
        /** 系统消息 */
        SYSTEM("system"),
        /** 函数调用结果 */
        FUNCTION("function");

        private final String value;

        MessageRole(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @JsonCreator
        public static MessageRole fromValue(String value) {
            for (MessageRole role : MessageRole.values()) {
                if (role.value.equals(value)) {
                    return role;
                }
            }
            throw new IllegalArgumentException("Unknown MessageRole value: " + value);
        }
    }

    /**
     * 函数调用信息
     */
    public static class FunctionCall {
        /** 函数名称 */
        private String name;
        
        /** 函数参数（JSON字符串） */
        private String arguments;
        
        /** 函数执行结果 */
        private String result;

        public FunctionCall() {}

        public FunctionCall(String name, String arguments) {
            this.name = name;
            this.arguments = arguments;
        }

        public FunctionCall(String name, String arguments, String result) {
            this.name = name;
            this.arguments = arguments;
            this.result = result;
        }

        // Getters and Setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public String getArguments() { return arguments; }
        public void setArguments(String arguments) { this.arguments = arguments; }
        
        public String getResult() { return result; }
        public void setResult(String result) { this.result = result; }
    }

    // 构造方法
    public ChatMessage() {
        this.timestamp = Instant.now();
    }

    public ChatMessage(MessageRole role, String content) {
        this.role = role;
        this.content = content;
        this.timestamp = Instant.now();
    }

    public ChatMessage(MessageRole role, String content, String messageId) {
        this.role = role;
        this.content = content;
        this.messageId = messageId;
        this.timestamp = Instant.now();
    }

    // 静态工厂方法
    public static ChatMessage userMessage(String content) {
        return new ChatMessage(MessageRole.USER, content);
    }

    public static ChatMessage assistantMessage(String content) {
        return new ChatMessage(MessageRole.ASSISTANT, content);
    }

    public static ChatMessage systemMessage(String content) {
        return new ChatMessage(MessageRole.SYSTEM, content);
    }

    public static ChatMessage functionMessage(String content, FunctionCall functionCall) {
        ChatMessage message = new ChatMessage(MessageRole.FUNCTION, content);
        message.setFunctionCall(functionCall);
        return message;
    }

    // Getters and Setters
    public MessageRole getRole() { return role; }
    public void setRole(MessageRole role) { this.role = role; }
    
    public String getContent() { return content; }
    public void setContent(String content) { this.content = content; }
    
    public Instant getTimestamp() { return timestamp; }
    public void setTimestamp(Instant timestamp) { this.timestamp = timestamp; }

    /**
     * 获取本地时间格式的时间戳
     * @return LocalDateTime
     */
    @JsonIgnore
    public LocalDateTime getLocalTimestamp() {
        return timestamp != null ? LocalDateTime.ofInstant(timestamp, ZoneId.systemDefault()) : null;
    }
    
    public String getMessageId() { return messageId; }
    public void setMessageId(String messageId) { this.messageId = messageId; }
    
    public FunctionCall getFunctionCall() { return functionCall; }
    public void setFunctionCall(FunctionCall functionCall) { this.functionCall = functionCall; }

    @Override
    public String toString() {
        return "ChatMessage{" +
                "role=" + role +
                ", content='" + content + '\'' +
                ", timestamp=" + timestamp +
                ", messageId='" + messageId + '\'' +
                ", functionCall=" + functionCall +
                '}';
    }
}
