package com.javaxiaobear.base.ai.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * AI 函数注解
 * 
 * 用于标记可以被 AI Agent 调用的方法。被此注解标记的方法将自动注册到 AI 函数库中，
 * 供 AI 模型在对话过程中调用以完成特定任务。
 * 
 * 使用示例：
 * <pre>
 * {@code
 * @AiFunction(
 *     description = "根据用户ID查询用户详细信息，包括基本信息、角色和权限",
 *     dangerous = false,
 *     requiredRoles = {"ROLE_USER_QUERY", "ROLE_ADMIN"}
 * )
 * public UserDetailResult getUserDetail(GetUserDetailRequest request) {
 *     // 实现逻辑
 * }
 * }
 * </pre>
 * 
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface AiFunction {

    /**
     * [必需] 函数功能的详细描述
     * 
     * 这个描述将提供给 AI 模型，让它理解函数的作用和使用场景。
     * 描述应该清晰、准确，包含关键信息，例如：
     * - 函数的主要功能
     * - 输入参数的含义
     * - 返回结果的内容
     * - 使用场景和限制条件
     * 
     * 示例：
     * "根据订单ID查询订单的详细信息，包括商品列表、金额、状态和物流信息。适用于客服查询和用户自助查询场景。"
     */
    String description();

    /**
     * [可选] 标记此操作是否为危险操作
     * 
     * 如果设置为 true，系统在调用前需要用户进行二次确认。
     * 危险操作通常包括：
     * - 删除数据
     * - 修改重要配置
     * - 执行支付或转账
     * - 发送通知或邮件
     * - 修改用户权限
     * 
     * 默认值：false
     */
    boolean dangerous() default false;

    /**
     * [可选] 调用此 AI 函数所需要的角色列表
     * 
     * 如果设置了此项，当前登录用户必须拥有其中至少一个角色才能调用此函数。
     * 角色名称应该与系统中定义的角色保持一致。
     * 
     * 示例：
     * - {"ROLE_ADMIN"} - 仅管理员可调用
     * - {"ROLE_ADMIN", "ROLE_FINANCE_MANAGER"} - 管理员或财务经理可调用
     * - {} - 所有通过认证的用户均可调用（默认值）
     * 
     * 默认值：空数组，表示所有通过认证的用户均可调用
     */
    String[] requiredRoles() default {};

    /**
     * [可选] 函数的分类标签
     * 
     * 用于对函数进行分类管理，便于 AI 模型理解和选择合适的函数。
     * 常见分类包括：
     * - "query" - 查询类操作
     * - "modify" - 修改类操作
     * - "system" - 系统管理类操作
     * - "report" - 报表统计类操作
     * 
     * 默认值：空字符串
     */
    String category() default "";

    /**
     * [可选] 函数的优先级
     * 
     * 当 AI 模型面临多个可选函数时，优先级高的函数会被优先考虑。
     * 取值范围：1-10，数值越大优先级越高。
     * 
     * 默认值：5（中等优先级）
     */
    int priority() default 5;
}
