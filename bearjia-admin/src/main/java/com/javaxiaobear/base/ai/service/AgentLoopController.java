package com.javaxiaobear.base.ai.service;

import com.javaxiaobear.base.ai.client.AiClientHelper;
import com.javaxiaobear.base.ai.dto.AiResponse;
import com.javaxiaobear.base.ai.dto.ChatMessage;
import com.javaxiaobear.base.ai.dto.ChatRequest;
import com.javaxiaobear.base.ai.executor.DynamicFunctionExecutor;
import com.javaxiaobear.base.ai.model.ReActTurn;
import com.javaxiaobear.base.ai.parser.ReActParser;
import com.javaxiaobear.base.ai.prompt.AiPromptFactory;
import com.javaxiaobear.base.ai.registry.AiFunctionWrapper;
import com.javaxiaobear.base.ai.registry.DynamicFunctionRegistry;
import com.javaxiaobear.base.common.utils.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * ReAct智能体循环控制器
 * 
 * 负责驱动整个ReAct思考-行动-观察循环
 * 
 * <AUTHOR>
 */
@Service
public class AgentLoopController {
    
    private static final Logger logger = LoggerFactory.getLogger(AgentLoopController.class);
    
    @Autowired
    private AiClientHelper aiClientHelper;
    
    @Autowired
    private DynamicFunctionExecutor functionExecutor;
    
    @Autowired
    private AiPromptFactory promptFactory;
    
    @Autowired
    private ReActParser reActParser;
    
    @Autowired
    private DynamicFunctionRegistry functionRegistry;
    
    // 配置参数
    private static final int MAX_TURNS = 10; // 最大循环次数
    private static final int MAX_SAME_ACTION_RETRIES = 3; // 同一行动最大重试次数
    private static final long MAX_EXECUTION_TIME = 300000; // 最大执行时间（5分钟）
    
    /**
     * 处理ReAct智能体请求
     * 
     * @param request 聊天请求
     * @return AI响应
     */
    public AiResponse processReActRequest(ChatRequest request) {
        long startTime = System.currentTimeMillis();
        List<ReActTurn> history = new ArrayList<>();
        String userInput = request.getMessage();
        
        logger.info("🤖 开始ReAct智能体处理: {}", userInput);
        
        try {
            // 获取用户权限
            Set<String> userRoles = getUserRoles();
            
            // 获取可用函数
            List<AiFunctionWrapper> availableFunctions = functionRegistry.getAvailableFunctions(userRoles);
            logger.info("📋 可用函数数量: {}", availableFunctions.size());
            
            // 执行ReAct循环
            for (int turnNumber = 1; turnNumber <= MAX_TURNS; turnNumber++) {
                logger.info("🔄 开始第{}轮ReAct循环", turnNumber);
                
                // 检查执行时间
                if (System.currentTimeMillis() - startTime > MAX_EXECUTION_TIME) {
                    logger.warn("⏰ ReAct执行超时，强制终止");
                    AiResponse response = AiResponse.error("任务执行超时，已自动终止", buildChatHistory(history, userInput));
                    response.setReActHistory(history);
                    return response;
                }
                
                // 1. 构建Prompt
                String prompt = promptFactory.buildReActPrompt(userInput, history, availableFunctions);
                
                // 2. 调用AI获取思考和行动
                String llmOutput = callAI(prompt);
                if (llmOutput == null) {
                    return AiResponse.error("AI服务调用失败", buildChatHistory(history, userInput));
                }
                
                // 3. 解析响应
                ReActTurn currentTurn = reActParser.parse(llmOutput, turnNumber);
                history.add(currentTurn);
                
                logger.info("📝 第{}轮解析结果: {}", turnNumber, currentTurn.getSummary());
                
                // 4. 检查解析错误
                if (currentTurn.isError()) {
                    logger.error("❌ 第{}轮解析失败: {}", turnNumber, currentTurn.getErrorMessage());
                    return AiResponse.error("AI响应解析失败: " + currentTurn.getErrorMessage(), 
                        buildChatHistory(history, userInput));
                }
                
                // 5. 判断是否结束
                if (currentTurn.isFinalAnswer()) {
                    logger.info("🎉 ReAct智能体完成任务，共{}轮", turnNumber);
                    AiResponse response = AiResponse.text(currentTurn.getFinalAnswer(), buildChatHistory(history, userInput));
                    // 添加ReAct执行历史到响应中
                    response.setReActHistory(history);
                    return response;
                }
                
                // 6. 执行行动
                if (currentTurn.hasAction()) {
                    if (!executeAction(currentTurn, userRoles)) {
                        // 行动执行失败，但继续循环让AI处理错误
                        logger.warn("⚠️ 第{}轮行动执行失败，继续循环", turnNumber);
                    }
                } else {
                    // AI没有给出行动也没有最终答案
                    logger.warn("⚠️ 第{}轮AI未给出有效行动或最终答案", turnNumber);
                    currentTurn.setObservation("错误：你必须给出一个具体的行动或最终答案");
                }
                
                // 7. 检查循环检测
                if (detectInfiniteLoop(history)) {
                    logger.warn("🔄 检测到可能的无限循环，终止执行");
                    return AiResponse.error("检测到循环执行模式，任务已终止", buildChatHistory(history, userInput));
                }
            }
            
            // 达到最大循环次数
            logger.warn("🔄 达到最大循环次数({})，强制终止", MAX_TURNS);
            AiResponse response = AiResponse.error("任务过于复杂，已超出最大执行步骤限制", buildChatHistory(history, userInput));
            response.setReActHistory(history);
            return response;
            
        } catch (Exception e) {
            logger.error("❌ ReAct智能体执行异常", e);
            AiResponse response = AiResponse.error("智能体执行异常: " + e.getMessage(), buildChatHistory(history, userInput));
            response.setReActHistory(history);
            return response;
        }
    }
    
    /**
     * 调用AI服务
     */
    private String callAI(String prompt) {
        try {
            logger.debug("🤖 调用AI服务，Prompt长度: {}", prompt.length());

            AiClientHelper.AiCallConfig config = new AiClientHelper.AiCallConfig()
                .temperature(0.1)
                .maxTokens(1500)
                .systemPrompt("你是一个专业的AI智能体，严格按照ReAct格式输出。必须包含Thought和Action或Final Answer。")
                .userPrompt(prompt);

            String response = aiClientHelper.callForGeneral(config, "ReAct智能体推理");
            logger.debug("🤖 AI响应长度: {}", response != null ? response.length() : 0);
            logger.debug("🤖 AI响应内容: {}", response);

            return response;
        } catch (Exception e) {
            logger.error("AI服务调用失败: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 执行行动
     */
    private boolean executeAction(ReActTurn turn, Set<String> userRoles) {
        try {
            // 安全检查
            if (!isActionSafe(turn.getAction(), userRoles)) {
                turn.setObservation("错误：该操作被安全策略拒绝，请选择其他方式或确认权限");
                return false;
            }
            
            // 执行函数
            DynamicFunctionExecutor.ExecutionResult result = functionExecutor.execute(turn.getAction(), userRoles);
            
            if (result.isSuccess()) {
                String observation = functionExecutor.formatResultForAI(result.getResult());
                turn.setObservation("执行成功：" + observation);
                logger.debug("✅ 行动执行成功: {}", turn.getAction().getFunctionName());
                return true;
            } else {
                turn.setObservation("执行失败：" + result.getErrorMessage());
                logger.warn("❌ 行动执行失败: {}", result.getErrorMessage());
                return false;
            }
            
        } catch (Exception e) {
            logger.error("行动执行异常: {}", e.getMessage(), e);
            turn.setObservation("执行异常：" + e.getMessage());
            return false;
        } finally {
            turn.markCompleted();
        }
    }
    
    /**
     * 安全检查
     */
    private boolean isActionSafe(com.javaxiaobear.base.ai.dto.FunctionCallAnalysis action, Set<String> userRoles) {
        if (action == null) {
            return false;
        }
        
        AiFunctionWrapper function = functionRegistry.getFunctionWrapper(action.getFunctionName());
        if (function == null) {
            logger.warn("尝试调用不存在的函数: {}", action.getFunctionName());
            return false;
        }
        
        // 权限检查
        Set<String> requiredRoles = function.getRequiredRoles();
        if (requiredRoles != null && !requiredRoles.isEmpty()) {
            boolean hasPermission = false;
            
            // 检查管理员权限
            try {
                if (SecurityUtils.isAdmin(SecurityUtils.getUserId())) {
                    hasPermission = true;
                }
            } catch (Exception e) {
                // 忽略异常
            }
            
            // 检查具体权限
            if (!hasPermission) {
                for (String requiredRole : requiredRoles) {
                    if (userRoles.contains(requiredRole)) {
                        hasPermission = true;
                        break;
                    }
                }
            }
            
            if (!hasPermission) {
                logger.warn("权限不足: 函数={}, 需要={}, 拥有={}", 
                    function.getFunctionName(), requiredRoles, userRoles);
                return false;
            }
        }
        
        // 危险操作检查
        if (function.isDangerous()) {
            logger.warn("⚠️ 智能体尝试执行危险操作: {}", function.getFunctionName());
            // 在ReAct中间步骤，我们可以允许危险操作，但会记录警告
            // 实际的危险操作控制应该在业务层面处理
        }
        
        return true;
    }
    
    /**
     * 检测无限循环
     */
    private boolean detectInfiniteLoop(List<ReActTurn> history) {
        if (history.size() < 4) {
            return false;
        }
        
        // 检查最近的行动是否重复
        Map<String, Integer> recentActions = new HashMap<>();
        int recentTurns = Math.min(6, history.size());
        
        for (int i = history.size() - recentTurns; i < history.size(); i++) {
            ReActTurn turn = history.get(i);
            if (turn.hasAction()) {
                String actionKey = turn.getAction().getFunctionName();
                recentActions.put(actionKey, recentActions.getOrDefault(actionKey, 0) + 1);
                
                if (recentActions.get(actionKey) >= MAX_SAME_ACTION_RETRIES) {
                    logger.warn("检测到重复行动: {} 出现 {} 次", actionKey, recentActions.get(actionKey));
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * 获取用户权限
     */
    private Set<String> getUserRoles() {
        try {
            return SecurityUtils.getLoginUser().getPermissions();
        } catch (Exception e) {
            // 匿名访问时的临时处理
            logger.info("匿名访问ReAct智能体，使用默认权限");
            Set<String> defaultRoles = new HashSet<>();
            defaultRoles.add("system:user:list");
            defaultRoles.add("system:user:query");
            defaultRoles.add("system:role:list");
            defaultRoles.add("system:dept:list");
            return defaultRoles;
        }
    }
    
    /**
     * 构建聊天历史
     */
    private List<ChatMessage> buildChatHistory(List<ReActTurn> history, String userInput) {
        List<ChatMessage> messages = new ArrayList<>();
        
        // 添加用户消息
        messages.add(new ChatMessage(ChatMessage.MessageRole.USER, userInput));
        
        // 添加AI的思考过程（可选，用于调试）
        if (!history.isEmpty()) {
            StringBuilder processLog = new StringBuilder();
            processLog.append("🤖 智能体执行过程：\n\n");
            
            for (ReActTurn turn : history) {
                processLog.append(String.format("**第%d轮**\n", turn.getTurnNumber()));
                if (turn.hasThought()) {
                    processLog.append("💭 思考：").append(turn.getThought()).append("\n");
                }
                if (turn.hasAction()) {
                    processLog.append("🎯 行动：").append(turn.getAction().getFunctionName()).append("\n");
                }
                if (turn.hasObservation()) {
                    processLog.append("👀 观察：").append(turn.getObservation()).append("\n");
                }
                processLog.append("\n");
            }
            
            messages.add(new ChatMessage(ChatMessage.MessageRole.ASSISTANT, processLog.toString()));
        }
        
        return messages;
    }
}
