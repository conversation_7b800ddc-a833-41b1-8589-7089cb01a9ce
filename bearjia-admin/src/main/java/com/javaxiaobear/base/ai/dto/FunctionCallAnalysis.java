package com.javaxiaobear.base.ai.dto;

import com.javaxiaobear.base.ai.model.OperationStep;
import java.util.List;
import java.util.Map;

/**
 * AI函数调用分析结果
 * 
 * 用于封装AI对用户意图的分析结果，包括是否需要调用函数、
 * 调用哪个函数、提取的参数等信息。
 * 
 * <AUTHOR>
 */
public class FunctionCallAnalysis {

    /** 是否应该调用函数 */
    private boolean shouldCall;
    
    /** 要调用的函数名 */
    private String functionName;
    
    /** AI对分析结果的信心程度 (0.0-1.0) */
    private double confidence;
    
    /** 从用户消息中提取的参数 */
    private Map<String, Object> extractedParams;
    
    /** AI的分析理由 */
    private String reasoning;

    /** 是否为多步操作 */
    private boolean isMultiStep;

    /** 多步操作的步骤列表 */
    private List<OperationStep> steps;

    public FunctionCallAnalysis() {}

    public FunctionCallAnalysis(boolean shouldCall, String functionName, double confidence,
                               Map<String, Object> extractedParams, String reasoning) {
        this.shouldCall = shouldCall;
        this.functionName = functionName;
        this.confidence = confidence;
        this.extractedParams = extractedParams;
        this.reasoning = reasoning;
        this.isMultiStep = false;
    }

    public FunctionCallAnalysis(boolean shouldCall, String functionName, double confidence,
                               Map<String, Object> extractedParams, String reasoning,
                               boolean isMultiStep, List<OperationStep> steps) {
        this.shouldCall = shouldCall;
        this.functionName = functionName;
        this.confidence = confidence;
        this.extractedParams = extractedParams;
        this.reasoning = reasoning;
        this.isMultiStep = isMultiStep;
        this.steps = steps;
    }

    /**
     * 判断是否应该调用函数
     */
    public boolean shouldCallFunction() {
        return shouldCall && confidence > 0.5; // 信心度大于0.5才执行
    }

    // Getters and Setters
    public boolean isShouldCall() {
        return shouldCall;
    }

    public void setShouldCall(boolean shouldCall) {
        this.shouldCall = shouldCall;
    }

    public String getFunctionName() {
        return functionName;
    }

    public void setFunctionName(String functionName) {
        this.functionName = functionName;
    }

    public double getConfidence() {
        return confidence;
    }

    public void setConfidence(double confidence) {
        this.confidence = confidence;
    }

    public Map<String, Object> getExtractedParams() {
        return extractedParams;
    }

    public void setExtractedParams(Map<String, Object> extractedParams) {
        this.extractedParams = extractedParams;
    }

    public String getReasoning() {
        return reasoning;
    }

    public void setReasoning(String reasoning) {
        this.reasoning = reasoning;
    }

    public boolean isMultiStep() {
        return isMultiStep;
    }

    public void setMultiStep(boolean multiStep) {
        isMultiStep = multiStep;
    }

    public List<OperationStep> getSteps() {
        return steps;
    }

    public void setSteps(List<OperationStep> steps) {
        this.steps = steps;
    }

    @Override
    public String toString() {
        return "FunctionCallAnalysis{" +
                "shouldCall=" + shouldCall +
                ", functionName='" + functionName + '\'' +
                ", confidence=" + confidence +
                ", extractedParams=" + extractedParams +
                ", reasoning='" + reasoning + '\'' +
                ", isMultiStep=" + isMultiStep +
                ", steps=" + steps +
                '}';
    }
}
