package com.javaxiaobear.base.ai.registry;

import com.javaxiaobear.base.ai.annotation.AiFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Method;
import java.util.Set;
import java.util.HashSet;
import java.util.Arrays;

/**
 * AI 函数 Bean 后置处理器
 * 
 * 在 Spring 容器初始化 Bean 时，自动扫描所有被 @AiFunction 注解标记的方法，
 * 并将其注册到函数注册中心。这是实现自动发现和注册的核心组件。
 * 
 * <AUTHOR>
 */
@Component
public class AiFunctionBeanPostProcessor implements BeanPostProcessor {

    private static final Logger logger = LoggerFactory.getLogger(AiFunctionBeanPostProcessor.class);

    private final DynamicFunctionRegistry registry;

    public AiFunctionBeanPostProcessor(DynamicFunctionRegistry registry) {
        this.registry = registry;
    }

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        Class<?> beanClass = bean.getClass();
        
        // 扫描所有方法，查找 @AiFunction 注解
        ReflectionUtils.doWithMethods(beanClass, method -> {
            AiFunction aiFunctionAnnotation = AnnotationUtils.findAnnotation(method, AiFunction.class);
            if (aiFunctionAnnotation != null) {
                try {
                    registerFunction(bean, method, aiFunctionAnnotation);
                } catch (Exception e) {
                    logger.error("注册AI函数失败: {}.{}", beanClass.getSimpleName(), method.getName(), e);
                    throw new RuntimeException("AI函数注册失败", e);
                }
            }
        });
        
        return bean;
    }

    /**
     * 注册单个函数
     * 
     * @param bean Bean实例
     * @param method 方法对象
     * @param annotation @AiFunction注解
     */
    private void registerFunction(Object bean, Method method, AiFunction annotation) {
        String functionName = method.getName();
        
        // 验证方法签名
        validateMethodSignature(method, functionName);
        
        // 获取参数和返回值类型
        Class<?> inputType = method.getParameterTypes()[0];
        Class<?> outputType = method.getReturnType();

        // 如果是基本类型，创建包装类型
        if (isPrimitiveOrWrapper(inputType)) {
            inputType = createWrapperType(inputType, method.getParameters()[0].getName());
        }
        
        // 处理角色权限
        Set<String> requiredRoles = new HashSet<>(Arrays.asList(annotation.requiredRoles()));
        
        // 创建函数包装器
        AiFunctionWrapper wrapper = new AiFunctionWrapper(
            functionName,
            annotation.description(),
            annotation.dangerous(),
            requiredRoles,
            annotation.category(),
            annotation.priority(),
            bean,
            method,
            inputType,
            outputType
        );
        
        // 注册到注册中心
        registry.register(functionName, wrapper);
    }

    /**
     * 验证方法签名是否符合要求
     * 
     * @param method 方法对象
     * @param functionName 函数名称
     */
    private void validateMethodSignature(Method method, String functionName) {
        // 检查参数数量
        if (method.getParameterCount() != 1) {
            throw new IllegalArgumentException(
                String.format("AI函数 %s 的参数数量必须为1，当前为%d。请将所有参数封装到一个请求对象中。", 
                    functionName, method.getParameterCount())
            );
        }
        
        // 检查返回值类型
        if (method.getReturnType() == void.class || method.getReturnType() == Void.class) {
            throw new IllegalArgumentException(
                String.format("AI函数 %s 必须有返回值，不能为void。", functionName)
            );
        }
        
        // 检查参数类型
        Class<?> parameterType = method.getParameterTypes()[0];
        if (parameterType.isPrimitive()) {
            throw new IllegalArgumentException(
                String.format("AI函数 %s 的参数不能是基本类型，请使用包装类型或自定义对象。", functionName)
            );
        }
        
        logger.debug("AI函数签名验证通过: {} - 参数类型: {}, 返回类型: {}",
            functionName, parameterType.getSimpleName(), method.getReturnType().getSimpleName());
    }

    /**
     * 判断是否为基本类型或包装类型
     */
    private boolean isPrimitiveOrWrapper(Class<?> type) {
        return type.isPrimitive() ||
               type == Long.class || type == Integer.class || type == String.class ||
               type == Boolean.class || type == Double.class || type == Float.class;
    }

    /**
     * 为基本类型创建动态包装类型
     */
    private Class<?> createWrapperType(Class<?> primitiveType, String paramName) {
        // 对于基本类型，我们使用一个通用的包装策略
        // 这里简化处理，直接返回Map类型，让AI框架处理JSON到Map的转换
        return java.util.Map.class;
    }
}
