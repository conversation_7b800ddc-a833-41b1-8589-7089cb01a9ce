package com.javaxiaobear.base.ai.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 执行确认请求数据传输对象
 * 
 * 用于接收前端发送的函数执行确认请求，当用户确认执行危险操作时使用。
 * 
 * <AUTHOR>
 */
public class ExecuteRequest {

    /** 要执行的函数名称 */
    @NotBlank(message = "函数名称不能为空")
    private String functionName;
    
    /** 函数参数 */
    @NotNull(message = "函数参数不能为空")
    private Object arguments;
    
    /** 对话历史记录 */
    private List<ChatMessage> conversationHistory;
    
    /** 会话ID（可选） */
    private String sessionId;
    
    /** 用户ID（从安全上下文中获取） */
    private Long userId;
    
    /** 确认标识（用于防止重复提交） */
    private String confirmationToken;

    // 构造方法
    public ExecuteRequest() {}

    public ExecuteRequest(String functionName, Object arguments) {
        this.functionName = functionName;
        this.arguments = arguments;
    }

    public ExecuteRequest(String functionName, Object arguments, List<ChatMessage> conversationHistory) {
        this.functionName = functionName;
        this.arguments = arguments;
        this.conversationHistory = conversationHistory;
    }

    // Getters and Setters
    public String getFunctionName() {
        return functionName;
    }

    public void setFunctionName(String functionName) {
        this.functionName = functionName;
    }

    public Object getArguments() {
        return arguments;
    }

    public void setArguments(Object arguments) {
        this.arguments = arguments;
    }

    public List<ChatMessage> getConversationHistory() {
        return conversationHistory;
    }

    public void setConversationHistory(List<ChatMessage> conversationHistory) {
        this.conversationHistory = conversationHistory;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getConfirmationToken() {
        return confirmationToken;
    }

    public void setConfirmationToken(String confirmationToken) {
        this.confirmationToken = confirmationToken;
    }

    @Override
    public String toString() {
        return "ExecuteRequest{" +
                "functionName='" + functionName + '\'' +
                ", arguments=" + arguments +
                ", conversationHistorySize=" + (conversationHistory != null ? conversationHistory.size() : 0) +
                ", sessionId='" + sessionId + '\'' +
                ", userId=" + userId +
                ", confirmationToken='" + confirmationToken + '\'' +
                '}';
    }
}
