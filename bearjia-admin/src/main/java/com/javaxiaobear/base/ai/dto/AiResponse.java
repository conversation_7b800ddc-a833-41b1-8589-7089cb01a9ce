package com.javaxiaobear.base.ai.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;

/**
 * AI 响应统一数据传输对象
 * 
 * 用于封装 AI Agent 的所有响应类型，包括普通文本回复、确认请求和最终结果。
 * 前端可以根据响应类型进行不同的处理逻辑。
 * 
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AiResponse {

    /** 响应类型 */
    private ResponseType type;
    
    /** 响应内容 */
    private String content;
    
    /** 确认请求（仅当type为CONFIRMATION_REQUIRED时有值） */
    private ConfirmationRequest confirmationRequest;
    
    /** 对话历史记录 */
    private List<ChatMessage> conversationHistory;

    /** ReAct执行历史 */
    private List<com.javaxiaobear.base.ai.model.ReActTurn> reActHistory;
    
    /** 错误信息（如果有） */
    private String errorMessage;
    
    /** 是否成功 */
    private boolean success;

    /**
     * 响应类型枚举
     */
    public enum ResponseType {
        /** 普通文本回复 */
        TEXT,
        /** 需要用户确认的操作 */
        CONFIRMATION_REQUIRED,
        /** 最终执行结果 */
        FINAL_RESULT,
        /** 错误响应 */
        ERROR
    }

    /**
     * 确认请求数据结构
     */
    public static class ConfirmationRequest {
        /** 函数名称 */
        private String functionName;
        
        /** 函数参数 */
        private Object arguments;
        
        /** 确认消息 */
        private String confirmationMessage;
        
        /** 函数描述 */
        private String functionDescription;

        public ConfirmationRequest() {}

        public ConfirmationRequest(String functionName, Object arguments, String confirmationMessage, String functionDescription) {
            this.functionName = functionName;
            this.arguments = arguments;
            this.confirmationMessage = confirmationMessage;
            this.functionDescription = functionDescription;
        }

        // Getters and Setters
        public String getFunctionName() { return functionName; }
        public void setFunctionName(String functionName) { this.functionName = functionName; }
        
        public Object getArguments() { return arguments; }
        public void setArguments(Object arguments) { this.arguments = arguments; }
        
        public String getConfirmationMessage() { return confirmationMessage; }
        public void setConfirmationMessage(String confirmationMessage) { this.confirmationMessage = confirmationMessage; }
        
        public String getFunctionDescription() { return functionDescription; }
        public void setFunctionDescription(String functionDescription) { this.functionDescription = functionDescription; }
    }

    // 构造方法
    public AiResponse() {}

    public AiResponse(ResponseType type, String content, List<ChatMessage> conversationHistory) {
        this.type = type;
        this.content = content;
        this.conversationHistory = conversationHistory;
        this.success = true;
    }

    // 静态工厂方法
    public static AiResponse text(String content, List<ChatMessage> history) {
        return new AiResponse(ResponseType.TEXT, content, history);
    }

    public static AiResponse confirmationRequired(ConfirmationRequest request, List<ChatMessage> history) {
        AiResponse response = new AiResponse();
        response.type = ResponseType.CONFIRMATION_REQUIRED;
        response.confirmationRequest = request;
        response.conversationHistory = history;
        response.success = true;
        return response;
    }

    public static AiResponse finalResult(String content, List<ChatMessage> history) {
        return new AiResponse(ResponseType.FINAL_RESULT, content, history);
    }

    public static AiResponse error(String errorMessage, List<ChatMessage> history) {
        AiResponse response = new AiResponse();
        response.type = ResponseType.ERROR;
        response.errorMessage = errorMessage;
        response.conversationHistory = history;
        response.success = false;
        return response;
    }

    // Getters and Setters
    public ResponseType getType() { return type; }
    public void setType(ResponseType type) { this.type = type; }
    
    public String getContent() { return content; }
    public void setContent(String content) { this.content = content; }
    
    public ConfirmationRequest getConfirmationRequest() { return confirmationRequest; }
    public void setConfirmationRequest(ConfirmationRequest confirmationRequest) { this.confirmationRequest = confirmationRequest; }
    
    public List<ChatMessage> getConversationHistory() { return conversationHistory; }
    public void setConversationHistory(List<ChatMessage> conversationHistory) { this.conversationHistory = conversationHistory; }
    
    public String getErrorMessage() { return errorMessage; }
    public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    
    public boolean isSuccess() { return success; }
    public void setSuccess(boolean success) { this.success = success; }

    public List<com.javaxiaobear.base.ai.model.ReActTurn> getReActHistory() {
        return reActHistory;
    }

    public void setReActHistory(List<com.javaxiaobear.base.ai.model.ReActTurn> reActHistory) {
        this.reActHistory = reActHistory;
    }
}
