package com.javaxiaobear.base.ai.controller;

import com.javaxiaobear.base.ai.dto.AiResponse;
import com.javaxiaobear.base.ai.dto.ChatRequest;
import com.javaxiaobear.base.ai.dto.ExecuteRequest;
import com.javaxiaobear.base.ai.registry.AiFunctionWrapper;
import com.javaxiaobear.base.ai.registry.DynamicFunctionRegistry;
import com.javaxiaobear.base.ai.service.AiOrchestrationService;
import com.javaxiaobear.base.common.utils.SecurityUtils;
import com.javaxiaobear.base.framework.aspectj.lang.annotation.RateLimiter;
import com.javaxiaobear.base.framework.aspectj.lang.enums.LimitType;
import com.javaxiaobear.base.framework.security.LoginUser;
import com.javaxiaobear.base.framework.web.domain.AjaxResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.Serializable;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * AI Agent 控制器
 * 
 * 提供 AI 聊天和函数执行的 REST API 接口。
 * 所有接口都需要用户登录认证，部分接口可能需要特定权限。
 * 
 * <AUTHOR>
 */
@Tag(name = "AI智能助手", description = "AI聊天和函数执行接口")
@RestController
@RequestMapping("/ai")
public class AiController {

    private static final Logger logger = LoggerFactory.getLogger(AiController.class);

    private final AiOrchestrationService aiOrchestrationService;
    private final DynamicFunctionRegistry functionRegistry;

    public AiController(AiOrchestrationService aiOrchestrationService, 
                       DynamicFunctionRegistry functionRegistry) {
        this.aiOrchestrationService = aiOrchestrationService;
        this.functionRegistry = functionRegistry;
    }

    /**
     * AI 聊天接口
     * 
     * @param request 聊天请求
     * @return AI 响应
     */
    @Operation(summary = "AI聊天", description = "与AI助手进行对话交流")
    @PostMapping("/chat")
    @RateLimiter(
        key = "ai_chat",
        count = 20,
        time = 60,
        limitType = LimitType.IP,
        message = "AI聊天请求过于频繁，请稍后再试"
    )
    // @PreAuthorize("@ss.hasPermi('ai:chat:use')") // 临时注释用于测试
    public AjaxResult chat(@Validated @RequestBody ChatRequest request) {
        try {
            logger.info("收到AI聊天请求: {}", request);
            
            AiResponse response = aiOrchestrationService.processChat(request);
            
            logger.info("AI聊天响应: {}", response.getType());
            return AjaxResult.success(response);
            
        } catch (Exception e) {
            logger.error("AI聊天处理失败", e);
            return AjaxResult.error("AI聊天服务暂时不可用，请稍后重试");
        }
    }

    /**
     * AI 流式聊天接口
     *
     * @param request 聊天请求
     * @return 流式响应
     */
    @Operation(summary = "AI流式聊天", description = "与AI助手进行流式对话交流，实时返回处理进度")
    @PostMapping(value = "/chat/stream", produces = "text/event-stream")
    @RateLimiter(
        key = "ai_chat_stream",
        count = 10,
        time = 60,
        limitType = LimitType.IP,
        message = "AI流式聊天请求过于频繁，请稍后再试"
    )
    // @PreAuthorize("@ss.hasPermi('ai:chat:use')") // 临时注释用于测试
    public SseEmitter chatStream(@Validated @RequestBody ChatRequest request) {
        try {
            logger.info("收到AI流式聊天请求: {}", request);

            return aiOrchestrationService.processChatStream(request);

        } catch (Exception e) {
            logger.error("AI流式聊天处理失败", e);
            SseEmitter emitter = new SseEmitter();
            try {
                emitter.send(SseEmitter.event()
                    .name("error")
                    .data("AI聊天服务暂时不可用，请稍后重试"));
                emitter.complete();
            } catch (Exception sendException) {
                logger.error("发送错误消息失败", sendException);
            }
            return emitter;
        }
    }

    /**
     * 执行确认操作接口
     * 
     * @param request 执行请求
     * @return 执行结果
     */
    @Operation(summary = "执行确认操作", description = "执行AI函数调用的确认操作")
    @PostMapping("/execute")
    @PreAuthorize("@ss.hasPermi('ai:function:execute')")
    public AjaxResult execute(@Validated @RequestBody ExecuteRequest request) {
        try {
            logger.info("收到AI函数执行请求: {}", request);
            
            AiResponse response = aiOrchestrationService.processExecution(request);
            
            logger.info("AI函数执行完成: {}", request.getFunctionName());
            return AjaxResult.success(response);
            
        } catch (Exception e) {
            logger.error("AI函数执行失败: {}", request.getFunctionName(), e);
            return AjaxResult.error("操作执行失败: " + e.getMessage());
        }
    }

    /**
     * 获取可用函数列表
     * 
     * @return 当前用户可用的函数列表
     */
    @Operation(summary = "获取可用函数列表", description = "获取当前用户可用的AI函数列表")
    @GetMapping("/functions")
    // @PreAuthorize("@ss.hasPermi('ai:function:list')") // 临时注释用于调试
    public AjaxResult getFunctions() {
        try {
            // 获取当前用户信息（临时处理匿名访问）
            LoginUser loginUser = null;
            Set<String> userRoles = new HashSet<>();
            try {
                loginUser = SecurityUtils.getLoginUser();
                userRoles = loginUser.getPermissions();
            } catch (Exception e) {
                // 匿名访问时的临时处理
                logger.info("匿名访问AI函数列表接口，跳过权限检查");
                // 为测试目的，给匿名用户一些基本权限
                userRoles = new HashSet<>();
                userRoles.add("system:user:add");    // 创建用户权限
                userRoles.add("system:user:query");  // 查询用户权限
                userRoles.add("system:user:list");   // 列表用户权限
                userRoles.add("system:user:edit");   // 编辑用户权限
                logger.info("为匿名用户临时分配权限: {}", userRoles);
            }
            
            List<AiFunctionWrapper> availableFunctions = functionRegistry.getAvailableFunctions(userRoles);
            
            // 转换为前端需要的格式
            List<Map<String, ? extends Serializable>> functionList = availableFunctions.stream()
                .map(wrapper -> {
                    Map<String, Serializable> map = new HashMap<>();
                    map.put("name", wrapper.getFunctionName());
                    map.put("description", wrapper.getDescription());
                    map.put("category", wrapper.getCategory());
                    map.put("dangerous", wrapper.isDangerous());
                    map.put("priority", wrapper.getPriority());
                    return map;
                })
                .collect(Collectors.toList());
            
            return AjaxResult.success(functionList);
            
        } catch (Exception e) {
            logger.error("获取函数列表失败", e);
            return AjaxResult.error("获取函数列表失败");
        }
    }

    /**
     * 获取函数统计信息
     * 
     * @return 函数统计信息
     */
    @Operation(summary = "获取函数统计信息", description = "获取AI函数的统计和使用情况")
    @GetMapping("/functions/statistics")
    @PreAuthorize("@ss.hasPermi('ai:function:statistics')")
    public AjaxResult getFunctionStatistics() {
        try {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            Set<String> userRoles = loginUser.getPermissions();
            
            List<AiFunctionWrapper> allFunctions = functionRegistry.getAllFunctionWrappers()
                .stream().collect(Collectors.toList());
            
            List<AiFunctionWrapper> availableFunctions = functionRegistry.getAvailableFunctions(userRoles);
            
            long dangerousFunctions = availableFunctions.stream()
                .mapToLong(wrapper -> wrapper.isDangerous() ? 1 : 0)
                .sum();
            
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalFunctions", allFunctions.size());
            statistics.put("availableFunctions", availableFunctions.size());
            statistics.put("dangerousFunctions", dangerousFunctions);
            statistics.put("categories", availableFunctions.stream()
                .collect(Collectors.groupingBy(
                    AiFunctionWrapper::getCategory,
                    Collectors.counting()
                )));
            
            return AjaxResult.success(statistics);
            
        } catch (Exception e) {
            logger.error("获取函数统计信息失败", e);
            return AjaxResult.error("获取统计信息失败");
        }
    }

    /**
     * 健康检查接口
     * 
     * @return 服务状态
     */
    @Operation(summary = "AI服务健康检查", description = "检查AI服务和函数注册状态")
    @GetMapping("/health")
    public AjaxResult health() {
        try {
            String statistics = functionRegistry.getStatistics();
            
            Map<String, Object> healthInfo = new HashMap<>();
            healthInfo.put("status", "UP");
            healthInfo.put("timestamp", System.currentTimeMillis());
            healthInfo.put("statistics", statistics);
            
            return AjaxResult.success(healthInfo);
            
        } catch (Exception e) {
            logger.error("AI服务健康检查失败", e);
            return AjaxResult.error("AI服务异常");
        }
    }

    /**
     * 测试ReAct智能体
     */
    @Operation(summary = "测试ReAct智能体", description = "测试ReAct智能体的执行过程")
    @PostMapping("/test-react")
    public AjaxResult testReAct(@RequestBody ChatRequest request) {
        try {
            logger.info("🧪 测试ReAct智能体: {}", request.getMessage());

            AiResponse response = aiOrchestrationService.processChat(request);

            logger.info("🧪 ReAct测试响应: success={}, message={}, hasHistory={}",
                response.isSuccess(),
                response.getContent(),
                response.getReActHistory() != null ? response.getReActHistory().size() : 0);

            return AjaxResult.success(response);
        } catch (Exception e) {
            logger.error("ReAct测试失败", e);
            return AjaxResult.error("ReAct测试失败: " + e.getMessage());
        }
    }
}
