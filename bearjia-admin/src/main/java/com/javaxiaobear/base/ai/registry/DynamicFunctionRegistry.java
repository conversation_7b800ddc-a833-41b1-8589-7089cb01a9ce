package com.javaxiaobear.base.ai.registry;

import com.javaxiaobear.base.common.utils.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 动态函数注册中心
 * 
 * 负责管理所有被 @AiFunction 注解标记的函数，提供注册、查询、调用等功能。
 * 这是整个 AI Agent 框架的核心组件之一。
 * 
 * <AUTHOR>
 */
@Service
public class DynamicFunctionRegistry {

    private static final Logger logger = LoggerFactory.getLogger(DynamicFunctionRegistry.class);

    /** 函数映射表，key为函数名，value为函数包装器 */
    private final Map<String, AiFunctionWrapper> functionMap = new ConcurrentHashMap<>();

    /**
     * 注册 AI 函数
     * 
     * @param functionName 函数名称
     * @param wrapper 函数包装器
     */
    public void register(String functionName, AiFunctionWrapper wrapper) {
        if (functionMap.containsKey(functionName)) {
            logger.warn("AI函数重复注册，将覆盖原有函数: {}", functionName);
        }
        
        functionMap.put(functionName, wrapper);
        logger.info("AI函数注册成功: {} | 描述: {} | 危险: {} | 权限: {} | 分类: {} | 优先级: {}", 
                functionName, wrapper.getDescription(), wrapper.isDangerous(), 
                wrapper.getRequiredRoles(), wrapper.getCategory(), wrapper.getPriority());
    }

    /**
     * 获取函数包装器
     * 
     * @param functionName 函数名称
     * @return 函数包装器，如果不存在则返回null
     */
    public AiFunctionWrapper getFunctionWrapper(String functionName) {
        return functionMap.get(functionName);
    }

    /**
     * 检查函数是否存在
     * 
     * @param functionName 函数名称
     * @return 是否存在
     */
    public boolean containsFunction(String functionName) {
        return functionMap.containsKey(functionName);
    }

    /**
     * 获取所有函数包装器
     * 
     * @return 所有函数包装器集合
     */
    public Collection<AiFunctionWrapper> getAllFunctionWrappers() {
        return functionMap.values();
    }

    /**
     * 根据分类获取函数列表
     * 
     * @param category 分类名称
     * @return 指定分类的函数列表
     */
    public List<AiFunctionWrapper> getFunctionsByCategory(String category) {
        return functionMap.values().stream()
                .filter(wrapper -> category.equals(wrapper.getCategory()))
                .collect(Collectors.toList());
    }

    /**
     * 根据用户角色获取可用函数列表
     * 
     * @param userRoles 用户角色集合
     * @return 用户可访问的函数列表
     */
    public List<AiFunctionWrapper> getAvailableFunctions(Collection<String> userRoles) {
        return functionMap.values().stream()
                .filter(wrapper -> {
                    // 如果函数没有角色要求，则所有用户都可以访问
                    if (wrapper.getRequiredRoles().isEmpty()) {
                        return true;
                    }
                    // 检查用户是否拥有所需角色
                    try {
                        if (userRoles.stream().anyMatch(role -> role.equals("*:*:*"))) {
                            return true;
                        }
                    } catch (Exception e) {
                        // 匿名访问时，忽略管理员检查
                    }
                    return userRoles.stream().anyMatch(wrapper.getRequiredRoles()::contains);
                })
                .sorted((a, b) -> Integer.compare(b.getPriority(), a.getPriority())) // 按优先级降序排列
                .collect(Collectors.toList());
    }

    /**
     * 获取函数统计信息
     * 
     * @return 统计信息字符串
     */
    public String getStatistics() {
        long totalFunctions = functionMap.size();
        long dangerousFunctions = functionMap.values().stream()
                .mapToLong(wrapper -> wrapper.isDangerous() ? 1 : 0)
                .sum();
        long functionsWithRoles = functionMap.values().stream()
                .mapToLong(wrapper -> wrapper.getRequiredRoles().isEmpty() ? 0 : 1)
                .sum();

        return String.format("AI函数统计 - 总数: %d, 危险函数: %d, 需要权限: %d", 
                totalFunctions, dangerousFunctions, functionsWithRoles);
    }

    /**
     * 清空所有注册的函数（主要用于测试）
     */
    public void clear() {
        functionMap.clear();
        logger.info("已清空所有AI函数注册");
    }

    /**
     * 获取所有函数名称列表
     * 
     * @return 函数名称列表
     */
    public List<String> getAllFunctionNames() {
        return functionMap.keySet().stream()
                .sorted()
                .collect(Collectors.toList());
    }
}
