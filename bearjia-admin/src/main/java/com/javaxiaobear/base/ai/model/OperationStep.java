package com.javaxiaobear.base.ai.model;

import java.util.Map;

/**
 * 多步操作中的单个步骤
 * 
 * <AUTHOR>
 */
public class OperationStep {
    
    /**
     * 函数名称
     */
    private String functionName;
    
    /**
     * 步骤目的说明
     */
    private String purpose;
    
    /**
     * 提取的参数
     */
    private Map<String, Object> extractedParams;
    
    /**
     * 步骤执行结果
     */
    private Object result;
    
    /**
     * 是否依赖前一步的结果
     */
    private boolean dependsOnPreviousStep;
    
    /**
     * 依赖的参数映射（如：userId -> ${step1.result[0].userId}）
     */
    private Map<String, String> parameterMappings;

    /**
     * 条件执行支持
     */
    private String condition; // 执行条件，如 "if_not_exists", "if_exists"
    private String conditionField; // 条件字段，如 "roleName"
    private Object conditionValue; // 条件值
    
    public OperationStep() {}
    
    public OperationStep(String functionName, String purpose, Map<String, Object> extractedParams) {
        this.functionName = functionName;
        this.purpose = purpose;
        this.extractedParams = extractedParams;
    }
    
    // Getters and Setters
    public String getFunctionName() {
        return functionName;
    }
    
    public void setFunctionName(String functionName) {
        this.functionName = functionName;
    }
    
    public String getPurpose() {
        return purpose;
    }
    
    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }
    
    public Map<String, Object> getExtractedParams() {
        return extractedParams;
    }
    
    public void setExtractedParams(Map<String, Object> extractedParams) {
        this.extractedParams = extractedParams;
    }
    
    public Object getResult() {
        return result;
    }
    
    public void setResult(Object result) {
        this.result = result;
    }
    
    public boolean isDependsOnPreviousStep() {
        return dependsOnPreviousStep;
    }
    
    public void setDependsOnPreviousStep(boolean dependsOnPreviousStep) {
        this.dependsOnPreviousStep = dependsOnPreviousStep;
    }
    
    public Map<String, String> getParameterMappings() {
        return parameterMappings;
    }
    
    public void setParameterMappings(Map<String, String> parameterMappings) {
        this.parameterMappings = parameterMappings;
    }

    public String getCondition() {
        return condition;
    }

    public void setCondition(String condition) {
        this.condition = condition;
    }

    public String getConditionField() {
        return conditionField;
    }

    public void setConditionField(String conditionField) {
        this.conditionField = conditionField;
    }

    public Object getConditionValue() {
        return conditionValue;
    }

    public void setConditionValue(Object conditionValue) {
        this.conditionValue = conditionValue;
    }

    @Override
    public String toString() {
        return "OperationStep{" +
                "functionName='" + functionName + '\'' +
                ", purpose='" + purpose + '\'' +
                ", extractedParams=" + extractedParams +
                ", dependsOnPreviousStep=" + dependsOnPreviousStep +
                '}';
    }
}
