package com.javaxiaobear.base.ai.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.lang.reflect.Array;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.function.Function;

public class TypeConverter {

    // 1. 使用Map替代if-else链，更清晰、易扩展
    private static final Map<Class<?>, Function<String, ?>> PRIMITIVE_CONVERTERS = new HashMap<>();

    static {
        PRIMITIVE_CONVERTERS.put(String.class, s -> s);
        PRIMITIVE_CONVERTERS.put(Integer.class, Integer::parseInt);
        PRIMITIVE_CONVERTERS.put(int.class, Integer::parseInt);
        PRIMITIVE_CONVERTERS.put(Long.class, Long::parseLong);
        PRIMITIVE_CONVERTERS.put(long.class, Long::parseLong);
        PRIMITIVE_CONVERTERS.put(Double.class, Double::parseDouble);
        PRIMITIVE_CONVERTERS.put(double.class, Double::parseDouble);
        PRIMITIVE_CONVERTERS.put(Float.class, Float::parseFloat);
        PRIMITIVE_CONVERTERS.put(float.class, Float::parseFloat);
        PRIMITIVE_CONVERTERS.put(Boolean.class, s -> "1".equals(s) || Boolean.parseBoolean(s)); // 增强对 "1" 的支持
        PRIMITIVE_CONVERTERS.put(boolean.class, s -> "1".equals(s) || Boolean.parseBoolean(s));
        PRIMITIVE_CONVERTERS.put(Short.class, Short::parseShort);
        PRIMITIVE_CONVERTERS.put(short.class, Short::parseShort);
        PRIMITIVE_CONVERTERS.put(Byte.class, Byte::parseByte);
        PRIMITIVE_CONVERTERS.put(byte.class, Byte::parseByte);
    }

    // 2. 使用线程安全的 java.time API
    private static final List<DateTimeFormatter> DATE_TIME_FORMATTERS = Arrays.asList(
            DateTimeFormatter.ISO_LOCAL_DATE_TIME, // yyyy-MM-dd'T'HH:mm:ss
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss"),
            DateTimeFormatter.ISO_LOCAL_DATE, // yyyy-MM-dd
            DateTimeFormatter.ofPattern("yyyy/MM/dd")
    );

    // ObjectMapper是线程安全的，可以复用
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();


    public static Object convertValueToFieldType(Object value, Class<?> targetType) {
        // 初始检查
        if (value == null || "null".equalsIgnoreCase(String.valueOf(value))) {
            return null;
        }
        if (targetType.isAssignableFrom(value.getClass())) {
            return value;
        }

        String stringValue = value.toString().trim();
        if (stringValue.isEmpty()) {
            return null;
        }

        try {
            // 3. 优先使用Map进行基本类型转换
            if (PRIMITIVE_CONVERTERS.containsKey(targetType)) {
                return PRIMITIVE_CONVERTERS.get(targetType).apply(stringValue);
            }

            // 4. 类型安全地处理枚举，无需@SuppressWarnings
            if (targetType.isEnum()) {
                // asSubclass是类型安全的转换，确保targetType是Enum的子类
                return Enum.valueOf(targetType.asSubclass(Enum.class), stringValue.toUpperCase());
            }

            // 5. 使用java.time处理日期，更健壮
            if (Date.class.isAssignableFrom(targetType) ||
                    LocalDateTime.class.isAssignableFrom(targetType) ||
                    LocalDate.class.isAssignableFrom(targetType)) {
                return parseDateTime(stringValue, targetType);
            }

            // 6. 统一处理集合和数组
            if (targetType.isArray() || Collection.class.isAssignableFrom(targetType)) {
                return convertToCollectionOrArray(value, targetType);
            }

            // 7. 处理Map
            if (Map.class.isAssignableFrom(targetType)) {
                if (value instanceof Map) {
                    return value;
                } else if (stringValue.startsWith("{") && stringValue.endsWith("}")) {
                    return OBJECT_MAPPER.readValue(stringValue, Map.class);
                }
            }
        } catch (Exception ex) {
            throw new IllegalArgumentException("无法将值 [" + stringValue + "] 转换为类型 [" + targetType.getSimpleName() + "]", ex);
        }

        throw new UnsupportedOperationException("不支持从值 [" + stringValue + "] 到类型 [" + targetType.getName() + "] 的转换");
    }

    private static Object parseDateTime(String stringValue, Class<?> targetType) {
        for (DateTimeFormatter formatter : DATE_TIME_FORMATTERS) {
            try {
                if (LocalDateTime.class.isAssignableFrom(targetType)) {
                    return LocalDateTime.parse(stringValue, formatter);
                }
                if (LocalDate.class.isAssignableFrom(targetType)) {
                    return LocalDate.parse(stringValue, formatter);
                }
                // 对于旧的Date类型
                LocalDateTime ldt = LocalDateTime.parse(stringValue, formatter);
                return Date.from(ldt.atZone(ZoneId.systemDefault()).toInstant());
            } catch (DateTimeParseException e) {
                // 忽略并尝试下一个格式
            }
        }
        // 如果所有格式都失败了
        throw new IllegalArgumentException("无法解析日期时间字符串: " + stringValue);
    }

    private static Object convertToCollectionOrArray(Object value, Class<?> targetType) {
        // Step 1: 标准化输入为List<String>
        List<String> sourceItems = new ArrayList<>();
        if (value instanceof Collection) {
            ((Collection<?>) value).forEach(item -> sourceItems.add(item.toString()));
        } else if (value.getClass().isArray()) {
            for (int i = 0; i < Array.getLength(value); i++) {
                sourceItems.add(Array.get(value, i).toString());
            }
        } else {
            // AI返回的List有时是"item1, item2, item3"格式的字符串
            sourceItems.addAll(Arrays.asList(value.toString().split("\\s*,\\s*")));
        }

        // Step 2: 确定集合/数组的元素类型并转换
        Class<?> componentType = targetType.isArray() ? targetType.getComponentType() : Object.class;
        // 注意：对于List<T>，由于类型擦除，我们无法在运行时简单获取T。
        // 这里的实现假定List<Object>，并对每个元素进行尽力转换。
        // 在实际应用中，如果需要精确的List<T>转换，需要传入Field或ParameterizedType对象。
        if (componentType == Object.class) {
            // 尽力推断一个通用类型
            componentType = String.class;
        }

        List<Object> convertedList = new ArrayList<>();
        for (String item : sourceItems) {
            convertedList.add(convertValueToFieldType(item, componentType));
        }

        // Step 3: 返回正确的类型（Array或List）
        if (targetType.isArray()) {
            Object array = Array.newInstance(componentType, convertedList.size());
            for (int i = 0; i < convertedList.size(); i++) {
                Array.set(array, i, convertedList.get(i));
            }
            return array;
        } else {
            // 对于List, Set等
            if (Set.class.isAssignableFrom(targetType)) {
                return new HashSet<>(convertedList);
            }
            return convertedList;
        }
    }
}