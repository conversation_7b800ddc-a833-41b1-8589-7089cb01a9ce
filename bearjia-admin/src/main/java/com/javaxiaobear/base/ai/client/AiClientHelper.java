package com.javaxiaobear.base.ai.client;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 统一的AI调用辅助类
 * 
 * 提供统一、可配置的AI调用接口，封装底层ChatClient的复杂性
 * 
 * <AUTHOR>
 */
@Component
public class AiClientHelper {

    private static final Logger logger = LoggerFactory.getLogger(AiClientHelper.class);

    @Autowired
    private ChatClient chatClient;

    @Value("${spring.ai.openai.chat.options.model:deepseek-chat}")
    private String model;

    /**
     * AI调用配置
     */
    public static class AiCallConfig {
        private double temperature = 0.1;
        private int maxTokens = 500;
        private String systemPrompt;
        private String userPrompt;

        public static AiCallConfig create() {
            return new AiCallConfig();
        }

        public AiCallConfig temperature(double temperature) {
            this.temperature = temperature;
            return this;
        }

        public AiCallConfig maxTokens(int maxTokens) {
            this.maxTokens = maxTokens;
            return this;
        }

        public AiCallConfig systemPrompt(String systemPrompt) {
            this.systemPrompt = systemPrompt;
            return this;
        }

        public AiCallConfig userPrompt(String userPrompt) {
            this.userPrompt = userPrompt;
            return this;
        }

        // Getters
        public double getTemperature() { return temperature; }
        public int getMaxTokens() { return maxTokens; }
        public String getSystemPrompt() { return systemPrompt; }
        public String getUserPrompt() { return userPrompt; }
    }

    /**
     * 执行AI调用 - 意图分析专用
     */
    public String callForIntentAnalysis(AiCallConfig config) {
        return executeAiCall(config, "意图分析");
    }

    /**
     * 执行AI调用 - 结果格式化专用
     */
    public String callForResultFormatting(AiCallConfig config) {
        return executeAiCall(config.temperature(0.3).maxTokens(300), "结果格式化");
    }

    /**
     * 执行AI调用 - 通用方法
     */
    public String callForGeneral(AiCallConfig config, String purpose) {
        return executeAiCall(config, purpose);
    }

    /**
     * 执行AI调用的核心方法
     */
    public String executeAiCall(AiCallConfig config, String purpose) {
        try {
            logger.info("🤖 开始AI调用 - 目的: {}, 模型: {}, 温度: {}, 最大Token: {}", 
                purpose, model, config.getTemperature(), config.getMaxTokens());

            ChatClient.ChatClientRequestSpec requestSpec = chatClient.prompt();
            
            // 设置系统提示
            if (config.getSystemPrompt() != null) {
                requestSpec = requestSpec.system(config.getSystemPrompt());
            }
            
            // 设置用户提示
            if (config.getUserPrompt() != null) {
                requestSpec = requestSpec.user(config.getUserPrompt());
            }
            
            // 设置选项
            requestSpec = requestSpec.options(OpenAiChatOptions.builder()
                .withModel(model)
                .withTemperature(config.getTemperature())
                .withMaxTokens(config.getMaxTokens())
                .build());

            String response = requestSpec.call().content();
            
            logger.info("✅ AI调用成功 - 目的: {}, 响应长度: {}", purpose, response.length());
            logger.debug("AI响应内容: {}", response);
            
            return response;
            
        } catch (Exception e) {
            logger.error("❌ AI调用失败 - 目的: {}, 错误: {}", purpose, e.getMessage(), e);
            throw new RuntimeException("AI调用失败: " + purpose, e);
        }
    }

    /**
     * 快速创建意图分析配置
     */
    public AiCallConfig intentAnalysisConfig(String userPrompt) {
        return AiCallConfig.create()
            .userPrompt(userPrompt)
            .temperature(0.1)
            .maxTokens(500);
    }

    /**
     * 快速创建结果格式化配置
     */
    public AiCallConfig resultFormattingConfig(String userPrompt) {
        return AiCallConfig.create()
            .userPrompt(userPrompt)
            .temperature(0.3)
            .maxTokens(300);
    }
}
