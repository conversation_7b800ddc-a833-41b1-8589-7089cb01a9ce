package com.javaxiaobear.base.ai.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.util.List;

/**
 * 聊天请求数据传输对象
 * 
 * 用于接收前端发送的聊天请求，包括用户消息和对话历史。
 * 
 * <AUTHOR>
 */
public class ChatRequest {

    /** 用户输入的消息 */
    @NotBlank(message = "消息内容不能为空")
    @Size(max = 2000, message = "消息内容不能超过2000个字符")
    private String message;
    
    /** 对话历史记录 */
    private List<ChatMessage> conversationHistory;
    
    /** 会话ID（可选，用于会话管理） */
    private String sessionId;
    
    /** 用户ID（从安全上下文中获取，前端不需要传递） */
    private Long userId;
    
    /** 是否流式响应（暂时保留，后续可能支持） */
    private boolean streaming = false;

    // 构造方法
    public ChatRequest() {}

    public ChatRequest(String message) {
        this.message = message;
    }

    public ChatRequest(String message, List<ChatMessage> conversationHistory) {
        this.message = message;
        this.conversationHistory = conversationHistory;
    }

    // Getters and Setters
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<ChatMessage> getConversationHistory() {
        return conversationHistory;
    }

    public void setConversationHistory(List<ChatMessage> conversationHistory) {
        this.conversationHistory = conversationHistory;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public boolean isStreaming() {
        return streaming;
    }

    public void setStreaming(boolean streaming) {
        this.streaming = streaming;
    }

    @Override
    public String toString() {
        return "ChatRequest{" +
                "message='" + message + '\'' +
                ", conversationHistorySize=" + (conversationHistory != null ? conversationHistory.size() : 0) +
                ", sessionId='" + sessionId + '\'' +
                ", userId=" + userId +
                ", streaming=" + streaming +
                '}';
    }
}
