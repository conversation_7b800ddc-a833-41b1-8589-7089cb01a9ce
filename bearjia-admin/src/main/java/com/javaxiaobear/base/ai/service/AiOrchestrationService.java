package com.javaxiaobear.base.ai.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.javaxiaobear.base.ai.dto.AiResponse;
import com.javaxiaobear.base.ai.dto.ChatMessage;
import com.javaxiaobear.base.ai.dto.ChatRequest;
import com.javaxiaobear.base.ai.dto.ExecuteRequest;
import com.javaxiaobear.base.ai.registry.AiFunctionWrapper;
import com.javaxiaobear.base.ai.registry.DynamicFunctionRegistry;
import com.javaxiaobear.base.common.utils.SecurityUtils;
import com.javaxiaobear.base.framework.security.LoginUser;
import com.javaxiaobear.base.ai.dto.FunctionCallAnalysis;
import com.javaxiaobear.base.ai.model.OperationStep;
import com.javaxiaobear.base.ai.prompt.AiPromptFactory;
import com.javaxiaobear.base.ai.binding.AiParameterBinder;
import com.javaxiaobear.base.ai.client.AiClientHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.model.function.FunctionCallback;
import org.springframework.ai.model.function.FunctionCallbackWrapper;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.*;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.stream.Collectors;

/**
 * AI 编排服务
 *
 * 这是整个 AI Agent 框架的核心服务，负责：
 * 1. 处理用户输入和对话管理
 * 2. 与 Spring AI 交互
 * 3. 执行权限校验和函数调用
 * 4. 处理危险操作确认
 * 5. 管理对话历史和上下文
 *
 * <AUTHOR>
 */
@Service
public class AiOrchestrationService {

    private static final Logger logger = LoggerFactory.getLogger(AiOrchestrationService.class);

    private final DynamicFunctionRegistry functionRegistry;
    private final ObjectMapper objectMapper;
    private final ChatClient chatClient;
    private final AiPromptFactory promptFactory;
    private final AiParameterBinder parameterBinder;
    private final AiClientHelper aiClientHelper;
    private final AgentLoopController agentLoopController;

    @Value("${ai.system.prompt:你是一个智能助手，能够调用系统内部的工具来帮助用户完成各种任务。当需要执行操作时，请准确调用相应的函数。如果信息不完整，请向用户询问必要的参数。}")
    private String systemPrompt;

    @Value("${spring.ai.openai.chat.options.model:deepseek-chat}")
    private String model;

    @Value("${spring.ai.openai.chat.options.temperature:0.7}")
    private Double temperature;

    @Value("${spring.ai.openai.chat.options.max-tokens:1000}")
    private Integer maxTokens;

    public AiOrchestrationService(DynamicFunctionRegistry functionRegistry,
                                ObjectMapper objectMapper,
                                ChatClient chatClient,
                                AiPromptFactory promptFactory,
                                AiParameterBinder parameterBinder,
                                AiClientHelper aiClientHelper,
                                AgentLoopController agentLoopController) {
        this.functionRegistry = functionRegistry;
        this.objectMapper = objectMapper;
        this.chatClient = chatClient;
        this.promptFactory = promptFactory;
        this.parameterBinder = parameterBinder;
        this.aiClientHelper = aiClientHelper;
        this.agentLoopController = agentLoopController;
    }

    /**
     * 处理聊天请求（流式返回）
     */
    public SseEmitter processChatStream(ChatRequest request) {
        SseEmitter emitter = new SseEmitter(120000L); // 2分钟超时，给AI调用足够时间

        // 设置连接断开回调
        emitter.onCompletion(() -> {
            logger.info("✅ SSE连接正常完成");
        });

        emitter.onTimeout(() -> {
            logger.warn("⏰ SSE连接超时");
        });

        emitter.onError((throwable) -> {
            logger.error("❌ SSE连接发生错误: {}", throwable.getMessage());
        });

        // 获取用户权限
        Set<String> userRoles = SecurityUtils.getLoginUser().getPermissions();

        // 异步处理
        new Thread(() -> {
            try {
                processStreamingChat(request, emitter, userRoles);
            } catch (Exception e) {
                logger.error("流式处理聊天请求失败", e);
                // 确保在异常情况下正确关闭emitter
                try {
                    safeSendSseEvent(emitter, "error", "处理请求时发生内部错误");
                    emitter.complete();
                } catch (Exception sendError) {
                    logger.error("发送错误消息失败: {}", sendError.getMessage());
                }
            }
        }).start();

        return emitter;
    }

    /**
     * 安全发送SSE消息
     */
    private boolean safeSendSseEvent(SseEmitter emitter, String eventName, String data) {
        try {
            emitter.send(SseEmitter.event().name(eventName).data(data));
            return true;
        } catch (IllegalStateException e) {
            // SseEmitter已经完成或出错
            logger.warn("⚠️ SSE连接已关闭，跳过发送: {}, 错误: {}", data, e.getMessage());
            return false;
        } catch (Exception e) {
            logger.error("❌ 发送SSE消息失败: {}, 错误: {}", data, e.getMessage());
            return false;
        }
    }

    /**
     * 流式处理聊天
     */
    private void processStreamingChat(ChatRequest request, SseEmitter emitter, Set<String> userRoles) throws IOException {
        try {
            // 发送开始消息
            if (!safeSendSseEvent(emitter, "start", "开始处理您的请求...")) {
                return;
            }

            // 发送权限信息
            if (!safeSendSseEvent(emitter, "info", "正在分析用户权限...")) {
                return;
            }

            // 获取可用函数
            List<AiFunctionWrapper> availableFunctions = functionRegistry.getAvailableFunctions(userRoles);

            // 添加调试日志
            logger.info("🔍 用户权限: {}", userRoles);
            logger.info("🔍 可用函数数量: {}", availableFunctions.size());
            logger.info("🔍 可用函数列表: {}",
                availableFunctions.stream()
                    .map(f -> f.getFunctionName() + "(" + f.getDescription() + ")")
                    .collect(java.util.stream.Collectors.toList()));

            // 发送函数分析信息
            if (!safeSendSseEvent(emitter, "info", "🔍 正在搜查系统功能，分析用户意图...")) {
                return;
            }

            // 发送AI分析开始信号
            if (!safeSendSseEvent(emitter, "info", "🧠 正在调用AI进行意图分析，请稍候...")) {
                return;
            }

            // 分析用户意图（使用AI）
            FunctionCallAnalysis analysis = analyzeUserIntentWithAI(request.getMessage(), availableFunctions);

            if (analysis == null) {
                safeSendSseEvent(emitter, "error", "❌ 无法理解您的请求，请重新描述");
                emitter.complete();
                return;
            }

            // 发送意图分析完成信号
            String analysisResult = analysis.isShouldCall() ?
                ("函数调用 - " + analysis.getFunctionName()) :
                "直接回复";
            if (!safeSendSseEvent(emitter, "info", "✅ 意图分析完成：" + analysisResult)) {
                return;
            }

            if (analysis.isShouldCall()) {
                if (analysis.isMultiStep()) {
                    // 多步操作流式处理
                    processMultiStepOperationStream(analysis, request, emitter);
                } else {
                    // 单步操作流式处理
                    processSingleStepOperationStream(analysis, request, emitter);
                }
            } else {
                // 发送AI回复
                safeSendSseEvent(emitter, "message", analysis.getReasoning());
            }

            // 发送完成信号
            if (safeSendSseEvent(emitter, "done", "")) {
                // 完成流式响应
                emitter.complete();
                logger.info("✅ 流式聊天处理完成");
            }

        } catch (Exception e) {
            logger.error("❌ 流式聊天处理失败: {}", e.getMessage(), e);
            try {
                safeSendSseEvent(emitter, "error", "处理过程中发生错误: " + e.getMessage());
                emitter.complete();
            } catch (Exception completeError) {
                logger.error("完成emitter失败: {}", completeError.getMessage());
            }
        }
    }

    /**
     * 处理聊天请求（非流式）
     *
     * @param request 聊天请求
     * @return AI响应
     */
    public AiResponse processChat(ChatRequest request) {
        // 检测是否需要使用ReAct智能体
        if (shouldUseReActAgent(request.getMessage())) {
            logger.info("🤖 使用ReAct智能体处理复杂请求");
            return agentLoopController.processReActRequest(request);
        }

        // 使用传统的单步处理方式
        return processChatTraditional(request);
    }

    /**
     * 判断是否应该使用ReAct智能体
     */
    private boolean shouldUseReActAgent(String message) {
        logger.debug("🤔 判断是否使用ReAct: {}", message);

        // 复杂操作的关键词
        String[] complexKeywords = {
            "所有", "批量", "全部", "多个", "统计", "分析", "报告",
            "删除.*用户名", "修改.*状态", "查找.*并.*", "先.*再.*", "然后"
        };

        String lowerMessage = message.toLowerCase();
        for (String keyword : complexKeywords) {
            if (lowerMessage.matches(".*" + keyword + ".*")) {
                logger.info("🎯 匹配复杂关键词 '{}', 使用ReAct模式", keyword);
                return true;
            }
        }

        // 检查是否包含多个动作词
        String[] actionWords = {"查询", "删除", "修改", "添加", "创建", "更新", "禁用", "启用"};
        int actionCount = 0;
        for (String action : actionWords) {
            if (lowerMessage.contains(action)) {
                actionCount++;
            }
        }

        if (actionCount >= 2) {
            logger.info("🎯 包含{}个动作词，使用ReAct模式", actionCount);
            return true;
        }

        // 特殊模式：强制使用ReAct（用于测试）
        if (lowerMessage.contains("react") || lowerMessage.contains("智能体")) {
            logger.info("🎯 强制ReAct模式");
            return true;
        }

        logger.debug("📝 使用传统模式");
        return false;
    }

    /**
     * 传统的聊天处理方式
     */
    private AiResponse processChatTraditional(ChatRequest request) {
        try {
            // 获取当前用户信息（临时处理匿名访问）
            Set<String> userRoles = getUserRoles();

            // 检查是否需要强制调用函数
            AiResponse forcedResponse = checkForForcedFunctionCall(request, userRoles);
            if (forcedResponse != null) {
                logger.info("🚀 检测到关键词，强制调用函数");
                return forcedResponse;
            }

            // 构建对话历史
            List<Message> messages = buildConversationHistory(request);
            List<AiFunctionWrapper> availableFunctions = functionRegistry.getAvailableFunctions(userRoles);

            // 调用 Spring AI
            ChatResponse result = callSpringAI(messages, availableFunctions);

            // 处理响应
            return processAIResponse(result, request.getConversationHistory(), availableFunctions);

        } catch (Exception e) {
            logger.error("处理聊天请求失败", e);
            return AiResponse.error("处理请求时发生错误: " + e.getMessage(),
                request.getConversationHistory());
        }
    }

    /**
     * 处理执行确认请求
     * 
     * @param request 执行请求
     * @return AI响应
     */
    public AiResponse processExecution(ExecuteRequest request) {
        try {
            // 获取当前用户信息
            LoginUser loginUser = SecurityUtils.getLoginUser();
            request.setUserId(loginUser.getUserId());

            // 权限校验
            checkFunctionPermission(request.getFunctionName(), loginUser.getPermissions());

            // 执行函数
            Object result = executeFunction(request.getFunctionName(), request.getArguments());

            // 构建响应消息
            List<ChatMessage> messages = new ArrayList<>(request.getConversationHistory());
            
            // 添加函数执行结果到对话历史
            ChatMessage.FunctionCall functionCall = new ChatMessage.FunctionCall(
                request.getFunctionName(), 
                objectMapper.writeValueAsString(request.getArguments()),
                objectMapper.writeValueAsString(result)
            );
            
            messages.add(ChatMessage.functionMessage("函数执行完成", functionCall));

            // 让 AI 总结执行结果
            String summary = generateExecutionSummary(request.getFunctionName(), result);
            messages.add(ChatMessage.assistantMessage(summary));

            return AiResponse.finalResult(summary, messages);

        } catch (AccessDeniedException e) {
            logger.warn("用户权限不足: {}", e.getMessage());
            return AiResponse.error("权限不足，无法执行该操作", request.getConversationHistory());
        } catch (Exception e) {
            logger.error("执行函数失败: {}", request.getFunctionName(), e);
            return AiResponse.error("执行操作失败: " + e.getMessage(), request.getConversationHistory());
        }
    }

    /**
     * 构建对话历史
     */
    private List<Message> buildConversationHistory(ChatRequest request) {
        List<Message> messages = new ArrayList<>();

        // 添加系统提示
        messages.add(new SystemMessage(systemPrompt));

        // 添加历史对话
        if (request.getConversationHistory() != null) {
            for (ChatMessage chatMessage : request.getConversationHistory()) {
                messages.add(convertToSpringAIMessage(chatMessage));
            }
        }

        // 添加当前用户消息
        messages.add(new UserMessage(request.getMessage()));

        return messages;
    }

    /**
     * 调用 Spring AI
     */
    private ChatResponse callSpringAI(List<Message> messages, List<AiFunctionWrapper> availableFunctions) {
        logger.info("开始调用Spring AI - 模型: {}, 温度: {}, 最大Token: {}", model, temperature, maxTokens);

        // 构建函数回调
        List<FunctionCallback> functionCallbacks = availableFunctions.stream()
            .map(this::convertToFunctionCallback)
            .collect(Collectors.toList());

        logger.info("可用函数数量: {}", functionCallbacks.size());

        // 添加强化的系统prompt
        String enhancedSystemPrompt = buildEnhancedSystemPrompt(availableFunctions);

        // 构建包含强化prompt的消息列表
        List<Message> enhancedMessages = new ArrayList<>();
        enhancedMessages.add(new SystemMessage(enhancedSystemPrompt));
        enhancedMessages.addAll(messages);

        // 使用新的 ChatClient API
        ChatClient.ChatClientRequestSpec requestSpec = chatClient.prompt()
            .messages(enhancedMessages)
            .options(OpenAiChatOptions.builder()
                .withModel(model)
                .withTemperature(temperature)
                .withMaxTokens(maxTokens)
                .withFunctionCallbacks(functionCallbacks)
                .build());

        // 调用 Spring AI
        try {
            logger.info("正在调用DeepSeek API...");
            ChatResponse response = requestSpec.call().chatResponse();
            logger.info("DeepSeek API调用成功 - 响应内容长度: {}",
                response.getResult().getOutput().getContent().length());
            return response;
        } catch (Exception e) {
            logger.error("DeepSeek API调用失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 处理 AI 响应
     */
    private AiResponse processAIResponse(ChatResponse result, List<ChatMessage> originalHistory,
                                       List<AiFunctionWrapper> availableFunctions) throws Exception {

        AssistantMessage aiMessage = result.getResult().getOutput();

        // 构建新的对话历史
        List<ChatMessage> messages = new ArrayList<>(originalHistory);
        messages.add(ChatMessage.assistantMessage(aiMessage.getContent()));

        // 检查是否有函数调用
        List<AssistantMessage.ToolCall> toolCalls = aiMessage.getToolCalls();
        if (toolCalls != null && !toolCalls.isEmpty()) {
            AssistantMessage.ToolCall toolCall = toolCalls.get(0);
            String functionName = toolCall.name();
            String arguments = toolCall.arguments();

            // 权限校验
            LoginUser loginUser = SecurityUtils.getLoginUser();
            checkFunctionPermission(functionName, loginUser.getPermissions());

            // 获取函数包装器
            AiFunctionWrapper wrapper = functionRegistry.getFunctionWrapper(functionName);
            if (wrapper == null) {
                throw new IllegalArgumentException("未找到函数: " + functionName);
            }

            // 解析参数
            Object args = objectMapper.readValue(arguments, wrapper.getInputType());
            logger.info("参数解析结果: {}", objectMapper.writeValueAsString(args));

            // 检查参数是否完整，如果不完整则需要二次确认
            String missingFields = checkMissingRequiredFields(args, wrapper);
            if (missingFields != null) {
                String confirmationMessage = String.format(
                    "AI 需要更多信息来执行【%s】\n\n描述: %s\n缺少必需参数: %s\n\n请提供这些参数的值：",
                    functionName, wrapper.getDescription(), missingFields
                );

                AiResponse.ConfirmationRequest confirmationRequest = new AiResponse.ConfirmationRequest(
                    functionName, args, confirmationMessage, wrapper.getDescription()
                );

                return AiResponse.confirmationRequired(confirmationRequest, messages);
            }

            // 检查是否为危险操作
            if (wrapper.isDangerous()) {
                // 需要用户确认
                String confirmationMessage = String.format(
                    "AI 准备执行危险操作【%s】\n\n描述: %s\n参数: %s\n\n您确定要继续执行吗？",
                    functionName, wrapper.getDescription(), arguments
                );

                AiResponse.ConfirmationRequest confirmationRequest = new AiResponse.ConfirmationRequest(
                    functionName, args, confirmationMessage, wrapper.getDescription()
                );

                return AiResponse.confirmationRequired(confirmationRequest, messages);
            } else {
                // 直接执行非危险操作
                Object functionResult = executeFunction(functionName, args);

                // 添加函数执行结果
                ChatMessage.FunctionCall funcCall = new ChatMessage.FunctionCall(
                    functionName, arguments, objectMapper.writeValueAsString(functionResult)
                );
                messages.add(ChatMessage.functionMessage("函数执行完成", funcCall));

                // 生成总结
                String summary = generateExecutionSummary(functionName, functionResult);
                messages.add(ChatMessage.assistantMessage(summary));

                return AiResponse.finalResult(summary, messages);
            }
        }

        // 普通文本回复
        return AiResponse.text(aiMessage.getContent(), messages);
    }

    /**
     * 检查函数权限
     */
    private void checkFunctionPermission(String functionName, Set<String> userRoles) {
        AiFunctionWrapper wrapper = functionRegistry.getFunctionWrapper(functionName);
        if (wrapper == null) {
            throw new AccessDeniedException("函数不存在: " + functionName);
        }

        Set<String> requiredRoles = wrapper.getRequiredRoles();
        if (!requiredRoles.isEmpty()) {
            boolean hasPermission = userRoles.stream().anyMatch(requiredRoles::contains);
            if (!hasPermission) {
                throw new AccessDeniedException("权限不足，无法调用函数: " + functionName);
            }
        }
    }

    /**
     * 执行函数
     */
    private Object executeFunction(String functionName, Object arguments) throws Exception {
        AiFunctionWrapper wrapper = functionRegistry.getFunctionWrapper(functionName);
        if (wrapper == null) {
            throw new IllegalArgumentException("函数不存在: " + functionName);
        }

        logger.info("执行AI函数: {} | 参数: {}", functionName, arguments);

        try {
            Object result = wrapper.invoke(arguments);

            // 对于插入操作，返回插入的对象而不是影响行数
            if (isInsertOperation(functionName) && result instanceof Number && ((Number) result).intValue() > 0) {
                logger.info("🎯 插入操作成功，返回插入的对象而不是影响行数");
                return arguments; // 插入成功后，MyBatis会自动设置ID到原对象中
            }

            logger.info("AI函数执行完成: {} | 结果: {}", functionName, result);
            return result;

        } catch (Exception e) {
            // 提取真实的错误信息
            String realErrorMessage = extractRealErrorMessage(e);
            logger.error("AI函数执行失败: {} | 错误: {}", functionName, realErrorMessage, e);
            throw new RuntimeException("执行 " + functionName + " 失败: " + realErrorMessage, e);
        }
    }

    /**
     * 提取真实的错误信息
     */
    private String extractRealErrorMessage(Exception e) {
        // 如果是RuntimeException包装的异常，尝试获取原始异常
        Throwable cause = e.getCause();
        while (cause != null && cause != e) {
            if (cause.getMessage() != null && !cause.getMessage().isEmpty()) {
                // 处理常见的数据库错误
                String message = cause.getMessage();
                if (message.contains("Duplicate entry")) {
                    return "数据重复，该记录已存在";
                } else if (message.contains("cannot be null")) {
                    return "必填字段不能为空";
                } else if (message.contains("Data too long")) {
                    return "数据长度超出限制";
                } else if (message.contains("foreign key constraint")) {
                    return "违反外键约束，相关数据不存在";
                } else if (message.contains("Access denied")) {
                    return "权限不足，无法执行此操作";
                }
                return message;
            }
            cause = cause.getCause();
        }

        // 如果没有找到有意义的cause，返回原始异常信息
        if (e.getMessage() != null && !e.getMessage().isEmpty()) {
            return e.getMessage();
        }

        return e.getClass().getSimpleName();
    }

    /**
     * 判断是否为插入操作
     */
    private boolean isInsertOperation(String functionName) {
        return functionName.startsWith("insert") || functionName.contains("Add") || functionName.contains("Create");
    }

    /**
     * 判断是否应该执行步骤（条件执行支持）
     */
    private boolean shouldExecuteStep(OperationStep step, List<OperationStep> allSteps, int currentIndex) {
        String condition = step.getCondition();
        if (condition == null || condition.isEmpty()) {
            return true; // 无条件，直接执行
        }

        String conditionField = step.getConditionField();
        Object conditionValue = step.getConditionValue();

        logger.info("🔍 检查步骤条件: {} {} = {}", condition, conditionField, conditionValue);

        switch (condition) {
            case "if_not_exists":
                return !checkDataExists(conditionField, conditionValue);
            case "if_exists":
                return checkPreviousStepHasData(allSteps, currentIndex);
            case "if_step1_empty":
                return checkPreviousStepEmpty(allSteps, currentIndex);
            default:
                logger.warn("未知的条件类型: {}", condition);
                return true;
        }
    }

    /**
     * 检查数据是否存在
     */
    private boolean checkDataExists(String field, Object value) {
        try {
            // 根据字段类型智能选择查询函数
            if (field != null && field.toLowerCase().contains("role")) {
                // 查询角色
                return checkRoleExists(value);
            } else if (field != null && field.toLowerCase().contains("dept")) {
                // 查询部门
                return checkDeptExists(value);
            } else if (field != null && field.toLowerCase().contains("user")) {
                // 查询用户
                return checkUserExists(value);
            }
            return false;
        } catch (Exception e) {
            logger.error("检查数据存在性失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查角色是否存在
     */
    private boolean checkRoleExists(Object value) {
        // 这里可以调用现有的查询函数
        // 例如：selectRoleList 然后检查结果
        return false; // 简化实现，实际应该调用查询函数
    }

    /**
     * 检查部门是否存在
     */
    private boolean checkDeptExists(Object value) {
        return false; // 简化实现
    }

    /**
     * 检查用户是否存在
     */
    private boolean checkUserExists(Object value) {
        return false; // 简化实现
    }

    /**
     * 检查前一步结果是否为空
     */
    private boolean checkPreviousStepEmpty(List<OperationStep> allSteps, int currentIndex) {
        if (currentIndex <= 0) {
            return true; // 没有前一步，认为为空
        }

        OperationStep previousStep = allSteps.get(currentIndex - 1);
        Object result = previousStep.getResult();

        if (result == null) {
            return true;
        }

        // 如果是列表，检查是否为空
        if (result instanceof java.util.List) {
            return ((java.util.List<?>) result).isEmpty();
        }

        return false;
    }

    /**
     * 检查前一步结果是否有数据
     */
    private boolean checkPreviousStepHasData(List<OperationStep> allSteps, int currentIndex) {
        if (currentIndex <= 0) {
            return false; // 没有前一步，认为无数据
        }

        OperationStep previousStep = allSteps.get(currentIndex - 1);
        Object result = previousStep.getResult();

        if (result == null) {
            return false;
        }

        // 如果是列表，检查是否非空
        if (result instanceof java.util.List) {
            boolean hasData = !((java.util.List<?>) result).isEmpty();
            logger.info("🔍 检查前一步数据: {} ({}条记录)", hasData ? "有数据" : "无数据",
                       ((java.util.List<?>) result).size());
            return hasData;
        }

        // 如果是其他类型，认为有数据
        return true;
    }

    /**
     * 生成步骤执行结果描述
     */
    private String generateStepResultDescription(String functionName, Object result) {
        if (result == null) {
            return "操作完成，但未返回数据";
        }

        // 处理列表类型结果
        if (result instanceof java.util.List) {
            java.util.List<?> list = (java.util.List<?>) result;
            int size = list.size();

            if (functionName.contains("select") || functionName.contains("List")) {
                if (size == 0) {
                    return "查询完成，未找到匹配的数据";
                } else {
                    return String.format("查询完成，找到 %d 条记录", size);
                }
            } else if (functionName.contains("delete")) {
                return String.format("删除操作完成，影响 %d 条记录", size);
            } else {
                return String.format("操作完成，返回 %d 条记录", size);
            }
        }

        // 处理数字类型结果（通常是影响行数）
        if (result instanceof Number) {
            int count = ((Number) result).intValue();
            if (functionName.contains("insert") || functionName.contains("add")) {
                return count > 0 ? String.format("创建成功，新增 %d 条记录", count) : "创建失败";
            } else if (functionName.contains("update") || functionName.contains("edit")) {
                return count > 0 ? String.format("更新成功，影响 %d 条记录", count) : "更新失败，未找到匹配记录";
            } else if (functionName.contains("delete") || functionName.contains("remove")) {
                return count > 0 ? String.format("删除成功，影响 %d 条记录", count) : "删除失败，未找到匹配记录";
            } else {
                return count > 0 ? String.format("操作成功，影响 %d 条记录", count) : "操作完成，无数据变更";
            }
        }

        // 处理对象类型结果
        if (result.getClass().getName().startsWith("com.javaxiaobear")) {
            return "操作成功，返回详细信息";
        }

        return "操作完成";
    }

    /**
     * 生成跳过步骤的原因描述
     */
    private String generateSkipReason(OperationStep step, List<OperationStep> allSteps, int currentIndex) {
        String condition = step.getCondition();
        if (condition == null || condition.isEmpty()) {
            return "无条件限制，但被系统跳过";
        }

        switch (condition) {
            case "if_not_exists":
                return "目标数据已存在，无需重复创建";
            case "if_exists":
                if (currentIndex > 0) {
                    OperationStep previousStep = allSteps.get(currentIndex - 1);
                    Object previousResult = previousStep.getResult();
                    if (previousResult instanceof List && ((List<?>) previousResult).isEmpty()) {
                        return "前一步查询无结果，无需执行此操作";
                    }
                }
                return "前置条件不满足，跳过执行";
            case "if_step1_empty":
                return "第1步结果为空，跳过此步骤";
            default:
                return String.format("条件 '%s' 不满足", condition);
        }
    }

    /**
     * 生成步骤失败描述
     */
    private String generateStepFailureDescription(String functionName, Exception e) {
        String errorMessage = e.getMessage();
        if (errorMessage == null || errorMessage.isEmpty()) {
            errorMessage = e.getClass().getSimpleName();
        }

        // 根据函数类型生成更友好的失败描述
        if (functionName.contains("select") || functionName.contains("List")) {
            return "查询操作失败 - " + errorMessage;
        } else if (functionName.contains("insert") || functionName.contains("add")) {
            return "创建操作失败 - " + errorMessage;
        } else if (functionName.contains("update") || functionName.contains("edit")) {
            return "更新操作失败 - " + errorMessage;
        } else if (functionName.contains("delete") || functionName.contains("remove")) {
            return "删除操作失败 - " + errorMessage;
        } else {
            return "操作失败 - " + errorMessage;
        }
    }

    /**
     * 生成执行总结
     */
    private String generateExecutionSummary(String functionName, Object result) {
        try {
            String resultJson = objectMapper.writeValueAsString(result);

            // 使用新的 ChatClient API
            return chatClient.prompt()
                .system("请根据函数执行结果，用简洁友好的语言向用户说明操作结果。")
                .user(String.format("函数 %s 执行完成，结果: %s", functionName, resultJson))
                .options(OpenAiChatOptions.builder()
                    .withMaxTokens(maxTokens)
                    .withTemperature(temperature)
                    .build())
                .call()
                .content();

        } catch (Exception e) {
            logger.warn("生成执行总结失败", e);
            return String.format("操作 %s 执行完成。", functionName);
        }
    }

    /**
     * 转换为 Spring AI 消息格式
     */
    private Message convertToSpringAIMessage(ChatMessage message) {
        switch (message.getRole()) {
            case ASSISTANT:
                return new AssistantMessage(message.getContent());
            case SYSTEM:
                return new SystemMessage(message.getContent());
            default:
                return new UserMessage(message.getContent());
        }
    }

    /**
     * 转换为 Spring AI 函数回调格式
     */
    private FunctionCallback convertToFunctionCallback(AiFunctionWrapper wrapper) {
        return FunctionCallbackWrapper.builder(args -> {
            try {
                return wrapper.invoke(args);
            } catch (Exception e) {
                throw new RuntimeException("函数调用失败: " + wrapper.getFunctionName(), e);
            }
        })
        .withName(wrapper.getFunctionName())
        .withDescription(wrapper.getDescription())
        .withInputType(wrapper.getInputType())
        .build();
    }

    /**
     * 检查是否需要强制调用函数
     * 使用AI智能分析用户意图和参数提取
     */
    private AiResponse checkForForcedFunctionCall(ChatRequest request, Set<String> userRoles) {
        try {
            // 获取可用的函数列表
            List<AiFunctionWrapper> availableFunctions = functionRegistry.getAvailableFunctions(userRoles);

            if (availableFunctions.isEmpty()) {
                return null; // 没有可用函数，继续正常流程
            }

            // 使用AI分析用户意图和提取参数
            FunctionCallAnalysis analysis = analyzeUserIntentWithAI(request.getMessage(), availableFunctions);

            if (analysis != null && analysis.shouldCallFunction()) {
                logger.info("🎯 AI分析检测到函数调用意图: {} -> {}", request.getMessage(), analysis.getFunctionName());

                // 查找目标函数
                AiFunctionWrapper targetFunction = findTargetFunction(availableFunctions, analysis.getFunctionName());
                if (targetFunction == null) {
                    // 生成友好的错误提示
                    String availableFunctionList = availableFunctions.stream()
                        .map(f -> f.getFunctionName() + "(" + f.getDescription() + ")")
                        .limit(5) // 限制显示数量，避免过长
                        .reduce((a, b) -> a + "、" + b)
                        .orElse("无");

                    String errorMessage = String.format("抱歉，系统中没有 '%s' 功能。当前可用的主要功能包括：%s%s。请尝试使用现有功能或联系管理员添加新功能。",
                        analysis.getFunctionName(),
                        availableFunctionList,
                        availableFunctions.size() > 5 ? "等" : "");

                    return AiResponse.error(errorMessage, request.getConversationHistory());
                }

                // 检查权限
                if (!hasRequiredPermissions(targetFunction, userRoles)) {
                    return AiResponse.error("您没有执行此操作的权限", request.getConversationHistory());
                }

                // 执行函数调用
                return executeAIAnalyzedFunction(targetFunction, analysis, request);
            }

        } catch (Exception e) {
            logger.error("AI意图分析失败: {}", e.getMessage(), e);
        }

        return null; // 没有检测到明确意图，继续正常流程
    }

    /**
     * 使用AI分析用户意图和提取参数
     */
    private FunctionCallAnalysis analyzeUserIntentWithAI(String userMessage, List<AiFunctionWrapper> availableFunctions) {
        try {
            // 构建系统消息和用户消息（分离以节省token）
            String systemMessage = promptFactory.buildSystemMessage(availableFunctions);
            String userMessage_formatted = promptFactory.buildUserMessage(userMessage);

            // 调用AI进行意图分析（使用分离式消息）
            AiClientHelper.AiCallConfig config = AiClientHelper.AiCallConfig.create()
                .systemPrompt(systemMessage)
                .userPrompt(userMessage_formatted)
                .temperature(temperature)
                .maxTokens(maxTokens);

            String aiResponse = aiClientHelper.executeAiCall(config, "意图分析");

            logger.info("🤖 AI意图分析结果: {}", aiResponse);

            // 解析AI的分析结果
            return parameterBinder.parseIntentAnalysis(aiResponse, availableFunctions);

        } catch (Exception e) {
            logger.error("AI意图分析过程中发生错误: {}", e.getMessage(), e);
            return null;
        }
    }





    /**
     * 检查是否有必需的权限
     */
    private boolean hasRequiredPermissions(AiFunctionWrapper function, Set<String> userRoles) {
        Set<String> requiredRoles = function.getRequiredRoles();
        if (requiredRoles == null || requiredRoles.isEmpty()) {
            return true; // 没有权限要求
        }
        if (SecurityUtils.isAdmin(SecurityUtils.getUserId())){
            return  true;
        }

        for (String requiredRole : requiredRoles) {
            if (userRoles.contains(requiredRole)) {
                return true; // 有任一权限即可
            }
        }

        return false;
    }

    /**
     * 执行AI分析后的函数调用
     */
    private AiResponse executeAIAnalyzedFunction(AiFunctionWrapper function, FunctionCallAnalysis analysis, ChatRequest request) {
        try {
            // 检查是否为多步操作
            if (analysis.isMultiStep()) {
                logger.info("🔄 执行多步操作: {} 步骤", analysis.getSteps().size());
                return executeMultiStepOperation(analysis, request);
            }

            logger.info("🚀 执行AI分析的函数调用: {} (信心度: {})", analysis.getFunctionName(), analysis.getConfidence());

            // 将AI提取的参数转换为函数需要的参数对象
            Object functionArgs = parameterBinder.bindParametersToFunction(analysis.getExtractedParams(), function);

            // 执行函数
            Object result = executeFunction(analysis.getFunctionName(), functionArgs);

            // 让AI处理结果格式化
            String resultMessage = formatResultWithAI(analysis.getFunctionName(), result, functionArgs, request.getMessage());

            // 构建响应
            List<ChatMessage> messages = new ArrayList<>(request.getConversationHistory());
            messages.add(new ChatMessage(ChatMessage.MessageRole.ASSISTANT, resultMessage));

            return AiResponse.text(resultMessage, messages);

        } catch (Exception e) {
            logger.error("执行AI分析的函数调用失败: {}", e.getMessage(), e);
            return AiResponse.error("执行操作失败: " + e.getMessage(), request.getConversationHistory());
        }
    }

    /**
     * 执行多步操作
     */
    private AiResponse executeMultiStepOperation(FunctionCallAnalysis analysis, ChatRequest request) {
        try {
            List<OperationStep> steps = analysis.getSteps();
            Object finalResult = null;
            String finalFunctionName = analysis.getFunctionName();
            Object finalFunctionArgs = null;

            logger.info("🔄 开始执行多步操作，共{}个步骤", steps.size());

            for (int i = 0; i < steps.size(); i++) {
                OperationStep step = steps.get(i);
                logger.info("📋 执行步骤 {}: {} - {}", i + 1, step.getFunctionName(), step.getPurpose());

                // 获取函数
                AiFunctionWrapper function = functionRegistry.getFunctionWrapper(step.getFunctionName());
                if (function == null) {
                    throw new RuntimeException("未找到函数: " + step.getFunctionName());
                }

                // 处理参数（可能需要从前一步结果中提取）
                Map<String, Object> stepParams = resolveStepParameters(step, steps, i);

                // 绑定参数
                Object functionArgs = parameterBinder.bindParametersToFunction(stepParams, function);

                // 执行函数
                Object stepResult = executeFunction(step.getFunctionName(), functionArgs);
                step.setResult(stepResult);

                logger.info("✅ 步骤 {} 执行完成，结果: {}", i + 1, stepResult);

                // 如果是最后一步，保存结果用于格式化
                if (i == steps.size() - 1) {
                    finalResult = stepResult;
                    finalFunctionName = step.getFunctionName();
                    finalFunctionArgs = functionArgs;
                }
            }

            // 使用AI格式化最终结果
            String resultMessage = formatResultWithAI(finalFunctionName, finalResult, finalFunctionArgs, request.getMessage());

            // 构建响应
            List<ChatMessage> messages = new ArrayList<>(request.getConversationHistory());
            messages.add(new ChatMessage(ChatMessage.MessageRole.ASSISTANT, resultMessage));

            return AiResponse.text(resultMessage, messages);

        } catch (Exception e) {
            logger.error("执行多步操作失败: {}", e.getMessage(), e);
            return AiResponse.error("执行多步操作失败: " + e.getMessage(), request.getConversationHistory());
        }
    }

    /**
     * 使用AI格式化执行结果
     */
    /**
     * 带流式反馈的AI结果格式化
     */
    private String formatResultWithAIStream(String functionName, Object result, Object functionArgs, String originalMessage, SseEmitter emitter) throws IOException {
        try {
            logger.debug("🎨 开始AI格式化结果: 函数={}, 结果类型={}", functionName, result != null ? result.getClass().getSimpleName() : "null");

            if (!safeSendSseEvent(emitter, "info", "🎨 正在分析查询结果...")) {
                return formatResultSimply(functionName, result, originalMessage);
            }

            // 预处理结果，避免过大的对象
            String processedResult = preprocessResultForAI(result);

            if (!safeSendSseEvent(emitter, "info", "📝 正在生成用户友好的回复...")) {
                return formatResultSimply(functionName, result, originalMessage);
            }

            // 构建格式化prompt
            String formatPrompt = promptFactory.buildResultFormatPrompt(
                originalMessage, functionName,
                objectMapper.writeValueAsString(functionArgs), processedResult);

            logger.debug("🎨 格式化Prompt长度: {}", formatPrompt.length());

            if (!safeSendSseEvent(emitter, "info", "🤖 正在调用AI生成最终回复...")) {
                return formatResultSimply(functionName, result, originalMessage);
            }

            // 调用AI进行结果格式化
            AiClientHelper.AiCallConfig config = aiClientHelper.resultFormattingConfig(formatPrompt);
            String aiResponse = aiClientHelper.callForResultFormatting(config);

            logger.info("🎨 AI格式化成功: {}", aiResponse);

            safeSendSseEvent(emitter, "info", "✨ 回复生成完成！");

            return aiResponse;

        } catch (Exception e) {
            logger.error("🎨 AI格式化结果失败: {}", e.getMessage(), e);
            // 降级到智能简单格式
            return formatResultSimply(functionName, result, originalMessage);
        }
    }

    private String formatResultWithAI(String functionName, Object result, Object functionArgs, String originalMessage) {
        try {
            logger.debug("🎨 开始AI格式化结果: 函数={}, 结果类型={}", functionName, result != null ? result.getClass().getSimpleName() : "null");

            // 预处理结果，避免过大的对象
            String processedResult = preprocessResultForAI(result);

            // 构建格式化prompt
            String formatPrompt = promptFactory.buildResultFormatPrompt(
                originalMessage, functionName,
                objectMapper.writeValueAsString(functionArgs), processedResult);

            logger.debug("🎨 格式化Prompt长度: {}", formatPrompt.length());

            // 调用AI进行结果格式化
            AiClientHelper.AiCallConfig config = aiClientHelper.resultFormattingConfig(formatPrompt);
            String aiResponse = aiClientHelper.callForResultFormatting(config);

            logger.info("🎨 AI格式化成功: {}", aiResponse);
            return aiResponse;

        } catch (Exception e) {
            logger.error("🎨 AI格式化结果失败: {}", e.getMessage(), e);
            // 降级到智能简单格式
            return formatResultSimply(functionName, result, originalMessage);
        }
    }

    /**
     * 预处理结果，使其适合AI处理
     */
    private String preprocessResultForAI(Object result) {
        if (result == null) {
            return "null";
        }

        try {
            // 如果是集合类型，提供摘要信息
            if (result instanceof java.util.Collection) {
                java.util.Collection<?> collection = (java.util.Collection<?>) result;
                if (collection.isEmpty()) {
                    return "空列表";
                }

                // 对于通知公告等业务对象，提供结构化摘要
                if (collection.size() <= 10) {
                    return objectMapper.writeValueAsString(result);
                } else {
                    return String.format("包含%d条记录的列表（数据量较大，已省略详细内容）", collection.size());
                }
            }

            // 单个对象
            String jsonResult = objectMapper.writeValueAsString(result);
            if (jsonResult.length() > 2000) {
                return jsonResult.substring(0, 2000) + "...(内容过长，已截断)";
            }

            return jsonResult;

        } catch (Exception e) {
            logger.warn("预处理结果失败: {}", e.getMessage());
            return result.toString();
        }
    }

    /**
     * 简单格式化结果（降级方案）
     */
    private String formatResultSimply(String functionName, Object result, String originalMessage) {
        try {
            if (result == null) {
                return "✅ 操作完成，但没有返回数据。";
            }

            // 针对不同函数类型提供不同的格式化
            if (functionName.contains("select") || functionName.contains("List")) {
                if (result instanceof java.util.Collection) {
                    java.util.Collection<?> collection = (java.util.Collection<?>) result;
                    if (collection.isEmpty()) {
                        return "📋 查询完成，没有找到符合条件的记录。";
                    } else {
                        return String.format("📋 查询完成，共找到 %d 条记录。", collection.size());
                    }
                }
            }

            if (functionName.contains("insert") || functionName.contains("add")) {
                return "✅ 添加操作完成。";
            }

            if (functionName.contains("update") || functionName.contains("modify")) {
                return "✅ 更新操作完成。";
            }

            if (functionName.contains("delete") || functionName.contains("remove")) {
                return "✅ 删除操作完成。";
            }

            // 默认格式
            return String.format("✅ 操作完成！执行了 %s 函数。", functionName);

        } catch (Exception e) {
            logger.error("简单格式化也失败: {}", e.getMessage());
            return "✅ 操作已完成。";
        }
    }

    /**
     * 构建强化的系统prompt
     */
    private String buildEnhancedSystemPrompt(List<AiFunctionWrapper> availableFunctions) {
        return promptFactory.buildSystemPrompt(availableFunctions);
    }

    /**
     * 查找目标函数
     */
    private AiFunctionWrapper findTargetFunction(List<AiFunctionWrapper> availableFunctions, String functionName) {
        return availableFunctions.stream()
            .filter(f -> f.getFunctionName().equals(functionName))
            .findFirst()
            .orElse(null);
    }



    /**
     * 检查必需字段是否缺失
     *
     * @param args 参数对象
     * @param wrapper 函数包装器
     * @return 缺失的字段描述，如果没有缺失则返回null
     */
    private String checkMissingRequiredFields(Object args, AiFunctionWrapper wrapper) {
        if (args == null) {
            return "所有参数";
        }

        try {
            Class<?> inputType = wrapper.getInputType();
            java.lang.reflect.Field[] fields = inputType.getDeclaredFields();

            List<String> missingFields = new ArrayList<>();

            for (java.lang.reflect.Field field : fields) {
                field.setAccessible(true);
                Object value = field.get(args);

                // 检查基本的必需字段
                if (value == null) {
                    // 根据字段名判断是否为必需字段
                    String fieldName = field.getName();
                    if (isRequiredField(fieldName)) {
                        missingFields.add(fieldName);
                    }
                } else if (value instanceof String && ((String) value).trim().isEmpty()) {
                    String fieldName = field.getName();
                    if (isRequiredField(fieldName)) {
                        missingFields.add(fieldName);
                    }
                }
            }

            if (!missingFields.isEmpty()) {
                return String.join(", ", missingFields);
            }

            return null;
        } catch (Exception e) {
            logger.warn("检查必需字段时发生错误: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 判断字段是否为必需字段
     *
     * @param fieldName 字段名
     * @return 是否为必需字段
     */
    private boolean isRequiredField(String fieldName) {
        // 定义必需字段的规则 - 只有真正必需的字段才要求
        switch (fieldName.toLowerCase()) {
            case "username":  // 用户名是创建用户的唯一必需字段
                return true;
            case "keyword":   // 搜索关键词是搜索的必需字段
                return true;
            default:
                return false;
        }
    }

    /**
     * 解析步骤参数，处理对前一步结果的引用
     */
    private Map<String, Object> resolveStepParameters(OperationStep currentStep, List<OperationStep> allSteps, int currentIndex) {
        Map<String, Object> resolvedParams = new HashMap<>(currentStep.getExtractedParams());

        // 处理参数中的引用（支持多种格式）
        for (Map.Entry<String, Object> entry : resolvedParams.entrySet()) {
            Object value = entry.getValue();
            if (value instanceof String) {
                String strValue = (String) value;
                Object resolvedValue = null;

                // 处理标准格式引用：${step1.result[0].userId}
                if (strValue.contains("${step")) {
                    resolvedValue = resolveParameterReference(strValue, allSteps, currentIndex);
                }
                // 处理自然语言描述的引用
                else if (isNaturalLanguageReference(strValue, entry.getKey())) {
                    resolvedValue = resolveNaturalLanguageReference(strValue, entry.getKey(), allSteps, currentIndex);
                }

                if (resolvedValue != null) {
                    resolvedParams.put(entry.getKey(), resolvedValue);
                    logger.info("🔗 解析参数引用: {} -> {}", strValue, resolvedValue);
                }
            }
        }

        // 智能数据合并：如果前一步返回了相关对象，优先使用真实数据
        resolvedParams = mergeWithPreviousStepData(resolvedParams, currentStep, allSteps, currentIndex);

        return resolvedParams;
    }

    /**
     * 判断是否为自然语言引用
     */
    private boolean isNaturalLanguageReference(String value, String paramName) {
        String lowerValue = value.toLowerCase();
        return lowerValue.contains("第一步") ||
               lowerValue.contains("前一步") ||
               lowerValue.contains("上一步") ||
               lowerValue.contains("响应中获取") ||
               lowerValue.contains("结果中获取") ||
               (paramName.toLowerCase().contains("id") && lowerValue.contains("获取"));
    }

    /**
     * 解析自然语言引用
     */
    private Object resolveNaturalLanguageReference(String reference, String paramName, List<OperationStep> allSteps, int currentIndex) {
        logger.info("🔍 尝试解析自然语言引用: {} (参数: {})", reference, paramName);

        // 查找前一步的结果
        if (currentIndex > 0) {
            OperationStep previousStep = allSteps.get(currentIndex - 1);
            Object previousResult = previousStep.getResult();

            logger.debug("前一步结果类型: {}", previousResult != null ? previousResult.getClass().getSimpleName() : "null");

            // 如果参数名包含ID，尝试提取ID值
            if (paramName.toLowerCase().contains("id")) {
                Object extractedId = extractIdFromResult(previousResult, paramName);
                if (extractedId != null) {
                    logger.info("✅ 成功提取ID: {} = {}", paramName, extractedId);
                    return extractedId;
                }
            }

            // 如果前一步返回的是简单值（如新增操作返回的ID）
            if (previousResult instanceof Number) {
                logger.info("✅ 使用前一步返回的数字值: {}", previousResult);
                return previousResult;
            }
        }

        logger.warn("⚠️ 无法解析自然语言引用: {}", reference);
        return null;
    }

    /**
     * 从结果中提取ID值
     */
    private Object extractIdFromResult(Object result, String paramName) {
        if (result == null) return null;

        // 如果结果是数字，直接返回（通常是新增操作返回的ID）
        if (result instanceof Number) {
            return result;
        }

        // 如果结果是列表，取第一个元素
        if (result instanceof java.util.List) {
            java.util.List<?> list = (java.util.List<?>) result;
            if (!list.isEmpty()) {
                Object firstItem = list.get(0);
                logger.debug("从列表中取第一个元素: {}", firstItem.getClass().getSimpleName());
                return extractIdFromSingleObject(firstItem, paramName);
            } else {
                logger.warn("⚠️ 查询结果列表为空");
                return null;
            }
        }

        // 如果结果是单个对象
        return extractIdFromSingleObject(result, paramName);
    }

    /**
     * 从单个对象中提取ID值
     */
    private Object extractIdFromSingleObject(Object obj, String paramName) {
        if (obj == null) return null;

        // 如果对象是我们的业务对象，尝试通过反射获取ID字段
        if (obj.getClass().getName().startsWith("com.javaxiaobear")) {
            try {
                // 智能推断ID字段名
                String[] idFieldNames = generateIdFieldNames(paramName);
                logger.debug("尝试提取ID字段: {}", String.join(", ", idFieldNames));

                for (String fieldName : idFieldNames) {
                    try {
                        java.lang.reflect.Field field = obj.getClass().getDeclaredField(fieldName);
                        field.setAccessible(true);
                        Object value = field.get(obj);
                        if (value != null) {
                            logger.info("✅ 从字段 {} 提取到ID值: {}", fieldName, value);
                            return value;
                        }
                    } catch (NoSuchFieldException ignored) {
                        // 继续尝试下一个字段名
                    }
                }
            } catch (Exception e) {
                logger.debug("反射提取ID失败: {}", e.getMessage());
            }
        }

        return null;
    }

    public String[] generateIdFieldNames(String paramName) {
        if (paramName == null || paramName.isEmpty()) {
            return new String[0];
        }

        List<String> fieldNames = new ArrayList<>();
        String lowerCaseParam = paramName.toLowerCase();

        // 规则1: 处理最常见的 "s" 结尾的复数, 例如 userIds -> userId
        if (lowerCaseParam.endsWith("s")) {
            String singularName;
            // 特殊规则: 处理以 "ies" 结尾的复数, 例如 categories -> category
            if (lowerCaseParam.endsWith("ies")) {
                singularName = paramName.substring(0, paramName.length() - 3) + "y";
            }
            // 一般规则: 处理以 "s" 结尾的复数 (包括 "Ids")
            else {
                singularName = paramName.substring(0, paramName.length() - 1);
            }
            // 确保结尾是 "Id", 如果不是则添加
            if (!singularName.toLowerCase().endsWith("id")) {
                singularName += "Id";
            }
            fieldNames.add(singularName);
        }

        // 备选规则: 如果参数名本身不以 "s" 结尾，但可能就是ID的前缀
        // 例如，参数是 "role"，我们可能也想尝试 "roleId"
        if (!paramName.toLowerCase().endsWith("id")) {
            fieldNames.add(paramName + "Id");
        } else {
            // 如果参数本身就是 "roleId"，也应该被包含
            fieldNames.add(paramName);
        }

        // 去重并返回
        return fieldNames.stream().distinct().toArray(String[]::new);
    }

    /**
     * 智能合并前一步的真实数据
     */
    private Map<String, Object> mergeWithPreviousStepData(Map<String, Object> currentParams,
                                                         OperationStep currentStep,
                                                         List<OperationStep> allSteps,
                                                         int currentIndex) {
        if (currentIndex == 0) {
            return currentParams; // 第一步不需要合并
        }

        // 检查前一步是否有查询结果
        OperationStep previousStep = allSteps.get(currentIndex - 1);
        Object previousResult = previousStep.getResult();

        if (previousResult instanceof List && !((List<?>) previousResult).isEmpty()) {
            List<?> resultList = (List<?>) previousResult;
            Object firstResult = resultList.get(0);

            // 如果当前步骤是修改/删除操作，且前一步查询了相同类型的对象
            if (isModifyOrDeleteOperation(currentStep.getFunctionName()) &&
                isSameEntityType(previousStep.getFunctionName(), currentStep.getFunctionName())) {

                logger.info("🔄 智能合并数据: 使用前一步查询的真实数据替换AI生成的默认值");
                return mergeRealDataWithParams(currentParams, firstResult);
            }
        }

        return currentParams;
    }

    /**
     * 判断是否为修改或删除操作
     */
    private boolean isModifyOrDeleteOperation(String functionName) {
        return functionName.startsWith("update") ||
               functionName.startsWith("delete") ||
               functionName.contains("Status");
    }

    /**
     * 判断是否为相同实体类型
     */
    private boolean isSameEntityType(String queryFunction, String operationFunction) {
        // 提取实体类型名称
        String queryEntity = extractEntityType(queryFunction);
        String operationEntity = extractEntityType(operationFunction);
        return queryEntity.equals(operationEntity);
    }

    /**
     * 提取实体类型名称
     */
    private String extractEntityType(String functionName) {
        if (functionName.contains("User")) return "User";
        if (functionName.contains("Role")) return "Role";
        if (functionName.contains("Dept")) return "Dept";
        if (functionName.contains("Menu")) return "Menu";
        if (functionName.contains("Notice")) return "Notice";
        if (functionName.contains("Post")) return "Post";
        if (functionName.contains("Config")) return "Config";
        return "";
    }

    /**
     * 合并真实数据与参数
     */
    private Map<String, Object> mergeRealDataWithParams(Map<String, Object> currentParams, Object realData) {
        Map<String, Object> mergedParams = new HashMap<>();

        try {
            // 使用反射获取真实对象的所有字段值
            Class<?> dataClass = realData.getClass();
            java.lang.reflect.Field[] fields = dataClass.getDeclaredFields();

            for (java.lang.reflect.Field field : fields) {
                field.setAccessible(true);
                String fieldName = field.getName();
                Object realValue = field.get(realData);

                // 优先使用真实数据，但保留当前参数中的特殊设置（如delFlag等）
                if (currentParams.containsKey(fieldName) && isSpecialOverrideField(fieldName)) {
                    // 对于特殊字段（如delFlag），使用当前参数的值
                    mergedParams.put(fieldName, currentParams.get(fieldName));
                    logger.debug("保留特殊字段: {} = {}", fieldName, currentParams.get(fieldName));
                } else if (realValue != null) {
                    // 使用真实数据
                    mergedParams.put(fieldName, realValue);
                    logger.debug("使用真实数据: {} = {}", fieldName, realValue);
                } else if (currentParams.containsKey(fieldName)) {
                    // 真实数据为null时，使用当前参数
                    mergedParams.put(fieldName, currentParams.get(fieldName));
                }
            }

            // 添加当前参数中的额外字段
            for (Map.Entry<String, Object> entry : currentParams.entrySet()) {
                if (!mergedParams.containsKey(entry.getKey())) {
                    mergedParams.put(entry.getKey(), entry.getValue());
                }
            }

        } catch (Exception e) {
            logger.warn("合并真实数据失败: {}", e.getMessage());
            return currentParams; // 失败时返回原参数
        }

        return mergedParams;
    }

    /**
     * 判断是否为需要特殊处理的覆盖字段
     */
    private boolean isSpecialOverrideField(String fieldName) {
        // 这些字段需要保留当前参数的值，不使用查询结果的值
        return "delFlag".equals(fieldName) ||
               "status".equals(fieldName) ||
               "updateTime".equals(fieldName) ||
               "updateBy".equals(fieldName);
    }

    /**
     * 解析参数引用，如 ${step1.result[0].userId}
     */
    private Object resolveParameterReference(String reference, List<OperationStep> allSteps, int currentIndex) {
        try {
            // 解析引用格式：${stepN.result[index].field}
            if (reference.startsWith("${step") && reference.endsWith("}")) {
                String expr = reference.substring(2, reference.length() - 1); // 去掉 ${ 和 }

                // 解析步骤索引
                int stepIndex = Integer.parseInt(expr.substring(4, expr.indexOf('.'))) - 1; // step1 -> index 0

                if (stepIndex >= 0 && stepIndex < currentIndex) {
                    Object stepResult = allSteps.get(stepIndex).getResult();

                    // 如果结果是List，尝试获取第一个元素
                    if (stepResult instanceof List && !((List<?>) stepResult).isEmpty()) {
                        Object firstItem = ((List<?>) stepResult).get(0);

                        // 解析字段名
                        if (expr.contains(".result[0].")) {
                            String fieldName = expr.substring(expr.lastIndexOf('.') + 1);
                            return extractFieldValue(firstItem, fieldName);
                        }
                    }

                    return stepResult;
                }
            }
        } catch (Exception e) {
            logger.warn("解析参数引用失败: {}, 错误: {}", reference, e.getMessage());
        }

        return null;
    }

    /**
     * 从对象中提取字段值
     */
    private Object extractFieldValue(Object obj, String fieldName) {
        try {
            if (obj == null) return null;

            // 使用反射获取字段值
            java.lang.reflect.Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.get(obj);
        } catch (Exception e) {
            logger.warn("提取字段值失败: {}.{}, 错误: {}", obj.getClass().getSimpleName(), fieldName, e.getMessage());
            return null;
        }
    }

    /**
     * 流式处理单步操作
     */
    private void processSingleStepOperationStream(FunctionCallAnalysis analysis, ChatRequest request, SseEmitter emitter) throws IOException {
        if (!safeSendSseEvent(emitter, "info", "🎯 准备执行操作: " + analysis.getFunctionName())) {
            return;
        }

        try {
            // 获取函数
            if (!safeSendSseEvent(emitter, "info", "🔍 正在查找函数定义...")) {
                return;
            }

            AiFunctionWrapper function = functionRegistry.getFunctionWrapper(analysis.getFunctionName());
            if (function == null) {
                // 生成友好的错误提示
                Set<String> userRoles = getUserRoles();
                List<AiFunctionWrapper> availableFunctions = functionRegistry.getAvailableFunctions(userRoles);
                String availableFunctionList = availableFunctions.stream()
                    .map(f -> f.getFunctionName() + "(" + f.getDescription() + ")")
                    .limit(5)
                    .reduce((a, b) -> a + "、" + b)
                    .orElse("无");

                String errorMessage = String.format("抱歉，系统中没有 '%s' 功能。当前可用的主要功能包括：%s%s。",
                    analysis.getFunctionName(),
                    availableFunctionList,
                    availableFunctions.size() > 5 ? "等" : "");

                throw new RuntimeException(errorMessage);
            }

            // 绑定参数
            if (!safeSendSseEvent(emitter, "info", "⚙️ 正在准备函数参数...")) {
                return;
            }

            Object functionArgs = parameterBinder.bindParametersToFunction(analysis.getExtractedParams(), function);

            if (!safeSendSseEvent(emitter, "info", "🚀 正在执行 " + function.getDescription() + "...")) {
                return;
            }

            // 执行函数
            Object result = executeFunction(analysis.getFunctionName(), functionArgs);

            if (!safeSendSseEvent(emitter, "info", "✅ 函数执行完成，正在使用AI格式化结果...")) {
                return;
            }

            // 使用AI格式化结果
            String formattedResult;
            try {
                formattedResult = formatResultWithAIStream(analysis.getFunctionName(), result, functionArgs, request.getMessage(), emitter);
            } catch (Exception formatException) {
                logger.warn("AI格式化失败，使用简单格式: {}", formatException.getMessage());
                if (!safeSendSseEvent(emitter, "info", "⚠️ AI格式化失败，使用简单格式...")) {
                    return;
                }
                formattedResult = formatResultSimply(analysis.getFunctionName(), result, request.getMessage());
            }

            // 发送格式化后的结果
            safeSendSseEvent(emitter, "message", formattedResult);

            logger.info("✅ 流式单步操作完成");

        } catch (Exception e) {
            logger.error("❌ 流式单步操作执行失败: {}", e.getMessage(), e);

            // 生成详细的失败描述
            String failureDescription = generateStepFailureDescription(analysis.getFunctionName(), e);
            safeSendSseEvent(emitter, "error", "❌ 操作执行失败：" + failureDescription);

            // 抛出异常让外层处理
            throw new RuntimeException("单步操作执行失败", e);
        }
    }

    /**
     * 流式处理多步操作
     */
    private void processMultiStepOperationStream(FunctionCallAnalysis analysis, ChatRequest request, SseEmitter emitter) throws IOException {
        if (!safeSendSseEvent(emitter, "info", "🔄 开始执行多步操作，共 " + analysis.getSteps().size() + " 步")) {
            return;
        }

        Object previousResult = null;

        for (int i = 0; i < analysis.getSteps().size(); i++) {
            OperationStep step = analysis.getSteps().get(i);

            if (!safeSendSseEvent(emitter, "info", "📋 第 " + (i + 1) + " 步: " + step.getPurpose())) {
                return;
            }

            try {
                // 检查条件执行
                logger.info("🔍 检查第{}步条件: condition={}, conditionField={}, conditionValue={}",
                           i + 1, step.getCondition(), step.getConditionField(), step.getConditionValue());

                if (!shouldExecuteStep(step, analysis.getSteps(), i)) {
                    String skipReason = generateSkipReason(step, analysis.getSteps(), i);
                    if (!safeSendSseEvent(emitter, "info", "⏭️ 跳过第 " + (i + 1) + " 步：" + skipReason)) {
                        return;
                    }
                    continue;
                }

                // 获取函数
                if (!safeSendSseEvent(emitter, "info", "🔍 正在查找函数: " + step.getFunctionName())) {
                    return;
                }

                AiFunctionWrapper function = functionRegistry.getFunctionWrapper(step.getFunctionName());
                if (function == null) {
                    throw new RuntimeException("未找到函数: " + step.getFunctionName());
                }

                // 处理参数替换
                if (!safeSendSseEvent(emitter, "info", "⚙️ 正在准备第 " + (i + 1) + " 步参数...")) {
                    return;
                }

                Map<String, Object> processedParams = resolveStepParameters(step, analysis.getSteps(), i);

                // 绑定参数
                Object functionArgs = parameterBinder.bindParametersToFunction(processedParams, function);

                // 执行步骤
                if (!safeSendSseEvent(emitter, "info", "🚀 正在执行第 " + (i + 1) + " 步操作...")) {
                    return;
                }

                try {
                    Object stepResult = executeFunction(step.getFunctionName(), functionArgs);
                    step.setResult(stepResult);
                    previousResult = stepResult;

                    // 生成详细的执行结果描述
                    String resultDescription = generateStepResultDescription(step.getFunctionName(), stepResult);
                    if (!safeSendSseEvent(emitter, "info", "✅ 第 " + (i + 1) + " 步执行成功：" + resultDescription)) {
                        return;
                    }
                } catch (Exception e) {
                    // 生成详细的失败描述
                    String failureDescription = generateStepFailureDescription(step.getFunctionName(), e);
                    if (!safeSendSseEvent(emitter, "error", "❌ 第 " + (i + 1) + " 步执行失败：" + failureDescription)) {
                        return;
                    }
                    throw new RuntimeException("第 " + (i + 1) + " 步执行失败", e);
                }

                // 如果是最后一步，发送最终结果
                if (i == analysis.getSteps().size() - 1) {
                    if (!safeSendSseEvent(emitter, "info", "🎨 正在格式化最终结果...")) {
                        return;
                    }

                    // 格式化最终结果
                    String formattedResult;
                    try {
                        formattedResult = formatResultWithAIStream(step.getFunctionName(), step.getResult(), functionArgs, request.getMessage(), emitter);
                    } catch (Exception formatException) {
                        logger.warn("多步操作最终结果格式化失败，使用简单格式: {}", formatException.getMessage());
                        if (!safeSendSseEvent(emitter, "info", "⚠️ AI格式化失败，使用简单格式...")) {
                            return;
                        }
                        formattedResult = formatResultSimply(step.getFunctionName(), step.getResult(), request.getMessage());
                    }

                    safeSendSseEvent(emitter, "message", formattedResult);

                    logger.info("✅ 流式多步操作完成");
                }

            } catch (Exception e) {
                logger.error("❌ 第{}步执行失败: {}", i + 1, e.getMessage(), e);
                safeSendSseEvent(emitter, "error", "第 " + (i + 1) + " 步执行失败: " + e.getMessage());

                // 抛出异常让外层处理
                throw new RuntimeException("第 " + (i + 1) + " 步执行失败", e);
            }
        }
    }

    /**
     * 获取用户权限
     */
    private Set<String> getUserRoles() {
        try {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            return loginUser.getPermissions();
        } catch (Exception e) {
            // 匿名访问时的临时处理
            logger.info("匿名访问AI聊天接口，跳过权限检查");
            Set<String> userRoles = new HashSet<>();
            // 为测试目的，给匿名用户一些基本权限
            userRoles.add("system:user:add");
            userRoles.add("system:user:query");
            userRoles.add("system:user:list");
            userRoles.add("system:user:edit");
            userRoles.add("system:user:remove");
            userRoles.add("system:role:add");
            userRoles.add("system:role:query");
            userRoles.add("system:role:list");
            userRoles.add("system:role:edit");
            userRoles.add("system:role:remove");
            userRoles.add("system:dept:add");
            userRoles.add("system:dept:query");
            userRoles.add("system:dept:list");
            userRoles.add("system:dept:edit");
            userRoles.add("system:dept:remove");
            userRoles.add("system:menu:add");
            userRoles.add("system:menu:query");
            userRoles.add("system:menu:list");
            userRoles.add("system:menu:edit");
            userRoles.add("system:menu:remove");
            userRoles.add("system:notice:add");
            userRoles.add("system:notice:query");
            userRoles.add("system:notice:list");
            userRoles.add("system:notice:edit");
            userRoles.add("system:notice:remove");
            userRoles.add("system:post:add");
            userRoles.add("system:post:query");
            userRoles.add("system:post:list");
            userRoles.add("system:post:edit");
            userRoles.add("system:post:remove");
            userRoles.add("system:config:add");
            userRoles.add("system:config:query");
            userRoles.add("system:config:list");
            userRoles.add("system:config:edit");
            logger.info("为匿名用户临时分配权限: {}", userRoles);
            return userRoles;
        }
    }
}
