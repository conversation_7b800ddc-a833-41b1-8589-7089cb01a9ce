package com.javaxiaobear.base.ai.executor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.javaxiaobear.base.ai.binding.AiParameterBinder;
import com.javaxiaobear.base.ai.dto.FunctionCallAnalysis;
import com.javaxiaobear.base.ai.registry.AiFunctionWrapper;
import com.javaxiaobear.base.ai.registry.DynamicFunctionRegistry;
import com.javaxiaobear.base.common.utils.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Set;

/**
 * 动态函数执行器
 * 
 * 负责安全地执行AI智能体请求的函数调用
 * 
 * <AUTHOR>
 */
@Component
public class DynamicFunctionExecutor {
    
    private static final Logger logger = LoggerFactory.getLogger(DynamicFunctionExecutor.class);
    
    @Autowired
    private DynamicFunctionRegistry functionRegistry;
    
    @Autowired
    private AiParameterBinder parameterBinder;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * 执行函数调用
     * 
     * @param action 函数调用分析
     * @param userRoles 用户权限
     * @return 执行结果
     */
    public ExecutionResult execute(FunctionCallAnalysis action, Set<String> userRoles) {
        long startTime = System.currentTimeMillis();
        
        try {
            logger.info("🚀 开始执行函数: {}", action.getFunctionName());
            
            // 1. 获取函数包装器
            AiFunctionWrapper function = functionRegistry.getFunctionWrapper(action.getFunctionName());
            if (function == null) {
                return ExecutionResult.error("函数不存在: " + action.getFunctionName());
            }
            
            // 2. 权限检查
            if (!hasRequiredPermissions(function, userRoles)) {
                return ExecutionResult.error("没有执行函数的权限: " + action.getFunctionName());
            }
            
            // 3. 参数绑定
            Object functionArgs;
            try {
                functionArgs = parameterBinder.bindParametersToFunction(action.getExtractedParams(), function);
            } catch (Exception e) {
                logger.error("参数绑定失败: {}", e.getMessage());
                return ExecutionResult.error("参数绑定失败: " + e.getMessage());
            }
            
            // 4. 执行函数
            Object result = invokeFunction(function, functionArgs);
            
            long duration = System.currentTimeMillis() - startTime;
            logger.info("✅ 函数执行成功: {}, 耗时: {}ms", action.getFunctionName(), duration);
            
            return ExecutionResult.success(result, duration);
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            logger.error("❌ 函数执行失败: {}, 耗时: {}ms, 错误: {}", 
                action.getFunctionName(), duration, e.getMessage(), e);
            return ExecutionResult.error("函数执行失败: " + e.getMessage(), duration);
        }
    }
    
    /**
     * 检查是否有必需的权限
     */
    private boolean hasRequiredPermissions(AiFunctionWrapper function, Set<String> userRoles) {
        Set<String> requiredRoles = function.getRequiredRoles();
        if (requiredRoles == null || requiredRoles.isEmpty()) {
            return true; // 没有权限要求
        }
        
        // 检查是否是管理员
        try {
            if (SecurityUtils.isAdmin(SecurityUtils.getUserId())) {
                return true;
            }
        } catch (Exception e) {
            // 忽略异常，继续权限检查
        }
        
        // 检查是否有任一必需权限
        for (String requiredRole : requiredRoles) {
            if (userRoles.contains(requiredRole)) {
                return true;
            }
        }
        
        logger.warn("权限检查失败: 函数={}, 需要权限={}, 用户权限={}", 
            function.getFunctionName(), requiredRoles, userRoles);
        return false;
    }
    
    /**
     * 调用函数
     */
    private Object invokeFunction(AiFunctionWrapper function, Object functionArgs) throws Exception {
        Method method = function.getTargetMethod();
        Object targetBean = function.getTargetBean();
        
        logger.debug("调用函数: {}.{}, 参数: {}", 
            targetBean.getClass().getSimpleName(), method.getName(), functionArgs);
        
        // 执行函数调用
        Object result = method.invoke(targetBean, functionArgs);
        
        logger.debug("函数调用结果类型: {}", result != null ? result.getClass().getSimpleName() : "null");
        
        return result;
    }
    
    /**
     * 格式化执行结果为AI友好的字符串
     */
    public String formatResultForAI(Object result) {
        if (result == null) {
            return "操作执行成功，无返回数据";
        }
        
        try {
            // 如果是简单类型，直接转换
            if (result instanceof String || result instanceof Number || result instanceof Boolean) {
                return result.toString();
            }
            
            // 如果是复杂对象，转换为JSON
            String jsonResult = objectMapper.writeValueAsString(result);
            
            // 限制结果长度，避免过长的响应
            if (jsonResult.length() > 2000) {
                jsonResult = jsonResult.substring(0, 2000) + "...(结果过长，已截断)";
            }
            
            return jsonResult;
            
        } catch (Exception e) {
            logger.warn("格式化结果失败: {}", e.getMessage());
            return "操作执行成功，但结果格式化失败: " + e.getMessage();
        }
    }
    
    /**
     * 执行结果封装类
     */
    public static class ExecutionResult {
        private boolean success;
        private Object result;
        private String errorMessage;
        private long duration;
        
        private ExecutionResult(boolean success, Object result, String errorMessage, long duration) {
            this.success = success;
            this.result = result;
            this.errorMessage = errorMessage;
            this.duration = duration;
        }
        
        public static ExecutionResult success(Object result, long duration) {
            return new ExecutionResult(true, result, null, duration);
        }
        
        public static ExecutionResult error(String errorMessage) {
            return new ExecutionResult(false, null, errorMessage, 0);
        }
        
        public static ExecutionResult error(String errorMessage, long duration) {
            return new ExecutionResult(false, null, errorMessage, duration);
        }
        
        // Getters
        public boolean isSuccess() {
            return success;
        }
        
        public Object getResult() {
            return result;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        public long getDuration() {
            return duration;
        }
        
        @Override
        public String toString() {
            return "ExecutionResult{" +
                    "success=" + success +
                    ", result=" + (result != null ? result.getClass().getSimpleName() : "null") +
                    ", errorMessage='" + errorMessage + '\'' +
                    ", duration=" + duration + "ms" +
                    '}';
        }
    }
}
