package com.javaxiaobear.base.ai.model;

import com.javaxiaobear.base.ai.dto.FunctionCallAnalysis;

/**
 * ReAct循环中的单个回合
 * 
 * 表示AI在一个思考-行动-观察循环中的完整状态
 * 
 * <AUTHOR>
 */
public class ReActTurn {
    
    /** 回合编号 */
    private int turnNumber;
    
    /** AI的思考过程 */
    private String thought;
    
    /** AI决定执行的行动 */
    private FunctionCallAnalysis action;
    
    /** 执行行动后的观察结果 */
    private String observation;
    
    /** 最终答案（如果AI认为任务完成） */
    private String finalAnswer;
    
    /** 是否是错误回合 */
    private boolean isError;
    
    /** 错误信息 */
    private String errorMessage;
    
    /** 回合开始时间 */
    private long startTime;
    
    /** 回合结束时间 */
    private long endTime;
    
    /** 回合状态 */
    private TurnStatus status = TurnStatus.THINKING;
    
    /**
     * 回合状态枚举
     */
    public enum TurnStatus {
        THINKING,    // 思考中
        ACTING,      // 执行行动中
        OBSERVING,   // 观察结果中
        COMPLETED,   // 回合完成
        ERROR        // 回合出错
    }
    
    public ReActTurn() {
        this.startTime = System.currentTimeMillis();
    }
    
    public ReActTurn(int turnNumber) {
        this.turnNumber = turnNumber;
        this.startTime = System.currentTimeMillis();
    }
    
    /**
     * 判断是否有最终答案
     */
    public boolean isFinalAnswer() {
        return finalAnswer != null && !finalAnswer.trim().isEmpty();
    }
    
    /**
     * 判断是否有行动
     */
    public boolean hasAction() {
        return action != null && action.getFunctionName() != null;
    }
    
    /**
     * 判断是否有思考
     */
    public boolean hasThought() {
        return thought != null && !thought.trim().isEmpty();
    }
    
    /**
     * 判断是否有观察结果
     */
    public boolean hasObservation() {
        return observation != null && !observation.trim().isEmpty();
    }
    
    /**
     * 标记回合完成
     */
    public void markCompleted() {
        this.status = TurnStatus.COMPLETED;
        this.endTime = System.currentTimeMillis();
    }
    
    /**
     * 标记回合出错
     */
    public void markError(String errorMessage) {
        this.status = TurnStatus.ERROR;
        this.isError = true;
        this.errorMessage = errorMessage;
        this.endTime = System.currentTimeMillis();
    }
    
    /**
     * 获取回合执行时长（毫秒）
     */
    public long getDuration() {
        if (endTime > 0) {
            return endTime - startTime;
        }
        return System.currentTimeMillis() - startTime;
    }
    
    /**
     * 获取回合摘要信息
     */
    public String getSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("Turn ").append(turnNumber).append(": ");
        
        if (isError) {
            summary.append("ERROR - ").append(errorMessage);
        } else if (isFinalAnswer()) {
            summary.append("FINAL ANSWER");
        } else if (hasAction()) {
            summary.append("ACTION - ").append(action.getFunctionName());
        } else {
            summary.append("THINKING");
        }
        
        return summary.toString();
    }
    
    // Getters and Setters
    public int getTurnNumber() {
        return turnNumber;
    }
    
    public void setTurnNumber(int turnNumber) {
        this.turnNumber = turnNumber;
    }
    
    public String getThought() {
        return thought;
    }
    
    public void setThought(String thought) {
        this.thought = thought;
        this.status = TurnStatus.THINKING;
    }
    
    public FunctionCallAnalysis getAction() {
        return action;
    }
    
    public void setAction(FunctionCallAnalysis action) {
        this.action = action;
        this.status = TurnStatus.ACTING;
    }
    
    public String getObservation() {
        return observation;
    }
    
    public void setObservation(String observation) {
        this.observation = observation;
        this.status = TurnStatus.OBSERVING;
    }
    
    public String getFinalAnswer() {
        return finalAnswer;
    }
    
    public void setFinalAnswer(String finalAnswer) {
        this.finalAnswer = finalAnswer;
        this.status = TurnStatus.COMPLETED;
        this.endTime = System.currentTimeMillis();
    }
    
    public boolean isError() {
        return isError;
    }
    
    public void setError(boolean error) {
        isError = error;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public long getStartTime() {
        return startTime;
    }
    
    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }
    
    public long getEndTime() {
        return endTime;
    }
    
    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }
    
    public TurnStatus getStatus() {
        return status;
    }
    
    public void setStatus(TurnStatus status) {
        this.status = status;
    }
    
    @Override
    public String toString() {
        return "ReActTurn{" +
                "turnNumber=" + turnNumber +
                ", status=" + status +
                ", hasThought=" + hasThought() +
                ", hasAction=" + hasAction() +
                ", hasObservation=" + hasObservation() +
                ", isFinalAnswer=" + isFinalAnswer() +
                ", isError=" + isError +
                ", duration=" + getDuration() + "ms" +
                '}';
    }
}
