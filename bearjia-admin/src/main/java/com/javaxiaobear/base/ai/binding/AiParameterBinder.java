package com.javaxiaobear.base.ai.binding;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.javaxiaobear.base.ai.dto.FunctionCallAnalysis;
import com.javaxiaobear.base.ai.model.OperationStep;
import com.javaxiaobear.base.ai.registry.AiFunctionWrapper;
import com.javaxiaobear.base.ai.utils.TypeConverter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * AI参数绑定器
 * 
 * 负责AI意图分析结果的解析、参数转换和绑定
 * 
 * <AUTHOR>
 */
@Component
public class AiParameterBinder {

    private static final Logger logger = LoggerFactory.getLogger(AiParameterBinder.class);

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 解析AI意图分析结果
     * 
     * @param aiResponse AI响应内容
     * @param availableFunctions 可用函数列表
     * @return 函数调用分析结果
     */
    public FunctionCallAnalysis parseIntentAnalysis(String aiResponse, List<AiFunctionWrapper> availableFunctions) {
        try {
            String jsonPart = extractJsonFromResponse(aiResponse);
            if (jsonPart == null) {
                logger.warn("无法从AI响应中提取JSON: {}", aiResponse);
                return null;
            }

            JsonNode jsonNode = objectMapper.readTree(jsonPart);

            // 验证JSON结构的基本完整性
            if (!validateJsonStructure(jsonNode)) {
                logger.warn("AI响应JSON结构不完整: {}", jsonPart);
                return null;
            }

            // 安全获取shouldCall字段
            JsonNode shouldCallNode = jsonNode.get("shouldCall");
            if (shouldCallNode == null) {
                logger.warn("AI响应中缺少shouldCall字段: {}", jsonPart);
                return null;
            }

            boolean shouldCall = shouldCallNode.asBoolean();

            // 获取reasoning字段
            JsonNode reasoningNode = jsonNode.get("reasoning");
            String reasoning = reasoningNode != null ? reasoningNode.asText() : "AI分析结果";

            if (!shouldCall) {
                // AI认为不需要调用函数，但返回包含reasoning的分析结果
                logger.info("✅ AI判断不需要调用函数: {}", reasoning);
                return new FunctionCallAnalysis(false, null, 0.0, new HashMap<>(), reasoning);
            }

            // 安全获取functionName字段（兼容function字段）
            JsonNode functionNameNode = jsonNode.has("functionName") ?
                jsonNode.get("functionName") : jsonNode.get("function");
            if (functionNameNode == null) {
                logger.warn("AI响应中缺少functionName或function字段: {}", jsonPart);
                return null;
            }
            String functionName = functionNameNode.asText();
            logger.debug("解析到函数名: {}", functionName);

            // 验证函数名是否在可用函数列表中
            if (shouldCall && functionName != null) {
                String finalFunctionName1 = functionName;
                boolean functionExists = availableFunctions.stream()
                    .anyMatch(f -> f.getFunctionName().equals(finalFunctionName1));

                if (!functionExists) {
                    logger.warn("AI尝试调用不存在的函数: {}，可用函数: {}",
                        functionName,
                        availableFunctions.stream().map(f -> f.getFunctionName()).collect(Collectors.toList()));

                    // 修改为不调用函数，并提供说明
                    shouldCall = false;
                    String availableFunctionList = availableFunctions.stream()
                        .map(f -> f.getFunctionName() + "(" + f.getDescription() + ")")
                        .reduce((a, b) -> a + "、" + b)
                        .orElse("无");

                    logger.info("生成函数不存在的友好提示，可用函数: {}", availableFunctionList);
                }
            }

            // 安全获取confidence字段，提供默认值
            JsonNode confidenceNode = jsonNode.get("confidence");
            double confidence = confidenceNode != null ? confidenceNode.asDouble() : 0.5;

            // 如果函数不存在，更新reasoning为友好提示
            String finalFunctionName = functionName;
            if (shouldCall && functionName != null && !availableFunctions.stream().anyMatch(f -> f.getFunctionName().equals(finalFunctionName))) {
                String availableFunctionList = availableFunctions.stream()
                    .map(f -> f.getFunctionName() + "(" + f.getDescription() + ")")
                    .reduce((a, b) -> a + "、" + b)
                    .orElse("无");

                reasoning = String.format("抱歉，系统中没有 '%s' 功能。当前可用的功能包括：%s。请尝试使用现有功能或联系管理员添加新功能。",
                    functionName, availableFunctionList);
                functionName = null; // 清空无效的函数名
                shouldCall = false; // 设置为不调用函数
            }

            // 检查是否为多步操作
            boolean isMultiStep = jsonNode.has("isMultiStep") && jsonNode.get("isMultiStep").asBoolean();

            // 安全提取参数（兼容parameters和extractedParams字段）
            JsonNode extractedParamsNode = jsonNode.has("extractedParams") ?
                jsonNode.get("extractedParams") : jsonNode.get("parameters");
            Map<String, Object> extractedParams = extractParameters(extractedParamsNode);
            logger.debug("解析到参数: {}", extractedParams);

            if (isMultiStep) {
                // 安全解析多步操作步骤
                JsonNode stepsNode = jsonNode.get("steps");
                if (stepsNode == null) {
                    logger.warn("多步操作缺少steps字段，降级为单步操作: {}", jsonPart);
                    isMultiStep = false;
                    logger.info("✅ AI意图分析成功: 函数={}, 信心度={}, 参数={}", functionName, confidence, extractedParams);
                    return new FunctionCallAnalysis(shouldCall, functionName, confidence, extractedParams, reasoning);
                } else {
                    List<OperationStep> steps = parseMultiStepOperations(stepsNode);
                    logger.info("✅ AI意图分析成功: 多步操作, 步骤数={}, 信心度={}", steps.size(), confidence);
                    return new FunctionCallAnalysis(shouldCall, functionName, confidence, extractedParams, reasoning, isMultiStep, steps);
                }
            } else {
                logger.info("✅ AI意图分析成功: 函数={}, 信心度={}, 参数={}", functionName, confidence, extractedParams);
                return new FunctionCallAnalysis(shouldCall, functionName, confidence, extractedParams, reasoning);
            }
            
        } catch (Exception e) {
            logger.error("解析AI意图分析结果失败: {}", e.getMessage(), e);
            logger.error("原始AI响应: {}", aiResponse);

            // 提供更详细的错误信息
            if (e instanceof com.fasterxml.jackson.core.JsonParseException) {
                logger.error("JSON解析错误，请检查AI响应格式");
            } else if (e instanceof NullPointerException) {
                logger.error("空指针异常，可能是AI响应中缺少必需字段");
            }

            return null;
        }
    }

    /**
     * 将AI提取的参数转换为函数需要的参数对象
     *
     * @param extractedParams AI提取的参数
     * @param function 目标函数
     * @return 转换后的参数对象
     */
    public Object bindParametersToFunction(Map<String, Object> extractedParams, AiFunctionWrapper function) {
        try {
            Class<?> inputType = function.getInputType();

            // 处理基本类型和包装类型（如Long、String等）
            if (isPrimitiveOrWrapper(inputType)) {
                return handlePrimitiveType(extractedParams, inputType, function);
            }

            // 处理复杂对象类型
            return handleComplexType(extractedParams, inputType, function.getFunctionName());

        } catch (Exception e) {
            logger.error("转换AI提取的参数失败: {}", e.getMessage(), e);
            throw new RuntimeException("参数转换失败", e);
        }
    }

    /**
     * 判断是否为基本类型或包装类型
     */
    private boolean isPrimitiveOrWrapper(Class<?> type) {
        return type.isPrimitive() ||
               type == String.class ||
               type == Long.class ||
               type == Integer.class ||
               type == Boolean.class ||
               type == Double.class ||
               type == Float.class ||
               type == Short.class ||
               type == Byte.class ||
               type == Character.class;
    }

    /**
     * 处理基本类型参数
     */
    private Object handlePrimitiveType(Map<String, Object> extractedParams, Class<?> inputType, AiFunctionWrapper function) {
        // 对于基本类型，通常参数名就是方法参数名
        // 从函数名推断参数名，或者使用第一个参数值
        String methodName = function.getFunctionName();

        // 常见的参数名模式
        String[] possibleParamNames = {
            getParameterNameFromMethod(methodName),
            "id", "userId", "roleId", "deptId", "menuId", "noticeId", "postId", "configId"
        };

        for (String paramName : possibleParamNames) {
            if (extractedParams.containsKey(paramName)) {
                Object value = extractedParams.get(paramName);
                if (value != null) {
                    return TypeConverter.convertValueToFieldType(value, inputType);
                }
            }
        }

        // 如果没有找到匹配的参数名，使用第一个参数值
        if (!extractedParams.isEmpty()) {
            Object firstValue = extractedParams.values().iterator().next();
            return TypeConverter.convertValueToFieldType(firstValue, inputType);
        }

        throw new RuntimeException("无法为基本类型参数找到合适的值: " + inputType.getSimpleName());
    }

    /**
     * 从方法名推断参数名
     */
    private String getParameterNameFromMethod(String methodName) {
        if (methodName.contains("User")) return "userId";
        if (methodName.contains("Role")) return "roleId";
        if (methodName.contains("Dept")) return "deptId";
        if (methodName.contains("Menu")) return "menuId";
        if (methodName.contains("Notice")) return "noticeId";
        if (methodName.contains("Post")) return "postId";
        if (methodName.contains("Config")) return "configId";
        return "id";
    }

    /**
     * 处理复杂对象类型
     */
    private Object handleComplexType(Map<String, Object> extractedParams, Class<?> inputType, String functionName) throws Exception {
        // 对于查询操作，过滤掉无关的默认值参数
        Map<String, Object> filteredParams = filterParametersForQueryOperation(extractedParams, functionName);

        // 处理接口类型和集合类型
        if (inputType.isInterface() || isCollectionType(inputType)) {
            return handleInterfaceOrCollectionType(filteredParams, inputType);
        }

        Object args = inputType.getDeclaredConstructor().newInstance();

        // 使用反射设置参数值
        java.lang.reflect.Field[] fields = inputType.getDeclaredFields();
        for (java.lang.reflect.Field field : fields) {
            field.setAccessible(true);
            String fieldName = field.getName();

            if (filteredParams.containsKey(fieldName)) {
                Object value = filteredParams.get(fieldName);
                if (value != null) {
                    // 类型转换
                    Object convertedValue = TypeConverter.convertValueToFieldType(value, field.getType());
                    field.set(args, convertedValue);
                    logger.debug("设置参数: {} = {}", fieldName, convertedValue);
                }
            }
        }

        return args;
    }

    /**
     * 从AI响应中提取JSON部分
     */
    private String extractJsonFromResponse(String response) {
        if (response == null || response.trim().isEmpty()) {
            logger.warn("AI响应为空或null");
            return null;
        }

        // 查找JSON开始和结束位置
        int startIndex = response.indexOf("{");
        int endIndex = response.lastIndexOf("}");

        if (startIndex != -1 && endIndex != -1 && endIndex > startIndex) {
            String jsonPart = response.substring(startIndex, endIndex + 1);
            logger.debug("提取的JSON部分: {}", jsonPart);
            return jsonPart;
        }

        // 如果没有找到完整的JSON，尝试查找是否有JSON片段
        logger.warn("无法从AI响应中提取完整JSON，原始响应: {}", response);

        // 尝试查找可能的JSON片段
        if (response.contains("\"shouldCall\"")) {
            logger.info("检测到可能的JSON片段，尝试修复");
            return tryFixIncompleteJson(response);
        }

        return null;
    }

    /**
     * 尝试修复不完整的JSON
     */
    private String tryFixIncompleteJson(String response) {
        try {
            // 简单的JSON修复逻辑
            StringBuilder jsonBuilder = new StringBuilder();

            if (!response.trim().startsWith("{")) {
                jsonBuilder.append("{");
            }

            jsonBuilder.append(response);

            if (!response.trim().endsWith("}")) {
                jsonBuilder.append("}");
            }

            String fixedJson = jsonBuilder.toString();
            logger.debug("尝试修复的JSON: {}", fixedJson);

            // 验证修复后的JSON是否有效
            objectMapper.readTree(fixedJson);
            return fixedJson;

        } catch (Exception e) {
            logger.warn("JSON修复失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 提取参数映射
     */
    private Map<String, Object> extractParameters(JsonNode paramsNode) {
        Map<String, Object> extractedParams = new HashMap<>();

        if (paramsNode == null) {
            logger.debug("参数节点为null，返回空参数映射");
            return extractedParams;
        }

        if (!paramsNode.isObject()) {
            logger.warn("参数节点不是对象类型: {}", paramsNode.getNodeType());
            return extractedParams;
        }

        try {
            paramsNode.fields().forEachRemaining(entry -> {
                try {
                    String key = entry.getKey();
                    JsonNode valueNode = entry.getValue();

                    // 处理不同类型的值
                    Object value;
                    if (valueNode.isNull()) {
                        value = null;
                    } else if (valueNode.isArray()) {
                        value = objectMapper.convertValue(valueNode, List.class);
                    } else if (valueNode.isObject()) {
                        value = objectMapper.convertValue(valueNode, Map.class);
                    } else {
                        value = valueNode.asText();
                    }

                    extractedParams.put(key, value);
                    logger.debug("提取参数: {} = {}", key, value);

                } catch (Exception e) {
                    logger.warn("提取参数时发生错误: {}", e.getMessage());
                }
            });
        } catch (Exception e) {
            logger.error("遍历参数节点时发生错误: {}", e.getMessage(), e);
        }

        return extractedParams;
    }

    /**
     * 解析多步操作步骤
     */
    private List<OperationStep> parseMultiStepOperations(JsonNode stepsNode) {
        List<OperationStep> steps = new ArrayList<>();

        if (stepsNode == null) {
            logger.warn("步骤节点为null");
            return steps;
        }

        if (!stepsNode.isArray()) {
            logger.warn("步骤节点不是数组类型: {}", stepsNode.getNodeType());
            return steps;
        }

        try {
            for (int i = 0; i < stepsNode.size(); i++) {
                JsonNode stepNode = stepsNode.get(i);
                if (stepNode == null) {
                    logger.warn("第{}个步骤节点为null，跳过", i + 1);
                    continue;
                }

                try {
                    OperationStep step = new OperationStep();

                    // 安全获取functionName
                    JsonNode functionNameNode = stepNode.get("functionName");
                    String functionName = functionNameNode != null ? functionNameNode.asText() : "";
                    if (functionName.isEmpty()) {
                        logger.warn("第{}个步骤缺少functionName，跳过", i + 1);
                        continue;
                    }
                    step.setFunctionName(functionName);

                    // 安全获取purpose
                    JsonNode purposeNode = stepNode.get("purpose");
                    String purpose = purposeNode != null ? purposeNode.asText() : "执行操作";
                    step.setPurpose(purpose);

                    // 解析步骤参数
                    JsonNode extractedParamsNode = stepNode.get("extractedParams");
                    Map<String, Object> stepParams = extractParameters(extractedParamsNode);
                    step.setExtractedParams(stepParams);

                    // 解析条件执行字段
                    JsonNode conditionNode = stepNode.get("condition");
                    if (conditionNode != null && !conditionNode.isNull()) {
                        step.setCondition(conditionNode.asText());
                    }

                    JsonNode conditionFieldNode = stepNode.get("conditionField");
                    if (conditionFieldNode != null && !conditionFieldNode.isNull()) {
                        step.setConditionField(conditionFieldNode.asText());
                    }

                    JsonNode conditionValueNode = stepNode.get("conditionValue");
                    if (conditionValueNode != null && !conditionValueNode.isNull()) {
                        step.setConditionValue(conditionValueNode.asText());
                    }

                    // 检查是否依赖前一步结果
                    boolean dependsOnPrevious = stepParams.values().stream()
                        .anyMatch(value -> value instanceof String && ((String) value).contains("${step"));
                    step.setDependsOnPreviousStep(dependsOnPrevious);

                    steps.add(step);
                    logger.debug("解析多步操作步骤 {}: {}", i + 1, step);

                } catch (Exception e) {
                    logger.error("解析第{}个步骤时发生错误: {}", i + 1, e.getMessage());
                }
            }
        } catch (Exception e) {
            logger.error("解析多步操作步骤时发生错误: {}", e.getMessage(), e);
        }

        logger.info("成功解析{}个操作步骤", steps.size());
        return steps;
    }

    /**
     * 验证JSON结构的基本完整性
     */
    private boolean validateJsonStructure(JsonNode jsonNode) {
        if (jsonNode == null || !jsonNode.isObject()) {
            logger.warn("JSON节点为null或不是对象类型");
            return false;
        }

        // 检查必需字段
        if (!jsonNode.has("shouldCall")) {
            logger.warn("JSON缺少必需字段: shouldCall");
            return false;
        }

        // 如果shouldCall为true，则需要functionName或function
        JsonNode shouldCallNode = jsonNode.get("shouldCall");
        if (shouldCallNode != null && shouldCallNode.asBoolean()) {
            // 检查是否有functionName或function字段
            if (!jsonNode.has("functionName") && !jsonNode.has("function")) {
                logger.warn("JSON缺少必需字段: functionName或function");
                return false;
            }

            // 获取函数名（优先使用functionName，如果没有则使用function）
            JsonNode functionNameNode = jsonNode.has("functionName") ?
                jsonNode.get("functionName") : jsonNode.get("function");

            if (functionNameNode == null || functionNameNode.asText().trim().isEmpty()) {
                logger.warn("函数名字段为空");
                return false;
            }
        }

        return true;
    }

    /**
     * 为查询操作过滤无关的默认值参数
     */
    private Map<String, Object> filterParametersForQueryOperation(Map<String, Object> extractedParams, String functionName) {
        // 如果不是查询操作，直接返回原参数
        if (!isQueryOperation(functionName)) {
            return extractedParams;
        }

        Map<String, Object> filteredParams = new HashMap<>();

        for (Map.Entry<String, Object> entry : extractedParams.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            // 过滤掉明显的AI生成默认值
            if (isAiGeneratedDefaultValue(key, value)) {
                logger.debug("过滤查询操作中的AI默认值: {} = {}", key, value);
                continue;
            }

            // 保留有意义的查询条件
            if (isMeaningfulQueryParameter(key, value)) {
                filteredParams.put(key, value);
                logger.debug("保留查询参数: {} = {}", key, value);
            }
        }

        logger.info("🔍 查询操作参数过滤: 原始{}个 -> 过滤后{}个", extractedParams.size(), filteredParams.size());
        return filteredParams;
    }

    /**
     * 判断是否为查询操作
     */
    private boolean isQueryOperation(String functionName) {
        return functionName.startsWith("select") || functionName.contains("List") || functionName.contains("Query");
    }

    /**
     * 判断是否为AI生成的默认值
     */
    private boolean isAiGeneratedDefaultValue(String fieldName, Object value) {
        if (value == null) {
            return false;
        }

        String strValue = value.toString();

        // 检查常见的AI生成默认值模式
        switch (fieldName.toLowerCase()) {
            case "email":
                return strValue.contains("@example.com") || strValue.equals("<EMAIL>");
            case "phonenumber":
            case "phone":
                return strValue.startsWith("138") && strValue.contains("x");
            case "nickname":
            case "nickName":
                return strValue.isEmpty() || strValue.equals("默认昵称");
            case "sex":
                return strValue.isEmpty();
            case "avatar":
                return strValue.isEmpty();
            case "password":
                return strValue.isEmpty();
            case "status":
                return strValue.isEmpty();
            case "delflag":
            case "delFlag":
                return strValue.isEmpty();
            case "loginip":
            case "loginIp":
                return strValue.isEmpty();
            case "remark":
                return strValue.isEmpty();
            default:
                return false;
        }
    }

    /**
     * 判断是否为有意义的查询参数
     */
    private boolean isMeaningfulQueryParameter(String fieldName, Object value) {
        if (value == null) {
            return false;
        }

        String strValue = value.toString().trim();

        // 空字符串不是有意义的查询条件
        if (strValue.isEmpty()) {
            return false;
        }

        // ID字段总是有意义的
        if (fieldName.toLowerCase().contains("id")) {
            return true;
        }

        // 用户名、部门名等关键字段
        if (fieldName.toLowerCase().matches(".*(name|code|key|title).*")) {
            return !isAiGeneratedDefaultValue(fieldName, value);
        }

        // 状态字段如果有具体值也是有意义的
        if (fieldName.toLowerCase().contains("status") && !strValue.isEmpty()) {
            return true;
        }

        return true; // 其他情况默认保留
    }

    /**
     * 判断是否为集合类型
     */
    private boolean isCollectionType(Class<?> type) {
        return java.util.Collection.class.isAssignableFrom(type) ||
               java.util.Map.class.isAssignableFrom(type) ||
               type.isArray();
    }

    /**
     * 处理接口类型和集合类型
     */
    private Object handleInterfaceOrCollectionType(Map<String, Object> filteredParams, Class<?> inputType) {
        logger.info("🔧 处理接口或集合类型: {}", inputType.getSimpleName());

        // 处理Map接口
        if (java.util.Map.class.isAssignableFrom(inputType)) {
            logger.info("📋 返回Map类型参数: {}", filteredParams);
            return new HashMap<>(filteredParams);
        }

        // 处理List接口
        if (java.util.List.class.isAssignableFrom(inputType)) {
            logger.info("📋 返回List类型参数: {}", filteredParams);
            return new ArrayList<>(filteredParams.values());
        }

        // 处理Set接口
        if (java.util.Set.class.isAssignableFrom(inputType)) {
            logger.info("📋 返回Set类型参数: {}", filteredParams);
            return new HashSet<>(filteredParams.values());
        }

        // 处理Collection接口
        if (java.util.Collection.class.isAssignableFrom(inputType)) {
            logger.info("📋 返回Collection类型参数: {}", filteredParams);
            return new ArrayList<>(filteredParams.values());
        }

        // 处理数组类型
        if (inputType.isArray()) {
            logger.info("📋 返回Array类型参数: {}", filteredParams);
            return filteredParams.values().toArray();
        }

        // 对于其他接口类型，尝试返回参数Map
        logger.warn("⚠️ 未知接口类型: {}，返回参数Map", inputType.getSimpleName());
        return filteredParams;
    }

}
