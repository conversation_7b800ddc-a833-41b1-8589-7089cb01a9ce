package com.javaxiaobear.base.ai.prompt;

import com.javaxiaobear.base.ai.registry.AiFunctionWrapper;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * AI Prompt工程中心
 * 
 * 负责构建各种AI交互的Prompt，集中管理Prompt模板和策略
 * 
 * <AUTHOR>
 */
@Component
public class AiPromptFactory {

    // 系统消息缓存
    private volatile String cachedSystemMessage;
    private volatile int cachedFunctionCount = -1;
    private volatile long lastCacheTime = 0;

    /**
     * 构建系统增强Prompt
     * 
     * @param availableFunctions 可用函数列表
     * @return 系统Prompt
     */
    public String buildSystemPrompt(List<AiFunctionWrapper> availableFunctions) {
        return String.format(
            "你是一个智能系统管理助手。你的核心职责是执行用户请求的系统操作。\n" +
            "\n" +
            "🎯 重要指导原则：\n" +
            "1. 当用户请求执行系统操作时，你必须优先调用相应的系统函数\n" +
            "2. 使用合理的默认值来补全缺失的参数\n" +
            "3. 执行函数后，向用户报告具体的操作结果\n" +
            "\n" +
            "📋 可用的系统函数：\n" +
            "%s\n" +
            "\n" +
            "请积极主动地调用这些函数来帮助用户完成任务！",
            formatFunctionList(availableFunctions));
    }

    /**
     * 构建意图分析Prompt
     * 
     * @param userMessage 用户消息
     * @param availableFunctions 可用函数列表
     * @return 意图分析Prompt
     */
    public String buildIntentAnalysisPrompt(String userMessage, List<AiFunctionWrapper> availableFunctions) {
        // 动态生成Prompt，避免硬编码业务逻辑
        return buildDynamicIntentPrompt(userMessage, availableFunctions);
    }

    /**
     * 构建系统消息（固定部分，可缓存）
     */
    public String buildSystemMessage(List<AiFunctionWrapper> availableFunctions) {
        // 检查缓存（5分钟过期）
        long currentTime = System.currentTimeMillis();
        if (cachedSystemMessage != null &&
            cachedFunctionCount == availableFunctions.size() &&
            (currentTime - lastCacheTime) < 300000) {
            return cachedSystemMessage;
        }

        StringBuilder systemMsg = new StringBuilder();

        systemMsg.append("你是一个智能的业务意图分析助手。请分析用户的意图并提取参数。\n\n");

        systemMsg.append("⚠️ 重要约束：\n");
        systemMsg.append("- 你只能从下面列出的可用函数中选择，不能创造或推测不存在的函数\n");
        systemMsg.append("- 如果用户的需求无法通过现有函数满足，请设置shouldCall为false\n");
        systemMsg.append("- 在reasoning中明确说明缺少相应的功能\n\n");

        // 动态生成函数列表（包含参数结构信息）
        systemMsg.append("可用的函数列表:\n");
        systemMsg.append(formatFunctionListWithParameters(availableFunctions));

        // 动态生成分析规则
        systemMsg.append("\n").append(buildDynamicAnalysisRules(availableFunctions));

        // 响应格式
        systemMsg.append("\n请按以下JSON格式返回分析结果:\n");
        systemMsg.append("```json\n");
        systemMsg.append("{\n");
        systemMsg.append("  \"shouldCall\": true/false,\n");
        systemMsg.append("  \"functionName\": \"函数名\",\n");
        systemMsg.append("  \"confidence\": 0.0-1.0,\n");
        systemMsg.append("  \"reasoning\": \"分析理由\",\n");
        systemMsg.append("  \"extractedParams\": {\n");
        systemMsg.append("    \"参数名\": \"参数值\"\n");
        systemMsg.append("  },\n");
        systemMsg.append("  \"isMultiStep\": false,\n");
        systemMsg.append("  \"steps\": [\n");
        systemMsg.append("    {\n");
        systemMsg.append("      \"functionName\": \"selectRoleList\",\n");
        systemMsg.append("      \"extractedParams\": {\"roleKey\": \"角色编码\"},\n");
        systemMsg.append("      \"reasoning\": \"查询角色是否存在\"\n");
        systemMsg.append("    },\n");
        systemMsg.append("    {\n");
        systemMsg.append("      \"functionName\": \"insertRole\",\n");
        systemMsg.append("      \"extractedParams\": {\"roleName\": \"角色名称\", \"roleKey\": \"角色编码\"},\n");
        systemMsg.append("      \"condition\": \"if_not_exists\",\n");
        systemMsg.append("      \"reasoning\": \"如果角色不存在则创建\"\n");
        systemMsg.append("    },\n");
        systemMsg.append("    {\n");
        systemMsg.append("      \"functionName\": \"insertUser\",\n");
        systemMsg.append("      \"extractedParams\": {\"userName\": \"用户名\", \"roleIds\": \"从第1步获取角色ID\"},\n");
        systemMsg.append("      \"reasoning\": \"创建用户并关联角色\"\n");
        systemMsg.append("    }\n");
        systemMsg.append("  ]\n");
        systemMsg.append("}\n");
        systemMsg.append("```\n");
        systemMsg.append("\n注意：\n");
        systemMsg.append("1. 如果shouldCall为false，只需要返回shouldCall和reasoning字段\n");
        systemMsg.append("2. 【严格要求】functionName必须是上述可用函数列表中的一个，不能是其他任何名称\n");
        systemMsg.append("3. 如果用户需求无法通过现有函数满足，必须设置shouldCall为false\n");
        systemMsg.append("4. 对于创建操作，必须提供所有必要的参数，如果用户没有提供，请生成合理的测试数据\n");
        systemMsg.append("5. 对于insertUser操作，必须提供userName、nickName、password等必要字段\n");
        systemMsg.append("6. confidence表示你对分析结果的信心程度\n\n");

        systemMsg.append("示例场景:\n");
        systemMsg.append("- 用户问\"系统中有多少个用户\"，有selectUserList函数时：\n");
        systemMsg.append("  应该返回 shouldCall: true，functionName: \"selectUserList\"，reasoning: \"通过查询用户列表来统计用户数量\"\n");
        systemMsg.append("- 用户问\"查看用户信息\"，有selectUserList函数时：\n");
        systemMsg.append("  应该返回 shouldCall: true，functionName: \"selectUserList\"，reasoning: \"查询用户列表信息\"\n");
        systemMsg.append("- 用户要求\"创建用户\"，有insertUser函数时：\n");
        systemMsg.append("  应该返回 shouldCall: true，functionName: \"insertUser\"\n");
        systemMsg.append("- 用户问\"删除所有数据\"，但没有相关函数时：\n");
        systemMsg.append("  应该返回 shouldCall: false，reasoning: \"抱歉，系统中没有批量删除功能...\"\n\n");

        // 缓存结果
        String result = systemMsg.toString();
        cachedSystemMessage = result;
        cachedFunctionCount = availableFunctions.size();
        lastCacheTime = System.currentTimeMillis();

        return result;
    }

    /**
     * 构建用户消息（变化部分）
     */
    public String buildUserMessage(String userMessage) {
        return "用户消息: \"" + userMessage + "\"";
    }

    /**
     * 动态构建意图分析Prompt
     */
    private String buildDynamicIntentPrompt(String userMessage, List<AiFunctionWrapper> availableFunctions) {
        StringBuilder prompt = new StringBuilder();

        prompt.append("请分析用户的意图并提取参数。\n\n");
        prompt.append("用户消息: \"").append(userMessage).append("\"\n\n");

        // 动态生成函数列表（包含参数结构信息）
        prompt.append("可用的函数列表:\n");
        prompt.append(formatFunctionListWithParameters(availableFunctions));

        // 动态生成分析规则
        prompt.append("\n").append(buildDynamicAnalysisRules(availableFunctions));

        // 响应格式
        prompt.append("\n请按以下JSON格式返回分析结果:\n");
        prompt.append("```json\n");
        prompt.append("{\n");
        prompt.append("  \"shouldCall\": true/false,\n");
        prompt.append("  \"functionName\": \"函数名\",\n");
        prompt.append("  \"confidence\": 0.0-1.0,\n");
        prompt.append("  \"reasoning\": \"分析理由\",\n");
        prompt.append("  \"extractedParams\": {\n");
        prompt.append("    \"参数名\": \"参数值\"\n");
        prompt.append("  },\n");
        prompt.append("  \"isMultiStep\": false,\n");
        prompt.append("  \"steps\": [\n");
        prompt.append("    {\n");
        prompt.append("      \"functionName\": \"函数名\",\n");
        prompt.append("      \"extractedParams\": {},\n");
        prompt.append("      \"condition\": \"if_not_exists\",\n");
        prompt.append("      \"conditionField\": \"roleName\",\n");
        prompt.append("      \"conditionValue\": \"角色名称\"\n");
        prompt.append("    }\n");
        prompt.append("  ]\n");
        prompt.append("}\n");
        prompt.append("```\n");
        prompt.append("\n注意：\n");
        prompt.append("1. 如果shouldCall为false，只需要返回shouldCall和reasoning字段\n");
        prompt.append("2. functionName必须是上述可用函数列表中的一个\n");
        prompt.append("3. 对于创建操作，必须提供所有必要的参数，如果用户没有提供，请生成合理的测试数据\n");
        prompt.append("4. 对于insertUser操作，必须提供userName、nickName、password等必要字段\n");
        prompt.append("5. confidence表示你对分析结果的信心程度\n");

        return prompt.toString();
    }

    /**
     * 构建ReAct智能体Prompt
     *
     * @param userInput 用户输入
     * @param history ReAct历史记录
     * @param availableFunctions 可用函数列表
     * @return ReAct Prompt
     */
    public String buildReActPrompt(String userInput, List<com.javaxiaobear.base.ai.model.ReActTurn> history, List<AiFunctionWrapper> availableFunctions) {
        StringBuilder prompt = new StringBuilder();

        // 1. 角色和指令
        prompt.append("# AI智能操作员\n\n");
        prompt.append("你是一个强大的AI系统操作员。你的任务是理解用户指令，并通过调用可用工具(函数)来完成任务。\n");
        prompt.append("你必须通过'思考-行动-观察'的循环来解决问题。在每一步，你都必须先思考，然后决定是调用一个工具还是给出最终答案。\n\n");

        // 2. 工作原理说明
        prompt.append("## 工作原理\n");
        prompt.append("1. **Thought**: 分析当前状况，思考下一步该做什么\n");
        prompt.append("2. **Action**: 如果需要更多信息或执行操作，调用相应的工具函数\n");
        prompt.append("3. **Observation**: 观察工具执行的结果\n");
        prompt.append("4. **重复**: 继续思考-行动-观察循环，直到能够给出最终答案\n\n");

        // 3. 可用工具列表
        prompt.append("## 可用工具\n");
        if (availableFunctions.isEmpty()) {
            prompt.append("当前没有可用的工具函数。\n");
        } else {
            availableFunctions.forEach(f -> {
                prompt.append(String.format("### %s\n", f.getFunctionName()));
                prompt.append(String.format("- **描述**: %s\n", f.getDescription()));
                prompt.append(String.format("- **分类**: %s\n", f.getCategory()));
                if (f.isDangerous()) {
                    prompt.append("- **⚠️ 危险操作**: 需要特别谨慎\n");
                }
                prompt.append("\n");
            });
        }

        // 4. 输出格式要求
        prompt.append("## 输出格式\n");
        prompt.append("你必须严格按照以下格式输出，不要有任何多余的文字：\n\n");
        prompt.append("```\n");
        prompt.append("Thought: [这里是你的思考过程，分析现状，规划下一步]\n");
        prompt.append("Action: {\"functionName\": \"函数名\", \"params\": {\"参数名\": \"参数值\"}}\n");
        prompt.append("```\n\n");
        prompt.append("或者，如果你已经有足够信息给出最终答案：\n\n");
        prompt.append("```\n");
        prompt.append("Thought: [最终思考]\n");
        prompt.append("Final Answer: [给用户的最终答复]\n");
        prompt.append("```\n\n");

        // 5. 重要规则
        prompt.append("## 重要规则\n");
        prompt.append("1. 每次只能调用一个函数\n");
        prompt.append("2. 必须先思考再行动\n");
        prompt.append("3. 仔细观察每次行动的结果\n");
        prompt.append("4. 如果信息不足，继续收集信息\n");
        prompt.append("5. 危险操作要特别谨慎，确保理解用户真实意图\n");
        prompt.append("6. 给出最终答案时要清晰、准确、有用\n\n");

        // 6. 对话历史
        prompt.append("## 任务\n");
        prompt.append("用户问题: ").append(userInput).append("\n\n");

        if (!history.isEmpty()) {
            prompt.append("## 执行历史\n");
            for (com.javaxiaobear.base.ai.model.ReActTurn turn : history) {
                if (turn.hasThought()) {
                    prompt.append("Thought: ").append(turn.getThought()).append("\n");
                }
                if (turn.hasAction()) {
                    prompt.append("Action: ").append(formatActionForPrompt(turn.getAction())).append("\n");
                }
                if (turn.hasObservation()) {
                    prompt.append("Observation: ").append(turn.getObservation()).append("\n");
                }
                prompt.append("\n");
            }
        }

        // 7. 当前指示
        prompt.append("现在，请基于以上信息进行思考并决定下一步行动：\n\n");

        return prompt.toString();
    }

    /**
     * 格式化行动信息用于Prompt
     */
    private String formatActionForPrompt(com.javaxiaobear.base.ai.dto.FunctionCallAnalysis action) {
        if (action == null) {
            return "null";
        }

        try {
            Map<String, Object> actionMap = new HashMap<>();
            actionMap.put("functionName", action.getFunctionName());
            actionMap.put("params", action.getExtractedParams());

            return new com.fasterxml.jackson.databind.ObjectMapper().writeValueAsString(actionMap);
        } catch (Exception e) {
            return String.format("{\"functionName\": \"%s\", \"params\": %s}",
                action.getFunctionName(), action.getExtractedParams());
        }
    }

    /**
     * 动态构建分析规则
     */
    private String buildDynamicAnalysisRules(List<AiFunctionWrapper> availableFunctions) {
        StringBuilder rules = new StringBuilder();

        // 基础规则
        rules.append("分析规则:\n");
        rules.append("1. 【重要】只能从上述可用函数列表中选择函数，不能创造或推测不存在的函数\n");
        rules.append("2. 如果没有完全匹配的函数，应该设置shouldCall为false，并在reasoning中说明缺少相应功能\n");
        rules.append("3. 根据用户意图选择最匹配的已有函数\n");
        rules.append("4. 提取用户明确提供的参数\n");
        rules.append("5. 对于创建操作，必须提供完整的必要参数\n");

        // 根据可用函数动态生成操作类型规则
        Set<String> categories = availableFunctions.stream()
            .map(AiFunctionWrapper::getCategory)
            .collect(Collectors.toSet());

        if (categories.contains("query")) {
            rules.append("4. 查询操作：只使用用户明确指定的条件\n");
        }
        if (categories.contains("create")) {
            rules.append("5. 新增操作规则：\n");
            rules.append("   - 分析函数参数结构，为必需字段生成合理的测试数据\n");
            rules.append("   - 字符串字段：根据字段名语义生成（如userName生成test_user_xxx）\n");
            rules.append("   - 数字字段：使用合理的默认值\n");
            rules.append("   - 状态字段：根据用户意图设置（如要求禁用则设为禁用状态）\n");
            rules.append("   - 生成的数据应该唯一且符合业务逻辑\n");
        }
        if (categories.contains("modify") || categories.contains("delete")) {
            rules.append("6. 修改/删除操作：需要明确的标识符\n");
        }

        // 添加智能关联处理规则
        rules.append("8. 智能关联处理：\n");
        rules.append("   - 当用户要求创建用户并指定角色时，完整流程应该是：\n");
        rules.append("     1. 查询角色是否存在\n");
        rules.append("     2. 如果角色不存在，创建角色\n");
        rules.append("     3. 创建用户并关联角色ID\n");
        rules.append("   - 当用户要求修改数据时，完整流程应该是：\n");
        rules.append("     1. 查询目标数据（获取主键ID）\n");
        rules.append("     2. 查询关联数据（如果涉及关联修改）\n");
        rules.append("     3. 更新目标数据（必须包含从第1步获取的主键ID）\n");
        rules.append("   - 必须包含最终的目标操作步骤，不要遗漏主要目标\n");
        rules.append("   - 当用户要求删除数据时，完整流程应该是：\n");
        rules.append("     1. 查询目标数据（获取主键ID列表）\n");
        rules.append("     2. 批量删除操作（遍历ID列表逐个删除）\n");
        rules.append("   - 更新/删除操作必须包含主键ID，通过参数引用从前面步骤获取\n");
        rules.append("   - 条件执行：if_exists(有数据时执行)、if_not_exists(无数据时执行)\n");
        rules.append("   - 如果查询结果为空，后续的删除/更新操作不应该执行\n");

        // 添加智能参数推断规则
        rules.append("7. 智能参数推断：\n");
        rules.append("   - 仔细分析函数参数结构中的字段语义提示\n");
        rules.append("   - 根据字段名模式和类型推断字段用途：\n");
        rules.append("     * 以Name结尾的String字段：通常是名称，用户提到的名称直接填入\n");
        rules.append("     * 以Id结尾的数字字段：通常是关联ID，需要查询或使用默认值\n");
        rules.append("     * 包含email的String字段：生成邮箱格式\n");
        rules.append("     * 包含phone的String字段：生成电话格式\n");
        rules.append("     * 包含password的String字段：生成密码\n");
        rules.append("     * 包含status的字段：根据用户意图设置状态\n");
        rules.append("   - 使用时间戳确保生成数据的唯一性\n");

        return rules.toString();
    }
    /**
     * 构建结果格式化Prompt
     * 
     * @param originalMessage 原始用户消息
     * @param functionName 执行的函数名
     * @param functionArgs 函数参数
     * @param result 执行结果
     * @return 结果格式化Prompt
     */
    public String buildResultFormatPrompt(String originalMessage, String functionName,
                                        Object functionArgs, Object result) {
        return String.format(
            "用户请求: %s\n" +
            "执行的函数: %s\n" +
            "函数参数: %s\n" +
            "执行结果: %s\n" +
            "\n" +
            "请根据以上信息，生成一个友好的回复消息告诉用户操作结果。\n" +
            "要求：\n" +
            "1. 语言自然友好\n" +
            "2. 包含关键的操作信息\n" +
            "3. 如果操作成功，告诉用户具体做了什么\n" +
            "4. 如果操作失败，给出可能的原因\n" +
            "5. 不要包含技术细节，用用户能理解的语言",
            originalMessage, functionName, functionArgs, result);
    }

    /**
     * 格式化函数列表
     */
    private String formatFunctionList(List<AiFunctionWrapper> functions) {
        return functions.stream()
            .map(f -> "- %s: %s".formatted(f.getFunctionName(), f.getDescription()))
            .reduce((a, b) -> a + "\n" + b)
            .orElse("暂无可用函数");
    }

    /**
     * 格式化函数列表（包含参数结构信息）
     */
    private String formatFunctionListWithParameters(List<AiFunctionWrapper> functions) {
        return functions.stream()
            .map(this::formatFunctionWithParameters)
            .reduce((a, b) -> a + "\n" + b)
            .orElse("暂无可用函数");
    }

    /**
     * 格式化单个函数信息（包含参数结构）
     */
    private String formatFunctionWithParameters(AiFunctionWrapper function) {
        StringBuilder sb = new StringBuilder();
        sb.append("- ").append(function.getFunctionName()).append(": ").append(function.getDescription());

        // 获取参数类型信息
        Class<?>[] paramTypes = function.getTargetMethod().getParameterTypes();
        if (paramTypes.length > 0) {
            sb.append("\n  参数结构: ");
            Class<?> paramType = paramTypes[0]; // 通常只有一个参数对象

            if (paramType.isPrimitive() || paramType == String.class) {
                sb.append(paramType.getSimpleName());
            } else {
                // 分析复杂对象的字段结构
                sb.append(analyzeParameterStructure(paramType));
            }
        }

        return sb.toString();
    }

    /**
     * 分析参数结构
     */
    private String analyzeParameterStructure(Class<?> paramType) {
        StringBuilder structure = new StringBuilder();
        structure.append(paramType.getSimpleName()).append(" {");

        java.lang.reflect.Field[] fields = paramType.getDeclaredFields();
        boolean first = true;
        for (java.lang.reflect.Field field : fields) {
            // 跳过静态字段和一些框架字段
            if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) ||
                field.getName().startsWith("$") ||
                field.getName().equals("serialVersionUID")) {
                continue;
            }

            if (!first) structure.append(", ");

            // 增强字段信息：包含类型和语义提示
            String fieldInfo = field.getName() + ": " + field.getType().getSimpleName();

            // 添加语义提示（基于字段名模式，不是具体业务）
            String semanticHint = getFieldSemanticHint(field.getName(), field.getType());
            if (!semanticHint.isEmpty()) {
                fieldInfo += " (" + semanticHint + ")";
            }

            structure.append(fieldInfo);
            first = false;
        }

        structure.append("}");
        return structure.toString();
    }

    /**
     * 获取字段语义提示（基于通用模式，不是具体业务）
     */
    private String getFieldSemanticHint(String fieldName, Class<?> fieldType) {
        String lowerName = fieldName.toLowerCase();

        // 基于字段名模式的通用语义推断
        if (lowerName.endsWith("name") && fieldType == String.class) {
            return "名称字段";
        } else if (lowerName.endsWith("id") && (fieldType == Long.class || fieldType == Integer.class)) {
            return "ID字段";
        } else if (lowerName.contains("email") && fieldType == String.class) {
            return "邮箱格式";
        } else if (lowerName.contains("phone") && fieldType == String.class) {
            return "电话格式";
        } else if (lowerName.contains("password") && fieldType == String.class) {
            return "密码字段";
        } else if (lowerName.contains("status") && fieldType == String.class) {
            return "状态字段";
        } else if (lowerName.contains("time") || lowerName.contains("date")) {
            return "时间字段";
        }

        return "";
    }
}
