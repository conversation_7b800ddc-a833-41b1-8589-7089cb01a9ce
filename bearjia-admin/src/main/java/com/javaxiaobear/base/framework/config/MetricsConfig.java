package com.javaxiaobear.base.framework.config;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 指标监控配置
 * 
 * <AUTHOR>
 */
@Configuration
public class MetricsConfig {

    /**
     * 自定义MeterRegistry配置
     */
    @Bean
    public MeterRegistryCustomizer<MeterRegistry> metricsCommonTags() {
        return registry -> registry.config().commonTags(
            "application", "bearjia-admin",
            "version", "1.0.0"
        );
    }

    /**
     * AI请求计数器
     */
    @Bean
    public Counter aiRequestCounter(MeterRegistry meterRegistry) {
        return Counter.builder("ai.requests.total")
            .description("Total AI requests")
            .tag("type", "chat")
            .register(meterRegistry);
    }

    /**
     * AI响应时间计时器
     */
    @Bean
    public Timer aiResponseTimer(MeterRegistry meterRegistry) {
        return Timer.builder("ai.response.time")
            .description("AI response time")
            .tag("type", "chat")
            .register(meterRegistry);
    }

    /**
     * 用户操作计数器
     */
    @Bean
    public Counter userOperationCounter(MeterRegistry meterRegistry) {
        return Counter.builder("user.operations.total")
            .description("Total user operations")
            .register(meterRegistry);
    }

    /**
     * 数据库连接池监控
     */
    @Bean
    public Timer databaseConnectionTimer(MeterRegistry meterRegistry) {
        return Timer.builder("database.connection.time")
            .description("Database connection acquisition time")
            .register(meterRegistry);
    }
}
