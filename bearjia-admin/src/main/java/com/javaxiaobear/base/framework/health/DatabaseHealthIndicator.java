package com.javaxiaobear.base.framework.health;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;

/**
 * 数据库连接健康检查
 * 
 * <AUTHOR>
 */
@Component
public class DatabaseHealthIndicator implements HealthIndicator {

    @Autowired
    private DataSource dataSource;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public Health health() {
        try {
            // 检查数据库连接
            long startTime = System.currentTimeMillis();
            
            try (Connection connection = dataSource.getConnection()) {
                // 执行简单查询测试连接
                Integer result = jdbcTemplate.queryForObject("SELECT 1", Integer.class);
                long responseTime = System.currentTimeMillis() - startTime;
                
                if (result != null && result == 1) {
                    return Health.up()
                        .withDetail("database", "MySQL")
                        .withDetail("status", "Connection successful")
                        .withDetail("response-time-ms", responseTime)
                        .withDetail("connection-pool", getConnectionPoolInfo())
                        .build();
                } else {
                    return Health.down()
                        .withDetail("database", "MySQL")
                        .withDetail("status", "Query failed")
                        .withDetail("response-time-ms", responseTime)
                        .build();
                }
            }
        } catch (SQLException e) {
            return Health.down()
                .withDetail("database", "MySQL")
                .withDetail("status", "Connection failed")
                .withDetail("error", e.getMessage())
                .build();
        } catch (Exception e) {
            return Health.down()
                .withDetail("database", "MySQL")
                .withDetail("status", "Health check failed")
                .withDetail("error", e.getMessage())
                .build();
        }
    }

    private String getConnectionPoolInfo() {
        try {
            // 尝试获取连接池信息
            if (dataSource instanceof com.alibaba.druid.pool.DruidDataSource) {
                com.alibaba.druid.pool.DruidDataSource druidDataSource = 
                    (com.alibaba.druid.pool.DruidDataSource) dataSource;
                return String.format("Active: %d, Idle: %d, Max: %d", 
                    druidDataSource.getActiveCount(),
                    druidDataSource.getPoolingCount(),
                    druidDataSource.getMaxActive());
            }
            return "Connection pool info not available";
        } catch (Exception e) {
            return "Error getting pool info: " + e.getMessage();
        }
    }
}
