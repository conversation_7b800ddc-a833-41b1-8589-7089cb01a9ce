package com.javaxiaobear.base.framework.health;

import com.javaxiaobear.base.ai.registry.DynamicFunctionRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.stereotype.Component;

/**
 * AI服务健康检查
 * 
 * <AUTHOR>
 */
@Component
public class AiHealthIndicator implements HealthIndicator {

    @Autowired
    private DynamicFunctionRegistry functionRegistry;

    @Override
    public Health health() {
        try {
            // 检查AI函数注册状态
            int functionCount = functionRegistry.getAvailableFunctions(null).size();
            
            if (functionCount > 0) {
                return Health.up()
                    .withDetail("ai-service", "Available")
                    .withDetail("functions-registered", functionCount)
                    .withDetail("status", "AI functions are properly registered and available")
                    .build();
            } else {
                return Health.down()
                    .withDetail("ai-service", "No functions registered")
                    .withDetail("functions-registered", 0)
                    .withDetail("status", "AI service is running but no functions are available")
                    .build();
            }
        } catch (Exception e) {
            return Health.down()
                .withDetail("ai-service", "Error")
                .withDetail("error", e.getMessage())
                .withDetail("status", "AI service health check failed")
                .build();
        }
    }
}
