package com.javaxiaobear.base.framework.aspectj;

import java.lang.reflect.Method;
import java.util.Collections;
import java.util.List;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.stereotype.Component;
import com.javaxiaobear.base.common.exception.ServiceException;
import com.javaxiaobear.base.common.utils.SecurityUtils;
import com.javaxiaobear.base.common.utils.StringUtils;
import com.javaxiaobear.base.common.utils.ip.IpUtils;
import com.javaxiaobear.base.framework.aspectj.lang.annotation.RateLimiter;
import com.javaxiaobear.base.framework.aspectj.lang.enums.LimitType;

/**
 * 限流处理
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class RateLimiterAspect
{
    private static final Logger log = LoggerFactory.getLogger(RateLimiterAspect.class);

    private RedisTemplate<Object, Object> redisTemplate;

    private RedisScript<Long> limitScript;

    @Autowired
    public void setRedisTemplate1(RedisTemplate<Object, Object> redisTemplate)
    {
        this.redisTemplate = redisTemplate;
    }

    @Autowired
    public void setLimitScript(RedisScript<Long> limitScript)
    {
        this.limitScript = limitScript;
    }

    @Before("@annotation(rateLimiter)")
    public void doBefore(JoinPoint point, RateLimiter rateLimiter) throws Throwable
    {
        // 检查是否启用限流
        if (!rateLimiter.enabled()) {
            return;
        }

        int time = rateLimiter.time();
        int count = rateLimiter.count();
        String message = rateLimiter.message();

        String combineKey = getCombineKey(rateLimiter, point);
        List<Object> keys = Collections.singletonList(combineKey);

        try
        {
            Long number = redisTemplate.execute(limitScript, keys, count, time);
            if (StringUtils.isNull(number) || number.intValue() > count)
            {
                log.warn("限流触发 - Key: {}, 限制: {}, 当前: {}, IP: {}",
                    combineKey, count, number, IpUtils.getIpAddr());
                throw new ServiceException(message);
            }

            // 记录限流统计信息
            if (log.isDebugEnabled()) {
                log.debug("限流检查通过 - Key: {}, 限制: {}, 当前: {}, 剩余: {}",
                    combineKey, count, number.intValue(), count - number.intValue());
            }
        }
        catch (ServiceException e)
        {
            throw e;
        }
        catch (Exception e)
        {
            log.error("限流检查异常 - Key: {}, Error: {}", combineKey, e.getMessage(), e);
            throw new RuntimeException("服务器限流异常，请稍候再试");
        }
    }

    public String getCombineKey(RateLimiter rateLimiter, JoinPoint point)
    {
        StringBuilder keyBuilder = new StringBuilder(rateLimiter.key());

        // 根据限流类型添加不同的标识
        LimitType limitType = rateLimiter.limitType();
        switch (limitType) {
            case IP:
                keyBuilder.append(":ip:").append(IpUtils.getIpAddr());
                break;
            case USER:
                try {
                    Long userId = SecurityUtils.getUserId();
                    keyBuilder.append(":user:").append(userId != null ? userId : "anonymous");
                } catch (Exception e) {
                    keyBuilder.append(":user:anonymous");
                }
                break;
            case METHOD:
                MethodSignature signature = (MethodSignature) point.getSignature();
                Method method = signature.getMethod();
                Class<?> targetClass = method.getDeclaringClass();
                keyBuilder.append(":method:").append(targetClass.getSimpleName())
                          .append(".").append(method.getName());
                break;
            case CUSTOM:
                // 自定义key，不添加额外标识
                break;
            case DEFAULT:
            default:
                keyBuilder.append(":global");
                break;
        }

        return keyBuilder.toString();
    }
}
