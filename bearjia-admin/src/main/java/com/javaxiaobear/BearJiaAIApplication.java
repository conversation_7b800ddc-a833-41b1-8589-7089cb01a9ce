package com.javaxiaobear;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

/**
 * 启动程序
 * 
 * <AUTHOR>
 */
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class })
public class BearJiaAIApplication
{
    public static void main(String[] args)
    {
        // System.setProperty("spring.devtools.restart.enabled", "false");
        SpringApplication.run(BearJiaAIApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  小熊学Java AI版本 启动成功   ლ(´ڡ`ლ)ﾞ");
    }
}