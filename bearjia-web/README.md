# 🐻 BearJia Admin - Vue3 前端框架

<div align="center">

![Vue](https://img.shields.io/badge/Vue-3.4.21-brightgreen.svg)
![Ant Design Vue](https://img.shields.io/badge/Ant%20Design%20Vue-4.1.2-blue.svg)
![Vite](https://img.shields.io/badge/Vite-5.1.4-646CFF.svg)
![Pinia](https://img.shields.io/badge/Pinia-2.1.7-yellow.svg)

**基于 Vue3 + Composition API + Vite + Ant Design Vue 的现代化管理后台前端框架**

[在线预览](https://admin.javaxiaobear.cn) | [后端仓库](https://gitee.com/javaxiaobear/BearJia-SpringBoot) | [技术文档](https://javaxiaobear.cn)

</div>

## 📖 项目简介

BearJia Admin 前端框架是一个基于 Vue3 + Composition API + Vite + Ant Design Vue 实现的现代化管理后台系统。采用最新的前端技术栈，使用 `<script setup>` 语法糖，代码结构清晰，开发效率高。

### ✨ 技术特色

- 🚀 **最新技术栈**：Vue3.4 + Composition API + Vite5 + Pinia
- 📝 **语法糖**：使用 `<script setup>` 语法，代码量减少30%+
- 🎨 **现代化UI**：Ant Design Vue 4.x，组件丰富，样式美观
- 🔧 **工程化**：ESLint + Prettier + Husky，代码质量保证
- 📱 **响应式**：完美适配桌面端、平板、手机等设备
- 🎯 **组件化**：高度封装的业务组件，开箱即用
- 🌙 **主题系统**：支持亮色/暗色主题，多种布局模式

## 🎯 核心功能

### 🔐 权限管理
- **用户管理**：用户信息维护、角色分配、状态管理、批量操作
- **角色管理**：角色权限配置、数据权限设置、角色分配
- **菜单管理**：动态菜单配置、权限控制、图标管理
- **部门管理**：组织架构管理、树形结构展示、层级管理
- **岗位管理**：岗位信息维护、人员分配、岗位层级

### 📊 系统监控
- **在线用户**：实时在线用户监控、强制下线、会话管理
- **服务监控**：服务器性能监控、JVM监控、系统信息
- **缓存监控**：Redis缓存监控、缓存管理、性能统计
- **操作日志**：用户操作记录、系统访问日志、日志分析
- **登录日志**：用户登录记录、异常登录监控、IP追踪

### 🛠️ 系统工具
- **代码生成**：一键生成前后端代码、支持自定义模板、预览功能
- **系统配置**：动态配置管理、参数设置、配置热更新
- **字典管理**：数据字典维护、下拉选项配置、字典缓存
- **通知公告**：系统通知发布、富文本编辑、消息推送

### 🎨 界面特色
- **多布局模式**：侧边栏、顶部菜单、混合布局、分栏布局、抽屉布局
- **主题切换**：亮色/暗色主题、主题色自定义、布局配置
- **工作台**：数据统计、快捷操作、更新日志、网站展示
- **历史导航**：智能标签页管理、快速页面切换、右键菜单

## 🚀 快速开始

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0 或 yarn >= 1.22.0
- 现代浏览器（Chrome 88+、Firefox 78+、Safari 14+、Edge 88+）

### 安装依赖

```bash
# 克隆项目
git clone https://gitee.com/javaxiaobear/BearJia_Antdv.git

# 进入前端目录
cd BearJia_Antdv/bear-jia-vue3

# 安装依赖
npm install
# 或使用 yarn
yarn install
```

### 开发环境

```bash
# 启动开发服务器
npm run dev
# 或
yarn dev

# 访问地址
http://localhost:5173
```

### 生产构建

```bash
# 构建生产版本
npm run build
# 或
yarn build

# 预览构建结果
npm run preview
# 或
yarn preview
```

### 代码检查

```bash
# ESLint 检查
npm run lint
# 或
yarn lint

# 代码格式化
npm run format
# 或
yarn format
```

## 🔧 配置说明

### 环境变量

项目支持多环境配置，通过 `.env` 文件管理：

- `.env.development` - 开发环境配置
- `.env.production` - 生产环境配置
- `.env.staging` - 预发布环境配置

```bash
# 开发环境示例 (.env.development)
VITE_APP_TITLE = BearJia Admin
VITE_APP_BASE_API = http://localhost:8080
VITE_APP_UPLOAD_URL = http://localhost:8080/common/upload
```

### 后端对接

本项目可以对接多种后端框架：

1. **推荐：BearJia SpringBoot**
   - 仓库地址：[BearJia-SpringBoot](https://gitee.com/javaxiaobear/BearJia-SpringBoot)
   - 完美适配，功能齐全

2. **兼容：RuoYi-Vue**
   - 仓库地址：[RuoYi-Vue](https://gitee.com/y_project/RuoYi-Vue)
   - 需要少量配置调整

## 📁 项目结构

```
bear-jia-vue3/
├── public/                 # 静态资源
│   ├── favicon.ico        # 网站图标
│   └── logo.png           # Logo图片
├── src/
│   ├── api/               # API接口
│   │   ├── system/        # 系统管理接口
│   │   ├── monitor/       # 系统监控接口
│   │   └── tool/          # 系统工具接口
│   ├── assets/            # 资源文件
│   │   ├── images/        # 图片资源
│   │   └── styles/        # 样式文件
│   ├── components/        # 公共组件
│   │   ├── BearJiaProTable/  # 表格组件
│   │   ├── editor/           # 富文本编辑器
│   │   ├── layout/           # 布局组件
│   │   └── common/           # 通用组件
│   ├── composables/       # 组合式函数
│   │   ├── useTable.js    # 表格逻辑
│   │   ├── useDict.js     # 字典逻辑
│   │   └── usePermission.js # 权限逻辑
│   ├── layout/            # 布局文件
│   │   ├── BaseLayout.vue # 基础布局
│   │   ├── SideLayout.vue # 侧边栏布局
│   │   └── TopLayout.vue  # 顶部布局
│   ├── router/            # 路由配置
│   │   ├── index.js       # 路由入口
│   │   ├── routes.js      # 路由配置
│   │   └── permission.js  # 路由守卫
│   ├── stores/            # 状态管理
│   │   ├── user.js        # 用户状态
│   │   ├── permission.js  # 权限状态
│   │   └── app.js         # 应用状态
│   ├── utils/             # 工具函数
│   │   ├── request.js     # HTTP请求
│   │   ├── auth.js        # 认证工具
│   │   └── common.js      # 通用工具
│   ├── views/             # 页面组件
│   │   ├── system/        # 系统管理页面
│   │   ├── monitor/       # 系统监控页面
│   │   ├── tool/          # 系统工具页面
│   │   └── workbench/     # 工作台页面
│   ├── App.vue            # 根组件
│   └── main.js            # 入口文件
├── .env.development       # 开发环境配置
├── .env.production        # 生产环境配置
├── .eslintrc.js           # ESLint配置
├── .prettierrc            # Prettier配置
├── vite.config.js         # Vite配置
└── package.json           # 项目配置
```

## 🔧 核心组件

### ProTable 表格组件

统一的表格组件，集成搜索、分页、导出等功能：

```vue
<template>
  <ProTable
    :api="tableApi"
    :columns="columns"
    :searchFields="searchFields"
    :isTreeTable="true"
    :exportConfig="exportConfig"
    rowKey="id"
  >
    <template #bodyCell="{ column, record }">
      <!-- 自定义单元格内容 -->
    </template>
  </ProTable>
</template>
```

**特性：**
- ✅ 统一的表格布局和样式
- ✅ 集成搜索、分页、导出功能
- ✅ 支持树形表格和可展开行
- ✅ 自定义操作按钮和单元格渲染
- ✅ 响应式设计，移动端友好

### WangEditor 富文本编辑器

基于 WangEditor v5 的富文本编辑器组件：

```vue
<template>
  <WangEditor
    v-model:value="content"
    :height="400"
    :imageSize="5"
    :videoSize="50"
    placeholder="请输入内容..."
  />
</template>
```

**特性：**
- ✅ 基于 WangEditor v5 最新版本
- ✅ 支持图片、视频上传
- ✅ 丰富的文本格式化功能
- ✅ 自定义工具栏配置
- ✅ 完美的Vue3集成

### HistoryNav 历史导航

智能的标签页管理组件：

```vue
<template>
  <HistoryNav />
</template>
```

**特性：**
- ✅ 智能标签页管理
- ✅ 工作台默认不显示
- ✅ 支持标签页关闭和刷新
- ✅ 右键菜单操作
- ✅ 路由状态同步

## 🏗️ 技术栈

### 核心技术

| 技术 | 版本 | 描述 |
|------|------|------|
| Vue | 3.4.21 | 渐进式JavaScript框架 |
| Vite | 5.1.4 | 下一代前端构建工具 |
| Ant Design Vue | 4.1.2 | 企业级UI组件库 |
| Vue Router | 4.3.0 | Vue.js官方路由管理器 |
| Pinia | 2.1.7 | Vue状态管理库 |
| Axios | 1.6.7 | HTTP客户端 |

### 开发工具

| 工具 | 版本 | 描述 |
|------|------|------|
| ESLint | 8.57.0 | 代码质量检查 |
| Prettier | 3.2.5 | 代码格式化 |
| Husky | 9.0.11 | Git钩子管理 |
| Sass | 1.71.1 | CSS预处理器 |

### 功能库

| 库 | 版本 | 描述 |
|------|------|------|
| WangEditor | 5.1.23 | 富文本编辑器 |
| ECharts | 5.6.0 | 数据可视化图表库 |
| Day.js | 1.11.10 | 日期处理库 |
| Lodash | 4.17.21 | 实用工具库 |

## 📈 更新日志

### v1.2.0 (2025-07-15) - 重大功能更新
- ✨ 新增ProTable组件，统一表格布局
- ✨ 完善工作台页面，增加统计功能
- ✨ 优化HistoryNav组件，支持动态显示
- ✨ 集成WangEditor v5富文本编辑器
- 🐛 修复部门管理树形结构显示问题
- 🐛 修复字典管理可展开行功能

### v1.1.5 (2025-06-20) - 功能优化
- ✨ 增强TableActionBar组件扩展性
- ✨ 改进SearchForm组件用户体验
- 🐛 修复导航模式选择样式问题
- 🎨 优化表格操作按钮样式

### v1.1.0 (2025-06-05) - 界面美化
- 🎨 重新设计登录页面
- 🎨 优化主题色彩搭配
- ✨ 增加暗色主题支持
- 📱 提升移动端适配效果

### v1.0.0 (2025-06-01) - 正式发布
- ✨ 完成基础框架搭建
- ✨ 实现用户权限管理
- ✨ 集成代码生成功能
- ✨ 建立完整的监控体系

## 🤝 参与贡献

我们欢迎所有形式的贡献！

### 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

### 开发规范

- 🔧 **代码规范**：遵循 ESLint 配置
- 📝 **提交规范**：使用 Conventional Commits
- 🧪 **测试覆盖**：新功能需要添加测试
- 📚 **文档更新**：重要变更需要更新文档

## 📄 开源协议

本项目基于 [MIT License](../LICENSE) 开源协议。

## 🙏 致谢

感谢以下优秀的开源项目：

- [Vue.js](https://vuejs.org/) - 渐进式JavaScript框架
- [Ant Design Vue](https://antdv.com/) - 企业级UI组件库
- [Vite](https://vitejs.dev/) - 下一代前端构建工具
- [WangEditor](https://www.wangeditor.com/) - 轻量级富文本编辑器

## 📞 联系方式

- 🌐 **个人网站**：[https://javaxiaobear.cn](https://javaxiaobear.cn)
- 📧 **邮箱**：<EMAIL>
- 🐙 **GitHub**：[JavaXiaoBear](https://github.com/javaxiaobear)
- 🦄 **Gitee**：[JavaXiaoBear](https://gitee.com/javaxiaobear)
-    **公众号**：小熊学Java

---

<div align="center">

**🐻 Made with ❤️ by JavaXiaoBear**

如果这个项目对您有帮助，请给我们一个 ⭐ Star 支持一下！

[⬆ 回到顶部](#-bearjia-admin---vue3-前端框架)

</div>
