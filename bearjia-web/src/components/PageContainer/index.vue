<template>
  <div>
    <slot name="search"></slot>
    <a-card :style="{ marginTop: hasSearchSlot ? '16px' : '0' }">
      <div v-if="hasActionsSlot" class="actions-row">
        <slot name="actions"></slot>
      </div>
      <slot></slot>
    </a-card>
  </div>
</template>

<script setup>
import {computed, useSlots} from 'vue';

const slots = useSlots();
const hasSearchSlot = computed(() => !!slots.search);
const hasActionsSlot = computed(() => !!slots.actions);
</script>

<style scoped>
.actions-row {
  margin-bottom: 16px;
}
</style>