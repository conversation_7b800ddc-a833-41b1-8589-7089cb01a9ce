<template>
  <PageContainer>
    <!-- 1. 查询表单区 -->
    <template v-if="props.searchFields && props.searchFields.length > 0" #search>
      <SearchForm
          v-model="searchFormData"
          :fields="props.searchFields"
          @reset="handleReset"
          @search="handleSearch"
      />
    </template>

    <!-- 2. 操作按钮区 -->
    <template #actions>
      <slot :delete="handleDelete" :selectedRowKeys="tableState.selectedRowKeys" name="actions"></slot>
    </template>

    <!-- 3. 表格区 -->
    <a-table
        :bordered="tableConfigStore.bordered"
        :columns="tableState.columns"
        :data-source="tableState.dataSource"
        :loading="tableState.loading"
        :pagination="pagination"
        :row-selection="finalRowSelectionConfig"
        :rowKey="props.rowKey"
        :scroll="tableConfigStore.fixHeader ? { y: tableConfigStore.tableHeight } : undefined"
        :size="tableConfigStore.size"
        :expandable="props.expandable"
        :default-expand-all-rows="props.isTreeTable"
        :indent-size="props.isTreeTable ? 20 : undefined"
        v-bind="$attrs"
        @change="handleTableChange"
    >
      <template #bodyCell="{ index, column, record }">
        <slot :column="column" :index="index" :record="record" name="bodyCell">
          <!-- 提供默认渲染，防止未定义插槽时单元格为空 -->
          <span>{{ record[column.dataIndex] }}</span>
        </slot>
      </template>

      <!-- 支持可展开行的插槽 -->
      <template v-if="props.expandable && props.expandable.expandedRowRender" #expandedRowRender="{ record }">
        <component :is="props.expandable.expandedRowRender(record)" />
      </template>
    </a-table>
  </PageContainer>
</template>

<script setup>
import {computed, defineProps} from 'vue';
import {useTable} from '@/composables/useTable';
import {useTableConfigStore} from '@/stores/tableConfig';

import PageContainer from '@/components/PageContainer/index.vue';
import SearchForm from '@/components/SearchForm/index.vue'; // 确保路径正确

// 定义组件的 Props
const props = defineProps({
  api: {type: Object, required: true},
  columns: {type: Array, required: true},
  rowKey: {type: String, required: true},
  searchFields: {type: Array, default: () => []},
  initialSearchParams: {type: Object, default: () => ({})},
  exportConfig: {type: Object, default: null}, // { url: '...', fileName: '...' }
  showSelection: {type: Boolean, default: true},
  // 树形表格支持
  isTreeTable: {type: Boolean, default: false},
  // 可展开行支持
  expandable: {type: Object, default: null},
  // 自定义行选择配置
  rowSelection: {type: Object, default: null},
});

const tableConfigStore = useTableConfigStore();

// 使用 useTable Hook
const {
  searchFormData,
  tableState,
  pagination,
  rowSelection,
  queryTableData,
  handleSearch,
  handleReset,
  handleTableChange,
  handleDelete,
  handleExport,
} = useTable({
  api: {
    list: props.api.list,
    delete: props.api.delete,
    exportUrl: props.exportConfig?.url,
    processListData: props.api.processListData,
  },
  columns: props.columns,
  initialSearchParams: props.initialSearchParams,
  rowKey: props.rowKey,
  exportFileName: props.exportConfig?.fileName,
  isTreeTable: props.isTreeTable,
});

// 计算最终的 rowSelection 配置
const finalRowSelectionConfig = computed(() => {
  // 如果传入了自定义的 rowSelection 配置，优先使用
  if (props.rowSelection) {
    return {
      ...rowSelection.value,
      ...props.rowSelection,
    };
  }

  // 否则使用默认配置
  if (!props.showSelection || !tableConfigStore.rowSelection) return undefined;
  return rowSelection.value;
});

// 暴露方法给父组件
defineExpose({
  refresh: queryTableData,
  delete: handleDelete,
  export: handleExport,
});
</script>