<template>
  <a-drawer
    :open="visible"
    @update:open="$emit('update:visible', $event)"
    title="消息中心"
    placement="right"
    width="400"
    class="message-center-drawer"
  >
    <div class="message-container">
      <!-- 消息类型切换 -->
      <a-tabs v-model:activeKey="activeTab" class="message-tabs">
        <a-tab-pane key="notification" tab="通知">
          <template #tab>
            <a-badge :count="notificationCount" size="small">
              <BellOutlined />
              通知
            </a-badge>
          </template>
        </a-tab-pane>
        <a-tab-pane key="message" tab="私信">
          <template #tab>
            <a-badge :count="messageCount" size="small">
              <MessageOutlined />
              私信
            </a-badge>
          </template>
        </a-tab-pane>
        <a-tab-pane key="todo" tab="待办">
          <template #tab>
            <a-badge :count="todoCount" size="small">
              <CheckCircleOutlined />
              待办
            </a-badge>
          </template>
        </a-tab-pane>
      </a-tabs>

      <!-- 消息列表 -->
      <div class="message-list">
        <!-- 通知消息 -->
        <div v-if="activeTab === 'notification'" class="message-content">
          <div
            v-for="item in notifications"
            :key="item.id"
            class="message-item"
            :class="{ 'unread': !item.read }"
            @click="handleMessageClick(item)"
          >
            <div class="message-avatar">
              <a-avatar :style="{ backgroundColor: item.color }">
                <template #icon>
                  <component :is="item.icon" />
                </template>
              </a-avatar>
            </div>
            <div class="message-content-body">
              <div class="message-title">{{ item.title }}</div>
              <div class="message-desc">{{ item.description }}</div>
              <div class="message-time">{{ item.time }}</div>
            </div>
            <div class="message-status" v-if="!item.read">
              <div class="unread-dot"></div>
            </div>
          </div>
        </div>

        <!-- 私信消息 -->
        <div v-if="activeTab === 'message'" class="message-content">
          <div
            v-for="item in messages"
            :key="item.id"
            class="message-item"
            :class="{ 'unread': !item.read }"
            @click="handleMessageClick(item)"
          >
            <div class="message-avatar">
              <a-avatar :src="item.avatar">
                {{ item.name.charAt(0) }}
              </a-avatar>
            </div>
            <div class="message-content-body">
              <div class="message-title">{{ item.name }}</div>
              <div class="message-desc">{{ item.content }}</div>
              <div class="message-time">{{ item.time }}</div>
            </div>
            <div class="message-status" v-if="!item.read">
              <div class="unread-dot"></div>
            </div>
          </div>
        </div>

        <!-- 待办事项 -->
        <div v-if="activeTab === 'todo'" class="message-content">
          <div
            v-for="item in todos"
            :key="item.id"
            class="message-item"
            :class="{ 'urgent': item.urgent }"
            @click="handleTodoClick(item)"
          >
            <div class="message-avatar">
              <a-avatar :style="{ backgroundColor: item.urgent ? '#ff4d4f' : '#52c41a' }">
                <template #icon>
                  <ClockCircleOutlined v-if="item.urgent" />
                  <CheckCircleOutlined v-else />
                </template>
              </a-avatar>
            </div>
            <div class="message-content-body">
              <div class="message-title">{{ item.title }}</div>
              <div class="message-desc">{{ item.description }}</div>
              <div class="message-time">截止时间：{{ item.deadline }}</div>
            </div>
            <div class="message-actions">
              <a-button size="small" type="primary" @click.stop="completeTodo(item)">
                完成
              </a-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作 -->
      <div class="message-footer">
        <a-button type="link" @click="markAllAsRead">全部标记为已读</a-button>
        <a-button type="link" @click="clearAll">清空消息</a-button>
      </div>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, computed } from 'vue';
import {
  BellOutlined,
  MessageOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  UserOutlined,
  WarningOutlined,
  InfoCircleOutlined
} from '@ant-design/icons-vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible']);

// 当前选中的标签
const activeTab = ref('notification');

// 通知消息
const notifications = ref([
  {
    id: 1,
    title: '系统更新通知',
    description: '系统将于今晚22:00进行维护更新，预计耗时2小时',
    time: '2小时前',
    read: false,
    icon: InfoCircleOutlined,
    color: '#1890ff'
  },
  {
    id: 2,
    title: '安全警告',
    description: '检测到异常登录行为，请及时检查账户安全',
    time: '5小时前',
    read: false,
    icon: WarningOutlined,
    color: '#ff4d4f'
  },
  {
    id: 3,
    title: '新功能上线',
    description: '数据导出功能已上线，支持多种格式导出',
    time: '1天前',
    read: true,
    icon: InfoCircleOutlined,
    color: '#52c41a'
  }
]);

// 私信消息
const messages = ref([
  {
    id: 1,
    name: '张三',
    content: '请帮忙审核一下用户权限申请',
    time: '10分钟前',
    read: false,
    avatar: ''
  },
  {
    id: 2,
    name: '李四',
    content: '明天的会议时间改到下午3点',
    time: '1小时前',
    read: false,
    avatar: ''
  },
  {
    id: 3,
    name: '王五',
    content: '数据报表已经准备好了',
    time: '3小时前',
    read: true,
    avatar: ''
  }
]);

// 待办事项
const todos = ref([
  {
    id: 1,
    title: '审核用户申请',
    description: '有3个新用户申请待审核',
    deadline: '今天 18:00',
    urgent: true
  },
  {
    id: 2,
    title: '月度报告',
    description: '准备本月运营数据报告',
    deadline: '明天 12:00',
    urgent: false
  },
  {
    id: 3,
    title: '系统备份',
    description: '执行数据库定期备份',
    deadline: '后天 09:00',
    urgent: false
  }
]);

// 计算各类消息数量
const notificationCount = computed(() => notifications.value.filter(item => !item.read).length);
const messageCount = computed(() => messages.value.filter(item => !item.read).length);
const todoCount = computed(() => todos.value.length);

// 处理消息点击
const handleMessageClick = (item) => {
  item.read = true;
  console.log('点击消息:', item);
};

// 处理待办点击
const handleTodoClick = (item) => {
  console.log('点击待办:', item);
};

// 完成待办
const completeTodo = (item) => {
  const index = todos.value.findIndex(todo => todo.id === item.id);
  if (index > -1) {
    todos.value.splice(index, 1);
  }
};

// 全部标记为已读
const markAllAsRead = () => {
  if (activeTab.value === 'notification') {
    notifications.value.forEach(item => item.read = true);
  } else if (activeTab.value === 'message') {
    messages.value.forEach(item => item.read = true);
  }
};

// 清空消息
const clearAll = () => {
  if (activeTab.value === 'notification') {
    notifications.value = [];
  } else if (activeTab.value === 'message') {
    messages.value = [];
  } else if (activeTab.value === 'todo') {
    todos.value = [];
  }
};
</script>

<style scoped>
.message-center-drawer {
  .message-container {
    height: 100%;
    display: flex;
    flex-direction: column;

    .message-tabs {
      margin-bottom: 16px;
    }

    .message-list {
      flex: 1;
      overflow-y: auto;

      .message-content {
        .message-item {
          display: flex;
          align-items: flex-start;
          padding: 16px 0;
          border-bottom: 1px solid #f0f0f0;
          cursor: pointer;
          transition: background 0.3s;

          &:hover {
            background: #fafafa;
          }

          &.unread {
            background: #f6ffed;
          }

          &.urgent {
            border-left: 3px solid #ff4d4f;
            padding-left: 13px;
          }

          .message-avatar {
            margin-right: 12px;
          }

          .message-content-body {
            flex: 1;

            .message-title {
              font-weight: 500;
              margin-bottom: 4px;
              color: #333;
            }

            .message-desc {
              color: #666;
              font-size: 14px;
              margin-bottom: 8px;
              line-height: 1.4;
            }

            .message-time {
              color: #999;
              font-size: 12px;
            }
          }

          .message-status {
            .unread-dot {
              width: 8px;
              height: 8px;
              background: #ff4d4f;
              border-radius: 50%;
            }
          }

          .message-actions {
            margin-left: 8px;
          }
        }
      }
    }

    .message-footer {
      padding: 16px 0;
      border-top: 1px solid #f0f0f0;
      text-align: center;

      .ant-btn {
        margin: 0 8px;
      }
    }
  }
}
</style>
