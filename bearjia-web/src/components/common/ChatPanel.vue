<template>
  <a-drawer
      :open="visible"
      @update:open="$emit('update:visible', $event)"
      title="在线聊天"
      placement="right"
      width="400"
      class="chat-panel-drawer"
  >
    <div class="chat-container">
      <!-- 联系人列表 -->
      <div class="contact-list" v-if="!currentChat">
        <div class="contact-search">
          <a-input
              style="height: 36px"
              v-model:value="searchKeyword"
              placeholder="搜索联系人..."
              size="small"
          >
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
        </div>

        <div class="contact-items">
          <div
              v-for="contact in filteredContacts"
              :key="contact.id"
              class="contact-item"
              @click="startChat(contact)"
          >
            <div class="contact-avatar">
              <div class="avatar-wrapper">
                <a-avatar
                  :src="contact.avatar"
                  :size="40"
                  :style="contact.isAi ? { backgroundColor: '#1890ff' } : {}"
                >
                  <RobotOutlined v-if="contact.isAi" />
                  <template v-else>{{ contact.name.charAt(0) }}</template>
                </a-avatar>
                <div v-if="contact.online" class="online-indicator"></div>
              </div>
            </div>
            <div class="contact-info">
              <div class="contact-main">
                <div class="contact-name">
                  {{ contact.name }}
                  <a-tag v-if="contact.isAi" color="blue" size="small">AI</a-tag>
                </div>
                <div class="contact-time">
                  {{ contact.lastMessageTime || '刚刚' }}
                </div>
              </div>
              <div class="contact-detail">
                <div class="contact-status">
                  {{ contact.isAi ? contact.description : getLastMessage(contact.id) }}
                </div>
                <div class="contact-unread" v-if="contact.unreadCount > 0">
                  <a-badge :count="contact.unreadCount" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 聊天界面 -->
      <div class="chat-interface" v-if="currentChat">
        <!-- 聊天头部 -->
        <div class="chat-header">
          <div class="header-left">
            <a-button type="text" @click="backToContacts" class="back-btn">
              <ArrowLeftOutlined />
            </a-button>
            <div class="chat-title">
              <div class="chat-name">
                {{ currentChat.name }}
              </div>
              <div class="chat-status">
                <span class="status-dot" :class="{ 'online': currentChat.online }"></span>
                {{ currentChat.isAi ? '智能助手' : (currentChat.online ? '在线' : '离线') }}
              </div>
            </div>
          </div>
          <div class="header-right">
            <a-button type="text" class="header-action-btn">
              <template #icon>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
                </svg>
              </template>
            </a-button>
          </div>
        </div>

        <!-- 消息列表 -->
        <div class="message-list" ref="messageListRef">
          <div
              v-for="message in currentMessages"
              :key="message.id"
              class="message-item"
              :class="{ 'own-message': message.isSelf }"
          >
            <!-- 对方消息 -->
            <div v-if="!message.isSelf" class="message-wrapper left-message">
              <div class="message-avatar">
                <a-avatar :src="currentChat.avatar" :size="32" :style="message.isAi ? { backgroundColor: '#1890ff' } : {}">
                  <RobotOutlined v-if="message.isAi" />
                  <template v-else>{{ currentChat.name.charAt(0) }}</template>
                </a-avatar>
              </div>
              <div class="message-content">
                <div class="message-header">
                  <span class="sender-name">{{ currentChat.name }}</span>
                  <span class="message-time">{{ message.time }}</span>
                </div>
                <div class="message-bubble left-bubble">
                  <div class="message-text" v-if="!message.isAi || !hasMarkdownContent(message.content)">
                    {{ message.content }}
                  </div>
                  <div class="message-text markdown-content" v-else v-html="renderMarkdown(message.content)"></div>
                </div>
              </div>
            </div>

            <!-- 自己的消息 -->
            <div v-else class="message-wrapper right-message">
              <div class="message-content">
                <div class="message-header">
                  <span class="message-time">{{ message.time }}</span>
                  <span class="sender-name">Ricky</span>
                </div>
                <div class="message-bubble right-bubble">
                  <div class="message-text">{{ message.content }}</div>
                </div>
              </div>
              <div class="message-avatar">
                <a-avatar src="/profile.jpg" :size="32">
                  我
                </a-avatar>
              </div>
            </div>
          </div>

          <!-- 流式消息显示 -->
          <div v-if="streamingMessage && isAiChat" class="message-item">
            <div class="message-avatar">
              <a-avatar size="small" style="background-color: #1890ff">
                <RobotOutlined />
              </a-avatar>
            </div>
            <div class="message-content">
              <div class="message-bubble streaming-message">
                <div class="message-text markdown-content" v-html="renderMarkdown(streamingMessage.content)"></div>
                <div class="streaming-cursor">|</div>
                <div class="message-time">{{ streamingMessage.time }}</div>
              </div>
            </div>
          </div>



          <!-- AI加载状态 -->
          <div v-if="aiLoading && isAiChat && !isStreaming" class="message-item">
            <div class="message-avatar">
              <a-avatar size="small" style="background-color: #1890ff">
                <RobotOutlined />
              </a-avatar>
            </div>
            <div class="message-content">
              <div class="message-bubble ai-loading">
                <div class="ai-thinking">
                  <a-spin size="small" />
                  <span style="margin-left: 8px;">AI正在思考中...</span>
                </div>
                <div class="message-time">{{ new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }) }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 输入框 -->
        <div class="message-input">
          <div class="input-container">
            <div class="input-left">
              <a-button type="text" class="attachment-btn">
                <template #icon>
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M16.5 6v11.5c0 2.21-1.79 4-4 4s-4-1.79-4-4V5c0-1.38 1.12-2.5 2.5-2.5s2.5 1.12 2.5 2.5v10.5c0 .55-.45 1-1 1s-1-.45-1-1V6H10v9.5c0 1.38 1.12 2.5 2.5 2.5s2.5-1.12 2.5-2.5V5c0-2.21-1.79-4-4-4S7 2.79 7 5v12.5c0 3.04 2.46 5.5 5.5 5.5s5.5-2.46 5.5-5.5V6h-1.5z"/>
                  </svg>
                </template>
              </a-button>
            </div>
            <div class="input-main">
              <a-input
                  v-model:value="messageInput"
                  :placeholder="isAiChat ? '输入消息...' : '输入消息...'"
                  :disabled="aiLoading || isStreaming"
                  @keydown="handleKeyDown"
                  class="message-input-field"
                  :bordered="false"
              />
            </div>
            <div class="input-right">
              <!-- 发送按钮 -->
              <a-button
                  v-if="!isStreaming && !aiLoading"
                  type="text"
                  @click="sendMessage"
                  :disabled="!messageInput.trim()"
                  class="send-btn"
                  shape="circle"
              >
                <template #icon>
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                  </svg>
                </template>
              </a-button>

              <!-- AI加载状态 -->
              <a-button
                  v-else-if="aiLoading && !isStreaming"
                  type="text"
                  class="send-btn loading-btn"
                  shape="circle"
                  disabled
              >
                <template #icon>
                  <a-spin size="small" />
                </template>
              </a-button>

              <!-- 停止按钮 -->
              <a-button
                  v-else-if="isStreaming"
                  type="text"
                  @click="cancelStream"
                  class="send-btn stop-btn"
                  shape="circle"
              >
                <template #icon>
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M6 6h12v12H6z"/>
                  </svg>
                </template>
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, computed, nextTick, watch } from 'vue';
import { message, Modal } from 'ant-design-vue';
import {
  SearchOutlined,
  ArrowLeftOutlined,
  SmileOutlined,
  PaperClipOutlined,
  PictureOutlined,
  RobotOutlined,
  LoadingOutlined
} from '@ant-design/icons-vue';
import { chatWithAi, chatWithAiStream, chatWithAiStreamFetch, executeAiFunction } from '@/api/ai';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible']);

// 搜索关键词
const searchKeyword = ref('');
const messageInput = ref('');
const currentChat = ref(null);
const messageListRef = ref(null);

// AI聊天相关状态
const isAiChat = ref(false);
const aiLoading = ref(false);
const conversationHistory = ref([]);
const pendingConfirmation = ref(null);

// 流式处理相关状态
const streamingMessage = ref(null); // 当前正在流式接收的消息
const streamCancelFn = ref(null); // 取消流式请求的函数
const isStreaming = ref(false); // 是否正在流式接收

// 联系人列表
const contacts = ref([
  {
    id: 'ai-assistant',
    name: 'AI智能助手',
    avatar: '',
    online: true,
    unreadCount: 0,
    isAi: true,
    description: '智能AI助手，可以帮您处理各种管理任务',
    lastMessageTime: '刚刚'
  },
  {
    id: 1,
    name: '张三',
    avatar: '',
    online: true,
    unreadCount: 2,
    lastMessageTime: '20小时前'
  },
  {
    id: 2,
    name: '李四',
    avatar: '',
    online: false,
    unreadCount: 0,
    lastMessageTime: '2周前'
  },
  {
    id: 3,
    name: '王五',
    avatar: '',
    online: true,
    unreadCount: 1,
    lastMessageTime: '5小时前'
  },
  {
    id: 4,
    name: '赵六',
    avatar: '',
    online: true,
    unreadCount: 0,
    lastMessageTime: '3天前'
  }
]);

// 聊天消息
const chatMessages = ref({
  'ai-assistant': [
    {
      id: 1,
      content: '您好！我是AI智能助手，可以帮您处理各种管理任务。请问有什么可以帮助您的吗？',
      time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
      isSelf: false,
      isAi: true
    }
  ],
  1: [
    {
      id: 1,
      content: '你好，有个问题想请教一下',
      time: '14:30',
      isSelf: false
    },
    {
      id: 2,
      content: '好的，什么问题？',
      time: '14:31',
      isSelf: true
    },
    {
      id: 3,
      content: '关于用户权限的配置',
      time: '14:32',
      isSelf: false
    }
  ],
  3: [
    {
      id: 1,
      content: '会议时间确定了吗？',
      time: '15:00',
      isSelf: false
    }
  ]
});

// 过滤联系人
const filteredContacts = computed(() => {
  if (!searchKeyword.value) {
    return contacts.value;
  }
  return contacts.value.filter(contact =>
      contact.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
  );
});

// 当前聊天消息
const currentMessages = computed(() => {
  if (!currentChat.value) return [];
  return chatMessages.value[currentChat.value.id] || [];
});

// 获取最后一条消息
const getLastMessage = (contactId) => {
  const messages = chatMessages.value[contactId];
  if (!messages || messages.length === 0) return '暂无消息';

  const lastMessage = messages[messages.length - 1];
  const content = lastMessage.content;

  // 如果消息太长，截断显示
  if (content.length > 30) {
    return content.substring(0, 30) + '...';
  }

  return content;
};

// 开始聊天
const startChat = (contact) => {
  currentChat.value = contact;
  contact.unreadCount = 0;
  isAiChat.value = contact.isAi || false;

  // 如果是AI聊天，初始化对话历史
  if (isAiChat.value) {
    conversationHistory.value = convertToAiFormat(chatMessages.value[contact.id] || []);
  }

  nextTick(() => {
    scrollToBottom();
  });
};

// 返回联系人列表
const backToContacts = () => {
  // 如果正在流式接收，先取消
  if (isStreaming.value) {
    cancelStream();
  }

  currentChat.value = null;
  isAiChat.value = false;
  aiLoading.value = false;
  pendingConfirmation.value = null;

  // 清理流式状态
  streamingMessage.value = null;
  streamCancelFn.value = null;
  isStreaming.value = false;
};

// 处理键盘事件
const handleKeyDown = (event) => {
  if (event.key === 'Enter') {
    if (event.shiftKey) {
      // Shift+Enter：换行，不阻止默认行为
      return;
    } else {
      // Enter：发送消息
      event.preventDefault();
      sendMessage();
    }
  }
};

// 发送消息
const sendMessage = async () => {
  if (!messageInput.value.trim() || !currentChat.value || aiLoading.value) return;

  const userMessage = {
    id: Date.now(),
    content: messageInput.value.trim(),
    time: new Date().toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    }),
    isSelf: true
  };

  if (!chatMessages.value[currentChat.value.id]) {
    chatMessages.value[currentChat.value.id] = [];
  }

  chatMessages.value[currentChat.value.id].push(userMessage);
  const currentInput = messageInput.value.trim();
  messageInput.value = '';

  nextTick(() => {
    scrollToBottom();
  });

  // 如果是AI聊天，调用AI接口
  if (isAiChat.value) {
    await sendAiMessage(currentInput);
  } else {
    // 普通聊天，模拟对方回复
    setTimeout(() => {
      const replyMessage = {
        id: Date.now() + 1,
        content: '收到，我来处理一下',
        time: new Date().toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit'
        }),
        isSelf: false
      };
      chatMessages.value[currentChat.value.id].push(replyMessage);
      nextTick(() => {
        scrollToBottom();
      });
    }, 1000);
  }
};

// AI聊天相关方法
const sendAiMessage = async (userInput) => {
  aiLoading.value = true;
  isStreaming.value = true;

  try {
    // 更新对话历史
    conversationHistory.value = convertToAiFormat(chatMessages.value[currentChat.value.id]);

    // 初始化流式消息
    streamingMessage.value = {
      id: Date.now() + Math.random(),
      content: '',
      time: new Date().toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      }),
      isSelf: false,
      isAi: true
    };

    // 使用流式接口
    console.log('开始发送AI消息:', userInput);

    const cancelFn = chatWithAiStreamFetch({
      message: userInput,
      conversationHistory: conversationHistory.value.slice(0, -1) // 排除刚添加的用户消息
    }, {
      onMessage: (data) => {
        if (data.type === 'start') {
          // 开始接收消息
          streamingMessage.value.messageId = data.messageId;
          aiLoading.value = false; // 开始接收后取消加载状态
          console.log('开始接收流式消息，messageId:', data.messageId);
        } else if (data.type === 'chunk') {
          // 接收消息片段，逐字更新
          if (streamingMessage.value) {
            streamingMessage.value.content = data.fullContent;
            nextTick(() => {
              scrollToBottom();
            });
          }
        }
        // 注意：其他类型的消息（如message事件）会在ai.js中的handleSseEvent函数中
        // 被转换为chunk事件，所以这里不需要额外处理
      },
      onError: (error) => {
        console.error('流式聊天错误:', error);
        // 清除流式消息
        streamingMessage.value = null;
        isStreaming.value = false;
        aiLoading.value = false;

        // 根据错误类型显示不同的消息
        let errorMessage = '抱歉，网络连接出现问题，请稍后重试。';
        let notificationMessage = '网络错误，请稍后重试';

        if (typeof error === 'string') {
          // 如果是字符串错误，可能是服务器返回的具体错误信息
          if (error.includes('没有') || error.includes('功能') || error.includes('抱歉') || error.includes('系统中')) {
            // 这是AI返回的功能缺失提示或其他说明
            errorMessage = error;
            notificationMessage = '系统提示';
          } else if (error.includes('权限')) {
            errorMessage = error;
            notificationMessage = '权限不足';
          } else if (error.includes('超时')) {
            errorMessage = '请求处理超时，请稍后重试。';
            notificationMessage = '请求超时';
          } else if (error.includes('HTTP error') || error.includes('网络')) {
            errorMessage = '网络连接出现问题，请检查网络后重试。';
            notificationMessage = '网络错误';
          } else {
            // 其他错误直接显示
            errorMessage = error;
            notificationMessage = '操作失败';
          }
        }

        addAiMessage(errorMessage);
        message.error(notificationMessage);
      },
      onComplete: (data) => {
        console.log('流式接收完成:', data);

        // 流式接收完成，将消息添加到聊天记录
        if (streamingMessage.value) {
          const finalMessage = {
            ...streamingMessage.value,
            content: data.fullContent
          };

          chatMessages.value[currentChat.value.id].push(finalMessage);

          // 更新对话历史
          if (data.conversationHistory) {
            conversationHistory.value = data.conversationHistory;
          }
        }

        // 清理状态
        streamingMessage.value = null;
        isStreaming.value = false;
        aiLoading.value = false;
        streamCancelFn.value = null;
        messageInput.value = '';


        nextTick(() => {
          scrollToBottom();
        });
      }
    });

    // 保存取消函数
    streamCancelFn.value = cancelFn;

  } catch (error) {
    console.error('AI聊天错误:', error);

    // 清理状态
    streamingMessage.value = null;
    isStreaming.value = false;
    aiLoading.value = false;
    streamCancelFn.value = null;

    addAiMessage('抱歉，网络连接出现问题，请稍后重试。');
    message.error('网络错误，请稍后重试');

    nextTick(() => {
      scrollToBottom();
    });
  }
};

// 取消流式请求
const cancelStream = () => {
  if (streamCancelFn.value) {
    streamCancelFn.value();
    streamCancelFn.value = null;
  }

  // 如果有正在流式接收的消息，将其添加到聊天记录
  if (streamingMessage.value && streamingMessage.value.content.trim()) {
    chatMessages.value[currentChat.value.id].push({
      ...streamingMessage.value,
      content: streamingMessage.value.content + ' [已停止生成]'
    });
  }

  // 清理状态
  streamingMessage.value = null;
  isStreaming.value = false;
  aiLoading.value = false;

  message.info('已停止生成');

  nextTick(() => {
    scrollToBottom();
  });
};

// 添加AI消息到聊天记录
const addAiMessage = (content) => {
  const aiMessage = {
    id: Date.now() + Math.random(),
    content: content,
    time: new Date().toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    }),
    isSelf: false,
    isAi: true
  };

  chatMessages.value[currentChat.value.id].push(aiMessage);
};

// 转换为AI格式的对话历史
const convertToAiFormat = (messages) => {
  return messages.map(msg => ({
    role: msg.isSelf ? 'user' : 'assistant',
    content: msg.content,
    timestamp: new Date().toISOString()
  }));
};

// 更新聊天消息（从AI响应的对话历史）
const updateChatMessages = (aiHistory) => {
  // 不要完全替换消息数组，这会导致用户消息丢失
  // 只有在对话历史比当前消息多的情况下才更新
  const currentMessages = chatMessages.value[currentChat.value.id] || [];

  if (aiHistory.length > currentMessages.length) {
    const newMessages = aiHistory.map((msg, index) => ({
      id: Date.now() + index,
      content: msg.content,
      time: new Date().toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      }),
      isSelf: msg.role === 'user',
      isAi: msg.role === 'assistant'
    }));

    chatMessages.value[currentChat.value.id] = newMessages;
  }
};

// 显示确认对话框
const showConfirmationDialog = () => {
  if (!pendingConfirmation.value) return;

  const { confirmationMessage, functionName, arguments: args } = pendingConfirmation.value;

  // 使用Ant Design的确认对话框
  const modal = Modal.confirm({
    title: '操作确认',
    content: confirmationMessage,
    okText: '确认执行',
    cancelText: '取消',
    onOk: async () => {
      await executeConfirmedAction();
    },
    onCancel: () => {
      addAiMessage('操作已取消。');
      pendingConfirmation.value = null;
    }
  });
};

// 执行确认的操作
const executeConfirmedAction = async () => {
  if (!pendingConfirmation.value) return;

  aiLoading.value = true;

  try {
    const response = await executeAiFunction({
      functionName: pendingConfirmation.value.functionName,
      arguments: pendingConfirmation.value.arguments,
      conversationHistory: conversationHistory.value
    });

    if (response.code === 200) {
      const aiResponse = response.data;

      if (aiResponse.type === 'ERROR') {
        addAiMessage('执行操作时发生错误：' + (aiResponse.errorMessage || '未知错误'));
        message.error(aiResponse.errorMessage || '执行操作时发生错误');
      } else {
        addAiMessage(aiResponse.content || '操作执行成功！');
        message.success('操作执行成功');
      }

      // 更新对话历史
      if (aiResponse.conversationHistory) {
        conversationHistory.value = aiResponse.conversationHistory;
        updateChatMessages(aiResponse.conversationHistory);
      }
    } else {
      addAiMessage('执行操作失败：' + (response.msg || '未知错误'));
      message.error(response.msg || '执行失败');
    }
  } catch (error) {
    console.error('执行AI函数错误:', error);
    addAiMessage('执行操作时发生网络错误，请稍后重试。');
    message.error('执行失败，请稍后重试');
  } finally {
    aiLoading.value = false;
    pendingConfirmation.value = null;
    nextTick(() => {
      scrollToBottom();
    });
  }
};

// 滚动到底部
const scrollToBottom = () => {
  if (messageListRef.value) {
    messageListRef.value.scrollTop = messageListRef.value.scrollHeight;
  }
};

// 检测是否包含Markdown内容
const hasMarkdownContent = (content) => {
  if (!content) return false;

  // 检测常见的Markdown语法
  const markdownPatterns = [
    /\*\*(.*?)\*\*/,     // 粗体 **text**
    /^#{1,6}\s/m,        // 标题 # ## ###
    /^-\s/m,             // 无序列表 - item
    /^\d+\.\s/m,         // 有序列表 1. item
    /^>\s/m,             // 引用 > text
    /```[\s\S]*?```/,    // 代码块
    /`[^`]+`/,           // 行内代码
    /\[.*?\]\(.*?\)/,    // 链接 [text](url)
    /!\[.*?\]\(.*?\)/,   // 图片 ![alt](url)
    /[📢📝✅❌⚠️📊👤🏢👑🎉🚀💡🔧📋🎯🔍🌟]/  // 表情符号
  ];

  return markdownPatterns.some(pattern => pattern.test(content));
};

// 优化的Markdown渲染
const renderMarkdown = (text) => {
  if (!text) return '';

  // 预处理：保护代码块和链接
  const codeBlocks = [];
  let html = text
      // 保护代码块
      .replace(/```([\s\S]*?)```/g, (match, code) => {
        const index = codeBlocks.length;
        codeBlocks.push(`<pre class="md-code-block"><code>${code.trim()}</code></pre>`);
        return `__CODE_BLOCK_${index}__`;
      })
      // 转义HTML特殊字符
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      // 标题（支持更多级别）
      .replace(/^#### (.*$)/gim, '<h4 class="md-h4">$1</h4>')
      .replace(/^### (.*$)/gim, '<h3 class="md-h3">$1</h3>')
      .replace(/^## (.*$)/gim, '<h2 class="md-h2">$1</h2>')
      .replace(/^# (.*$)/gim, '<h1 class="md-h1">$1</h1>')
      // 粗体和斜体
      .replace(/\*\*\*(.*?)\*\*\*/g, '<strong><em class="md-italic">$1</em></strong>')
      .replace(/\*\*(.*?)\*\*/g, '<strong class="md-bold">$1</strong>')
      .replace(/\*(.*?)\*/g, '<em class="md-italic">$1</em>')
      // 行内代码
      .replace(/`([^`]+)`/g, '<code class="md-code">$1</code>')
      // 链接
      .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="md-link" target="_blank" rel="noopener">$1</a>')
      // 列表项（支持数字列表）
      .replace(/^\d+\. (.*$)/gim, '<li class="md-li md-li-ordered">$1</li>')
      .replace(/^- (.*$)/gim, '<li class="md-li md-li-unordered">$1</li>')
      // 引用
      .replace(/^> (.*$)/gim, '<blockquote class="md-quote">$1</blockquote>')
      // 分隔线
      .replace(/^---$/gim, '<hr class="md-hr">')
      // 特殊格式：AI回复中的标签格式
      .replace(/📢\s*\*\*(.*?)\*\*/g, '<div class="ai-notice-title"><span class="ai-emoji">📢</span><strong>$1</strong></div>')
      .replace(/📝\s*\*\*(.*?)\*\*/g, '<div class="ai-content-title"><span class="ai-emoji">📝</span><strong>$1</strong></div>')
      .replace(/✅\s*(.*?)(?=\n|$)/g, '<div class="ai-success"><span class="ai-emoji">✅</span>$1</div>')
      // 换行处理
      .replace(/\n\n/g, '<div class="md-paragraph-break"></div>')
      .replace(/\n/g, '<br>')
      // 表情符号
      .replace(/([✅❌⚠️📊👤🏢👑🎉🚀💡🔧📋🎯🔍📝💻🌟])/g, '<span class="md-emoji">$1</span>');

  // 处理列表包装
  html = html.replace(/(<li class="md-li md-li-unordered">.*?<\/li>)(<br>(<li class="md-li md-li-unordered">.*?<\/li>))*(<br>)?/gs, (match) => {
    const items = match.replace(/<br>/g, '').trim();
    return `<ul class="md-ul">${items}</ul>`;
  });

  html = html.replace(/(<li class="md-li md-li-ordered">.*?<\/li>)(<br>(<li class="md-li md-li-ordered">.*?<\/li>))*(<br>)?/gs, (match) => {
    const items = match.replace(/<br>/g, '').trim();
    return `<ol class="md-ol">${items}</ol>`;
  });

  // 恢复代码块
  codeBlocks.forEach((block, index) => {
    html = html.replace(`__CODE_BLOCK_${index}__`, block);
  });

  return html;
};

// 监听抽屉打开，重置状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    currentChat.value = null;
    searchKeyword.value = '';
    messageInput.value = '';
  } else {
    // 抽屉关闭时，清理流式状态
    if (isStreaming.value) {
      cancelStream();
    }
  }
});
</script>

<style scoped>
.chat-panel-drawer {
  .chat-container {
    height: 100%;
    display: flex;
    flex-direction: column;

    .contact-list {
      height: 100%;
      display: flex;
      flex-direction: column;

      .contact-search {
        margin-bottom: 16px;
      }

      .contact-items {
        flex: 1;
        overflow-y: auto;

        .contact-item {
          display: flex;
          align-items: center;
          padding: 8px 12px;
          cursor: pointer;
          transition: background-color 0.2s ease;
          border-radius: 6px;
          margin-bottom: 2px;

          &:hover {
            background-color: #f5f5f5;
          }

          &:first-child {
            background: linear-gradient(135deg, #f0f8ff 0%, #e6f7ff 100%);
            margin-bottom: 8px;

            &:hover {
              background: linear-gradient(135deg, #e6f7ff 0%, #d6f7ff 100%);
            }
          }

          .contact-avatar {
            margin-right: 12px;
            position: relative;

            .avatar-wrapper {
              position: relative;
              display: inline-block;

              .online-indicator {
                position: absolute;
                bottom: 2px;
                right: 2px;
                width: 10px;
                height: 10px;
                background-color: #52c41a;
                border: 2px solid #fff;
                border-radius: 50%;
                box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
              }
            }
          }

          .contact-info {
            flex: 1;
            min-width: 0; /* 防止文本溢出 */

            .contact-main {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 4px;

              .contact-name {
                font-weight: 500;
                font-size: 14px;
                color: #262626;
                display: flex;
                align-items: center;
                gap: 6px;
              }

              .contact-time {
                font-size: 12px;
                color: #8c8c8c;
                white-space: nowrap;
              }
            }

            .contact-detail {
              display: flex;
              justify-content: space-between;
              align-items: center;

              .contact-status {
                font-size: 12px;
                color: #8c8c8c;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                flex: 1;
                margin-right: 8px;
              }

              .contact-unread {
                flex-shrink: 0;
              }
            }
          }
        }
      }
    }

    .chat-interface {
      height: 100%;
      display: flex;
      flex-direction: column;
      background: linear-gradient(180deg, #fafbfc 0%, #f8f9fa 100%);

      .chat-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        border-bottom: 1px solid #f0f0f0;
        background: #ffffff;

        .header-left {
          display: flex;
          align-items: center;

          .back-btn {
            margin-right: 8px;
            color: #666;

            &:hover {
              color: #1890ff;
            }
          }

          .chat-title {
            .chat-name {
              font-size: 16px;
              font-weight: 500;
              color: #262626;
              margin-bottom: 2px;
            }

            .chat-status {
              font-size: 12px;
              color: #8c8c8c;
              display: flex;
              align-items: center;

              .status-dot {
                width: 6px;
                height: 6px;
                border-radius: 50%;
                background-color: #d9d9d9;
                margin-right: 4px;

                &.online {
                  background-color: #52c41a;
                }
              }
            }
          }
        }

        .header-right {
          .header-action-btn {
            color: #8c8c8c;

            &:hover {
              color: #1890ff;
            }
          }
        }
      }

      .message-list {
        flex: 1;
        overflow-y: auto;
        padding: 16px;
        background: #ffffff;

        .message-item {
          margin-bottom: 20px;

          .message-wrapper {
            display: flex;
            align-items: flex-start;
            gap: 8px;

            &.left-message {
              justify-content: flex-start;

              .message-content {
                max-width: 70%;

                .message-header {
                  display: flex;
                  align-items: center;
                  gap: 8px;
                  margin-bottom: 4px;

                  .sender-name {
                    font-size: 12px;
                    font-weight: 500;
                    color: #262626;
                  }

                  .message-time {
                    font-size: 11px;
                    color: #8c8c8c;
                  }
                }

                .left-bubble {
                  background: #f5f5f5;
                  border-radius: 0 12px 12px 12px;
                  padding: 8px 12px;
                  color: #262626;

                  .message-text {
                    word-break: break-word;
                    line-height: 1.4;
                    font-size: 14px;

                    &.markdown-content {
                      .md-h1, .md-h2, .md-h3, .md-h4 {
                        margin: 12px 0 6px 0;
                        font-weight: 600;
                        line-height: 1.3;
                      }

                      .md-h1 {
                        font-size: 18px;
                        color: #1890ff;
                        border-bottom: 2px solid #e8e8e8;
                        padding-bottom: 6px;
                      }

                      .md-h2 {
                        font-size: 16px;
                        color: #1890ff;
                        border-bottom: 1px solid #f0f0f0;
                        padding-bottom: 4px;
                      }

                      .md-h3 {
                        font-size: 14px;
                        color: #1890ff;
                      }

                      .md-h4 {
                        font-size: 13px;
                        color: #1890ff;
                      }

                      .md-bold {
                        font-weight: 600;
                        color: #262626;
                      }

                      .md-italic {
                        font-style: italic;
                        color: #595959;
                      }

                      .md-code {
                        background: #f6f8fa;
                        border: 1px solid #e1e4e8;
                        border-radius: 4px;
                        padding: 2px 6px;
                        font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
                        font-size: 12px;
                        color: #d73a49;
                        margin: 0 2px;
                      }

                      .md-code-block {
                        background: #f6f8fa;
                        border: 1px solid #e1e4e8;
                        border-radius: 6px;
                        padding: 12px;
                        margin: 8px 0;
                        overflow-x: auto;

                        code {
                          background: none;
                          border: none;
                          padding: 0;
                          margin: 0;
                          font-size: 13px;
                          color: #24292e;
                          white-space: pre;
                        }
                      }

                      .md-link {
                        color: #1890ff;
                        text-decoration: none;
                        border-bottom: 1px solid transparent;
                        transition: all 0.2s;

                        &:hover {
                          border-bottom-color: #1890ff;
                        }
                      }

                      .md-ul, .md-ol {
                        margin: 8px 0;
                        padding-left: 20px;

                        .md-li {
                          margin: 4px 0;
                          line-height: 1.5;

                          &.md-li-unordered {
                            list-style-type: disc;
                          }

                          &.md-li-ordered {
                            list-style-type: decimal;
                          }
                        }
                      }

                      .md-quote {
                        margin: 8px 0;
                        padding: 8px 12px;
                        background: #f9f9f9;
                        border-left: 4px solid #1890ff;
                        color: #666;
                        font-style: italic;
                      }

                      .md-hr {
                        margin: 16px 0;
                        border: none;
                        border-top: 1px solid #e8e8e8;
                      }

                      .md-paragraph-break {
                        height: 8px;
                      }

                      .md-emoji {
                        font-size: 16px;
                        margin-right: 4px;
                        display: inline-block;
                        vertical-align: middle;
                      }
                    }
                  }
                }
              }
            }

            &.right-message {
              justify-content: flex-end;

              .message-content {
                max-width: 70%;

                .message-header {
                  display: flex;
                  align-items: center;
                  justify-content: flex-end;
                  gap: 8px;
                  margin-bottom: 4px;

                  .message-time {
                    font-size: 11px;
                    color: #8c8c8c;
                  }

                  .sender-name {
                    font-size: 12px;
                    font-weight: 500;
                    color: #262626;
                  }
                }

                .right-bubble {
                  background: #4285f4;
                  border-radius: 12px 0 12px 12px;
                  padding: 8px 12px;
                  color: #ffffff;

                  .message-text {
                    word-break: break-word;
                    line-height: 1.4;
                    font-size: 14px;
                  }
                }
              }
            }
          }
        }

        /** AI加载状态样式 */
        .ai-loading {
          display: flex;
          align-items: center;
          background: #f0f2f5 !important;

          .typing-indicator {
            display: flex;
            align-items: center;
            margin-right: 8px;

            span {
              width: 4px;
              height: 4px;
              border-radius: 50%;
              background-color: #1890ff;
              margin: 0 1px;
              animation: typing 1.4s infinite ease-in-out;

              &:nth-child(1) {
                animation-delay: -0.32s;
              }

              &:nth-child(2) {
                animation-delay: -0.16s;
              }
            }
          }
        }

        @keyframes typing {
          0%, 80%, 100% {
            transform: scale(0.8);
            opacity: 0.5;
          }
          40% {
            transform: scale(1);
            opacity: 1;
          }
        }

        @keyframes pulse {
          0% {
            opacity: 1;
            transform: scale(1);
          }
          50% {
            opacity: 0.7;
            transform: scale(1.1);
          }
          100% {
            opacity: 1;
            transform: scale(1);
          }
        }

        @keyframes slideInUp {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .message-item {
          animation: slideInUp 0.3s ease-out;
        }

        /** AI加载状态样式 */
        .ai-loading {
          background: #f0f8ff !important;
          border: 1px solid #1890ff;
          color: #1890ff;
          animation: pulse 1.5s ease-in-out infinite;
        }

        /** 流式消息样式 */
        .streaming-message {
          background: linear-gradient(135deg, #f0f8ff 0%, #e6f7ff 100%) !important;
          border: 1px solid #1890ff;
          position: relative;

          .streaming-cursor {
            display: inline-block;
            width: 2px;
            height: 16px;
            background-color: #1890ff;
            margin-left: 2px;
            animation: blink 1s infinite;
            vertical-align: text-bottom;
          }
        }

        @keyframes pulse {
          0% { opacity: 1; }
          50% { opacity: 0.7; }
          100% { opacity: 1; }
        }

        @keyframes blink {
          0%, 50% { opacity: 1; }
          51%, 100% { opacity: 0; }
        }

        /** AI特殊格式样式 */
        .ai-notice-title {
          display: flex;
          align-items: center;
          margin: 8px 0 4px 0;
          padding: 6px 10px;
          background: linear-gradient(135deg, #e6f7ff 0%, #f0f8ff 100%);
          border-left: 3px solid #1890ff;
          border-radius: 4px;

          .ai-emoji {
            margin-right: 6px;
            font-size: 16px;
          }

          strong {
            color: #1890ff;
            font-weight: 600;
          }
        }

        .ai-content-title {
          display: flex;
          align-items: center;
          margin: 4px 0;
          padding: 4px 8px;
          background: #f9f9f9;
          border-radius: 4px;

          .ai-emoji {
            margin-right: 6px;
            font-size: 14px;
          }

          strong {
            color: #666;
            font-weight: 500;
          }
        }

        .ai-success {
          display: flex;
          align-items: center;
          margin: 8px 0;
          padding: 6px 10px;
          background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
          border-left: 3px solid #52c41a;
          border-radius: 4px;
          color: #52c41a;
          font-weight: 500;

          .ai-emoji {
            margin-right: 6px;
            font-size: 16px;
          }
        }

        .ai-thinking {
          display: flex;
          align-items: center;
          color: #666;
          font-size: 14px;
        }
      }

      .message-input {
        padding: 16px 20px;
        background: #ffffff;

        .input-container {
          display: flex;
          align-items: center;
          border: 1px solid #e0e0e0;
          border-radius: 24px;
          padding: 8px 16px;
          gap: 12px;
          background: #ffffff;
          transition: border-color 0.2s ease;

          &:focus-within {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
          }

          .input-left {
            display: flex;
            align-items: center;

            .attachment-btn {
              color: #8c8c8c;
              border: none;
              padding: 6px;
              height: 32px;
              width: 32px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;

              &:hover {
                color: #1890ff;
                background: rgba(24, 144, 255, 0.1);
              }
            }
          }

          .input-main {
            flex: 1;

            .message-input-field {
              background: transparent;
              border: none;
              box-shadow: none;
              padding: 8px 0;
              font-size: 14px;
              line-height: 1.4;

              &:focus {
                box-shadow: none;
              }

              &::placeholder {
                color: #bfbfbf;
              }

              &:disabled {
                background: transparent;
                color: #bfbfbf;
              }
            }
          }

          .input-right {
            display: flex;
            align-items: center;

            .send-btn {
              height: 36px;
              width: 36px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              border: none;
              background: #1890ff;
              color: #ffffff;
              transition: all 0.2s ease;

              &:hover:not(:disabled) {
                background: #40a9ff;
                transform: scale(1.05);
              }

              &:disabled {
                background: #f5f5f5;
                color: #bfbfbf;
                cursor: not-allowed;
              }

              &.loading-btn {
                background: #f5f5f5;
                color: #1890ff;
              }

              &.stop-btn {
                background: #ff4d4f;
                color: #ffffff;

                &:hover {
                  background: #ff7875;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
