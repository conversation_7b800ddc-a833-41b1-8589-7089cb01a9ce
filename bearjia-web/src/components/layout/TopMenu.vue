<template>
  <div class="top-menu">
    <a-menu
      mode="horizontal"
      :selectedKeys="selectedKeys"
      :openKeys="openKeys"
      @select="handleMenuSelect"
      @openChange="handleOpenChange"
      class="top-menu-container"
    >
      <!-- 工作台菜单 -->
      <a-menu-item key="workbench" @click="handleWorkbenchClick">
        <template #icon>
          <BearJiaIcon icon="HomeOutlined" />
        </template>
        工作台
      </a-menu-item>

      <!-- 动态菜单 - 参考 SideMenu 写法 -->
      <a-sub-menu
        v-for="(router, index) in menuData"
        :key="`${router.path}-${index}`"
      >
        <template #icon>
          <BearJiaIcon :icon="router.meta?.icon || 'MenuOutlined'" />
        </template>
        <template #title>{{ router.meta?.title }}</template>

        <template v-for="(children, childIndex) in router.children" :key="`${children.path}-${childIndex}`">
          <!-- 有三级菜单的二级菜单 -->
          <a-sub-menu
            v-if="children.children"
            :key="`${children.path}-${childIndex}`"
          >
            <template #icon>
              <BearJiaIcon :icon="children.meta?.icon || 'AppstoreOutlined'" />
            </template>
            <template #title>{{ children.meta?.title }}</template>

            <!-- 三级菜单 -->
            <a-menu-item
              v-for="(threeLevelChildren, threeIndex) in children.children"
              :key="`${threeLevelChildren?.path}-${threeIndex}`"
              @click="handleThreeLevelMenuClick(router.path, children, threeLevelChildren)"
            >
              <template #icon>
                <BearJiaIcon :icon="threeLevelChildren.meta?.icon || 'BarsOutlined'" />
              </template>
              {{ threeLevelChildren.meta?.title }}
            </a-menu-item>
          </a-sub-menu>

          <!-- 没有三级菜单的二级菜单 -->
          <a-menu-item
            v-else
            :key="`${children.path}-${childIndex}`"
            @click="handleMenuClick(router.name, router.path, router.meta?.title, children.name, children.path, children.meta?.title, children.component)"
          >
            <template #icon>
              <BearJiaIcon :icon="children.meta?.icon || 'BarsOutlined'" />
            </template>
            {{ children.meta?.title }}
          </a-menu-item>
        </template>
      </a-sub-menu>
    </a-menu>
  </div>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits } from 'vue';
import { BearJiaIcon } from '@/utils/BearJiaIcon.js';

const props = defineProps({
  menuData: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['menuSelect']);

// 菜单状态
const selectedKeys = ref([]);
const openKeys = ref([]);

// 处理菜单选择
const handleMenuSelect = ({ key }) => {
  selectedKeys.value = [key];
};

// 处理子菜单展开/收起
const handleOpenChange = (keys) => {
  openKeys.value = keys;
};

// 处理工作台点击
const handleWorkbenchClick = () => {
  selectedKeys.value = ['workbench'];

  emit('menuSelect', {
    fatherName: 'HomePage',
    fatherPath: '/home',
    fatherTitle: '主页',
    name: 'Workbench',
    path: 'workbench',
    title: '工作台',
    component: null
  });
};

// 处理菜单点击
const handleMenuClick = (fatherName, fatherPath, fatherTitle, name, path, title, component) => {
  try {
    console.log('TopMenu 菜单点击:', { fatherName, fatherPath, fatherTitle, name, path, title });

    emit('menuSelect', {
      fatherName,
      fatherPath,
      fatherTitle,
      name,
      path,
      title,
      component
    });
  } catch (error) {
    console.error('菜单点击错误:', error);
  }
};

// 处理三级菜单点击
const handleThreeLevelMenuClick = (greatFatherPath, father, menu) => {
  try {
    console.log('TopMenu 三级菜单点击:', { greatFatherPath, father, menu });

    emit('menuSelect', {
      greatFatherPath,
      father,
      menu
    });
  } catch (error) {
    console.error('三级菜单点击错误:', error);
  }
};
</script>

<style scoped>
.top-menu {
  flex: 1;
  display: flex;
  justify-content: center;
}

.top-menu-container {
  border-bottom: none;
  background: transparent;
  line-height: 56px;
  height: 56px;
}

.top-menu-container .ant-menu-item,
.top-menu-container .ant-menu-submenu {
  border-bottom: none;
  height: 56px;
  line-height: 56px;
}

.top-menu-container .ant-menu-item:hover,
.top-menu-container .ant-menu-submenu:hover {
  color: var(--primary-color);
}

.top-menu-container .ant-menu-item-selected {
  color: var(--primary-color);
  border-bottom: 2px solid var(--primary-color);
}

.top-menu-container .ant-menu-submenu-title:hover {
  color: var(--primary-color);
}
</style>
