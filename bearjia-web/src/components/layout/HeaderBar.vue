<template>
  <a-layout-header :class="headerClasses" class="header-bar">
    <a-row style="height: 100%">
      <!-- 左侧区域 -->
      <a-col :span="(isTopMode || isMixMode) ? 4 : (isDrawerMode ? 8 : 12)" class="header-left">
        <!-- 侧边栏模式的折叠按钮 -->
        <template v-if="!isTopMode && !isMixMode && !isDrawerMode">
          <menu-unfold-outlined
              v-if="collapsed"
              class="trigger"
              @click="toggleCollapse"
          />
          <menu-fold-outlined
              v-else
              class="trigger"
              @click="toggleCollapse"
          />
        </template>

        <!-- 抽屉模式的菜单按钮 -->
        <template v-if="isDrawerMode">
          <menu-outlined
              class="trigger"
              @click="$emit('toggleMenu')"
          />
        </template>

        <!-- Logo 和标题（顶部模式） -->
        <div v-if="isTopMode || isMixMode" class="header-logo">
          <img src="/src/assets/images/logo.png" class="logo-img" alt="BearJia Logo" />
          <span class="logo-title">BearJia Admin</span>
        </div>

        <!-- 面包屑导航（侧边栏模式） -->
        <span v-if="!isTopMode && !isMixMode" class="header-title">
          {{ currentFatherMenuTitle }} / {{ currentMenuTitle }}
        </span>
      </a-col>

      <!-- 顶部菜单区域 -->
      <a-col v-if="isTopMode" :span="12" class="header-menu">
        <TopMenu
            :menu-data="menuData"
            @menu-select="$emit('menuSelect', $event)"
        />
      </a-col>

      <!-- 混合模式顶部菜单区域 -->
      <a-col v-if="isMixMode" :span="12" class="header-menu">
        <MixTopMenu
            :current-first-level-menu="currentFirstLevelMenu"
            :menu-data="menuData"
            @menu-select="$emit('menuSelect', $event)"
            @first-level-menu-select="$emit('firstLevelMenuSelect', $event)"
        />
      </a-col>

      <!-- 右侧区域 -->
      <a-col :span="(isTopMode || isMixMode) ? 8 : 12" class="header-right">
        <!-- 加载动画 -->
        <a-spin :spinning="loading" style="margin-right: 16px;"/>

        <!-- 搜索按钮 -->
        <a-tooltip placement="bottom" title="全局搜索">
          <a-button class="header-icon-btn" type="link" @click="showSearch">
            <SearchOutlined/>
          </a-button>
        </a-tooltip>

        <!-- 消息按钮 -->
        <a-tooltip placement="bottom" title="消息通知">
          <a-badge :count="messageCount" :offset="[10, 0]">
            <a-button class="header-icon-btn" type="link" @click="showMessages">
              <BellOutlined/>
            </a-button>
          </a-badge>
        </a-tooltip>

        <!-- 聊天按钮 -->
        <a-tooltip placement="bottom" title="在线聊天">
          <a-button class="header-icon-btn" type="link" @click="showChat">
            <MessageOutlined/>
          </a-button>
        </a-tooltip>

        <!-- 全屏按钮 -->
        <a-tooltip :title="isFullscreen ? '退出全屏' : '全屏显示'" placement="bottom">
          <a-button class="header-icon-btn" type="link" @click="toggleFullscreen">
            <FullscreenExitOutlined v-if="isFullscreen"/>
            <FullscreenOutlined v-else/>
          </a-button>
        </a-tooltip>

        <!-- 刷新按钮 -->
        <a-tooltip placement="bottom" title="刷新页面">
          <a-button class="header-icon-btn" type="link" @click="refreshCurrentPage">
            <ReloadOutlined/>
          </a-button>
        </a-tooltip>

        <!-- 布局设置 -->
        <a-tooltip placement="bottom" title="布局设置">
          <a-button class="header-icon-btn" type="link" @click="$emit('showSettings')">
            <SettingOutlined/>
          </a-button>
        </a-tooltip>

        <!-- 用户头像和下拉菜单 -->
        <a-dropdown class="user-dropdown">
          <a class="ant-dropdown-link user-info" @click.prevent>
            <a-avatar :size="32" :src="userAvatar">
              <template #icon>
                <UserOutlined/>
              </template>
            </a-avatar>
            <span class="username">{{ userName }}</span>
          </a>
          <template #overlay>
            <a-menu>
              <a-menu-item key="personalCenter" @click="$emit('personalCenter')">
                <UserOutlined class="menu-icon"/>
                <span>个人中心</span>
              </a-menu-item>
              <a-menu-item key="settings" @click="$emit('showSettings')">
                <SettingOutlined class="menu-icon"/>
                <span>布局设置</span>
              </a-menu-item>
              <a-menu-item key="logout" @click="$emit('logout')">
                <LogoutOutlined class="menu-icon"/>
                <span>退出登录</span>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </a-col>
    </a-row>

    <!-- 功能组件 -->
    <!-- 全局搜索 -->
    <GlobalSearch v-model:visible="searchVisible"/>

    <!-- 消息中心 -->
    <MessageCenter v-model:visible="messageVisible"/>

    <!-- 聊天面板 -->
    <ChatPanel v-model:visible="chatVisible"/>

    <!-- 全屏处理器 -->
    <FullscreenHandler ref="fullscreenHandlerRef"/>
  </a-layout-header>
</template>

<script setup>
import {computed, defineEmits, defineProps, ref} from 'vue';
import {useUserStore} from '@/stores/user';
import TopMenu from './TopMenu.vue';
import MixTopMenu from './MixTopMenu.vue';
import GlobalSearch from '@/components/common/GlobalSearch.vue';
import MessageCenter from '@/components/common/MessageCenter.vue';
import ChatPanel from '@/components/common/ChatPanel.vue';
import FullscreenHandler from '@/components/common/FullscreenHandler.vue';

import {
  BellOutlined,
  FullscreenExitOutlined,
  FullscreenOutlined,
  LogoutOutlined,
  MenuFoldOutlined,
  MenuOutlined,
  MenuUnfoldOutlined,
  MessageOutlined,
  ReloadOutlined,
  SearchOutlined,
  SettingOutlined,
  UserOutlined
} from '@ant-design/icons-vue';

const userStore = useUserStore();

const props = defineProps({
  collapsed: {
    type: Boolean,
    default: false
  },
  currentFatherMenuTitle: {
    type: String,
    default: ''
  },
  currentMenuTitle: {
    type: String,
    default: ''
  },
  loading: {
    type: Boolean,
    default: false
  },
  layoutSettings: {
    type: Object,
    default: () => ({})
  },
  menuData: {
    type: Array,
    default: () => []
  },
  currentFirstLevelMenu: {
    type: Object,
    default: null
  }
});

const emit = defineEmits([
  'update:collapsed',
  'showSettings',
  'personalCenter',
  'logout',
  'refreshPage',
  'menuSelect',
  'firstLevelMenuSelect',
  'toggleMenu'
]);

// 计算属性
const isTopMode = computed(() => props.layoutSettings?.navMode === 'top');
const isMixMode = computed(() => props.layoutSettings?.navMode === 'mix');
const isDrawerMode = computed(() => props.layoutSettings?.navMode === 'drawer');

const headerClasses = computed(() => ({
  'header-top-mode': isTopMode.value,
  'header-mix-mode': isMixMode.value,
  'header-side-mode': !isTopMode.value && !isMixMode.value
}));

const userAvatar = computed(() => userStore.avatar || '../../assets/images/profile.jpg');
const userName = computed(() => userStore.nickName || '用户');

// 功能状态
const messageCount = ref(5); // 消息数量

// 组件显示状态
const searchVisible = ref(false);
const messageVisible = ref(false);
const chatVisible = ref(false);

// 全屏处理器引用
const fullscreenHandlerRef = ref(null);

const toggleCollapse = () => {
  emit('update:collapsed', !props.collapsed);
};

const refreshCurrentPage = () => {
  emit('refreshPage');
};

// 搜索功能
const showSearch = () => {
  searchVisible.value = true;
};

// 消息功能
const showMessages = () => {
  messageVisible.value = true;
};

// 聊天功能
const showChat = () => {
  chatVisible.value = true;
};

// 全屏功能
const toggleFullscreen = () => {
  if (fullscreenHandlerRef.value) {
    fullscreenHandlerRef.value.toggleFullscreen();
  }
};

// 计算全屏状态
const isFullscreen = computed(() => {
  return fullscreenHandlerRef.value?.isFullscreen?.value || false;
});
</script>

<style lang="less" scoped>
.menu-icon {
  margin-right: 8px;
}

.header-bar {
  background: #fafbfc;
  padding: 0;
  height: 56px !important;
  line-height: 56px;

  .header-left {
    display: flex;
    align-items: center;

    .header-logo {
      display: flex;
      align-items: center;
      padding: 0 24px;

      .logo-img {
        height: 32px;
        width: auto;
        margin-right: 12px;
        transition: transform 0.3s ease;

        &:hover {
          transform: scale(1.05);
        }
      }

      .logo-title {
        font-size: 20px;
        font-weight: 600;
        color: var(--primary-color);
      }
    }

    .header-title {
      margin-left: 16px;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
    }
  }

  .header-menu {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  // 顶部模式样式
  &.header-top-mode {
    .header-left {
      justify-content: flex-start;
    }

    .header-menu {
      flex: 1;
    }
  }

  // 顶部模式样式
  &.header-mix-mode {
    .header-left {
      justify-content: flex-start;
    }

    .header-menu {
      flex: 1;
    }
  }

  .trigger {
    color: rgba(0, 0, 0, 0.65);
    padding: 0 24px;
    cursor: pointer;
    transition: color 0.3s, transform 0.3s;

    &:hover {
      color: #1890ff;
      transform: rotate(180deg);
    }
  }

  .header-left {
    .header-title {
      color: rgba(0, 0, 0, 0.65);
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-right: 12px;
    height: 56px;
    gap: 8px; /* 添加子元素间距 */
    min-width: 0; /* 允许 flex 收缩 */

    .header-icon-btn {
      color: rgba(0, 0, 0, 0.65);
      padding: 0 12px;
      transition: background 0.3s, color 0.3s, transform 0.3s;

      &:hover {
        background: rgba(0, 0, 0, 0.04);
        color: var(--primary-color);
        transform: rotate(360deg);
      }
    }

    .username {
      margin-left: 4px;
      color: rgba(0, 0, 0, 0.65);
    }

    .ant-dropdown-link {
      transition: all 0.3s ease;
      //padding: 4px 8px;
      border-radius: 4px;

      &:hover {
        background: rgba(0, 0, 0, 0.02);

        .ant-avatar {
          transform: rotate(360deg);
        }
      }
    }

    .ant-avatar {
      transition: transform 0.5s;
    }

    /* 用户信息区域样式 */
    .user-dropdown {
      margin-right: 12px;
      min-width: 120px; /* 确保有足够的最小宽度 */
    }

    .user-info {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      border-radius: 6px;
      transition: background-color 0.3s;
      white-space: nowrap; /* 防止用户名换行 */
      max-width: 150px; /* 设置最大宽度 */

      &:hover {
        background-color: rgba(0, 0, 0, 0.04);
      }

      .username {
        color: rgba(0, 0, 0, 0.85);
        font-size: 14px;
        font-weight: 500;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 80px; /* 用户名最大宽度 */
      }
    }
  }
}
</style>