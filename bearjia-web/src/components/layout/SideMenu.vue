<template>
  <a-layout-sider
      :theme="layoutSettings.theme"
      :collapsed="collapsed"
      :trigger="null"
      collapsible
      :class="{ 'dark-theme': layoutSettings.theme === 'dark' }"
      @collapse="$emit('update:collapsed', $event)"
  >
    <!-- Logo区域 -->
    <div class="layout-logo">
      <img src="/src/assets/images/logo.png" class="logo-img" alt="BearJia Logo" />
      <span class="logo-title" v-show="!collapsed">BearJia Admin</span>
    </div>

    <!-- 菜单区域 -->
    <a-menu
        v-model:selectedKeys="selectedKeys"
        v-model:openKeys="openKeys"
        :theme="layoutSettings.theme"
        mode="inline"
        class="side-menu"
        @openChange="handleOpenChange"
    >
      <a-menu-item key="workbench" @click="handleMenuClick('HomePage', '/home', '主页', 'Workbench', 'workbench', '工作台')">
        <template #icon>
          <BearJiaIcon icon="HomeOutlined" />
        </template>
        工作台
      </a-menu-item>

      <!-- 动态菜单 -->
      <a-sub-menu
          v-for="(router, index) in menuData"
          :key="`${router.path}-${index}`"
      >
        <template #icon>
          <BearJiaIcon :icon="router.meta?.icon || 'MenuOutlined'" />
        </template>
        <template #title>{{ router.meta?.title }}</template>

        <template v-for="(children, childIndex) in router.children" :key="`${children.path}-${childIndex}`">
          <!-- 二级父菜单 -->
          <a-sub-menu
              v-if="children.children"
              :key="`${children.path}-${childIndex}`"
          >
            <template #icon>
              <BearJiaIcon :icon="children.meta?.icon || 'AppstoreOutlined'" />
            </template>
            <template #title>{{ children.meta?.title }}</template>

            <!-- 三级菜单 -->
            <a-menu-item
                v-for="(threeLevelChildren, threeIndex) in children.children"
                :key="`${threeLevelChildren?.path}-${threeIndex}`"
                @click="handleThreeLevelMenuClick(router.path, children, threeLevelChildren)"
            >
              <template #icon>
                <BearJiaIcon :icon="threeLevelChildren.meta?.icon || 'BarsOutlined'" />
              </template>
              {{ threeLevelChildren.meta?.title }}
            </a-menu-item>
          </a-sub-menu>

          <!-- 二级菜单 -->
          <a-menu-item
              v-else
              :key="`${children.path}-${childIndex}`"
              @click="handleMenuClick(router.name, router.path, router.meta.title, children.name, children.path, children.meta.title, children.component)"
          >
            <template #icon>
              <BearJiaIcon :icon="children.meta?.icon || 'BarsOutlined'" />
            </template>
            {{ children.meta?.title }}
          </a-menu-item>
        </template>
      </a-sub-menu>
    </a-menu>
  </a-layout-sider>
</template>

<script setup>
import { ref } from 'vue';
import { BearJiaIcon } from '@/utils/BearJiaIcon.js';

const props = defineProps({
  collapsed: Boolean,
  menuData: Array,
  layoutSettings: Object
});

const emit = defineEmits(['update:collapsed', 'menuSelect']);

const selectedKeys = ref([]);
const openKeys = ref([]);

const handleOpenChange = (keys) => {
  if (keys.length <= 1) {
    openKeys.value = keys;
    return;
  }

  const latestKey = keys[keys.length - 1];
  const isTopLevel = props.menuData.some((item, idx) => `${item.path}-${idx}` === latestKey);

  if (isTopLevel) {
    openKeys.value = [latestKey];
  } else {
    openKeys.value = keys;
  }
};

const handleMenuClick = (fatherName, fatherPath, fatherTitle, name, path, title, component) => {
  try {
    if (path === 'workbench') {
      selectedKeys.value = ['workbench'];
      openKeys.value = [];
    } else {
      const fatherIndex = props.menuData.findIndex(item => item.path === fatherPath);
      const childIndex = props.menuData[fatherIndex]?.children.findIndex(item => item.path === path);
      const menuKey = `${path}-${childIndex}`;
      const fatherKey = `${fatherPath}-${fatherIndex}`;

      selectedKeys.value = [menuKey];
      openKeys.value = [fatherKey];
    }
    emit('menuSelect', {
      fatherName, fatherPath, fatherTitle, name, path, title, component
    });
  } catch (error) {
    console.error('菜单点击错误:', error);
  }
};

const handleThreeLevelMenuClick = (greatFatherPath, father, menu) => {
  try {
    const greatFatherIndex = props.menuData.findIndex(item => item.path === greatFatherPath);
    const fatherIndex = props.menuData[greatFatherIndex]?.children.findIndex(item => item.path === father.path);
    const menuIndex = father?.children.findIndex(item => item.path === menu.path);

    selectedKeys.value = [`${menu.path}-${menuIndex}`];
    openKeys.value = [
      `${greatFatherPath}-${greatFatherIndex}`,
      `${father.path}-${fatherIndex}`
    ];

    emit('menuSelect', {
      greatFatherPath, father, menu
    });
  } catch (error) {
    console.error('三级菜单点击错误:', error);
  }
};

const setCurrentMenu = (menuPath, isThreeLevel = false, parentPaths = []) => {
  if (isThreeLevel && parentPaths.length >= 2) {
    const greatFatherIndex = props.menuData.findIndex(item => item.path === parentPaths[0]);
    const fatherIndex = props.menuData[greatFatherIndex]?.children.findIndex(item => item.path === parentPaths[1]);
    const father = props.menuData[greatFatherIndex]?.children[fatherIndex];
    const menuIndex = father?.children.findIndex(item => item.path === menuPath);

    if (menuIndex !== -1) {
      selectedKeys.value = [`${menuPath}-${menuIndex}`];
      openKeys.value = [
        `${parentPaths[0]}-${greatFatherIndex}`,
        `${parentPaths[1]}-${fatherIndex}`
      ];
    }
  } else if (parentPaths.length >= 1) {
    const fatherIndex = props.menuData.findIndex(item => item.path === parentPaths[0]);
    const childIndex = props.menuData[fatherIndex]?.children.findIndex(item => item.path === menuPath);

    if (childIndex !== -1) {
      selectedKeys.value = [`${menuPath}-${childIndex}`];
      openKeys.value = [`${parentPaths[0]}-${fatherIndex}`];
    }
  }
};

defineExpose({ setCurrentMenu });
</script>

<style lang="less">
@import '@/style/components/menu.less';
.ant-layout-sider{
  width: 68px!important;
}
:deep(.ant-layout-sider) {
  margin: 16px 0 16px 16px;
  border-radius: 16px;
  overflow: hidden;
  background: #FFFFFF;
  width: 68px!important;

  &.dark-theme {
    background: #001529;
    .layout-logo {
      background-color: #001529;
    }
  }
  &:not(.dark-theme) {
    .layout-logo {
      background-color: #fff;
    }
  }
}

.layout-logo {
  display: flex;
  align-items: center;
  padding: 16px;
  .logo-img {
    height: 32px;
    margin-right: 8px;
  }
  .logo-title {
    color: v-bind('layoutSettings.theme === "dark" ? "#fff" : "#000"');
    font-size: 18px;
    font-weight: 600;
    white-space: nowrap;
    opacity: 1;
    transition: all 0.3s;
  }
}

.side-menu {
  height: calc(100% - 64px);
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}
.side-menu::-webkit-scrollbar {
  display: none;
}
</style>
