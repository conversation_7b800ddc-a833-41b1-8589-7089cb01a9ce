import request from '@/utils/request'
import { getToken } from '@/utils/auth'

/**
 * AI 聊天接口
 * @param {Object} data 聊天请求数据
 * @param {string} data.message 用户消息
 * @param {Array} data.conversationHistory 对话历史
 * @param {string} data.sessionId 会话ID（可选）
 */
export function chatWithAi(data) {
  return request({
    url: '/ai/chat',
    method: 'post',
    data,
    timeout: 100000
  })
}



/**
 * AI 实时返回聊天接口 - 使用 fetch 流式处理 (推荐)
 * @param {Object} data 聊天请求数据
 * @param {string} data.message 用户消息
 * @param {Array} data.conversationHistory 对话历史
 * @param {string} data.sessionId 会话ID（可选）
 * @param {Function} onMessage 接收消息的回调函数
 * @param {Function} onError 错误处理回调函数
 * @param {Function} onComplete 完成回调函数
 * @returns {Function} 取消函数
 */
export function chatWithAiStream(data, { onMessage, onError, onComplete } = {}) {
  return chatWithAiStreamFetch(data, { onMessage, onError, onComplete });
}

/**
 * AI 流式聊天接口 - 使用 fetch 流式处理
 * @param {Object} data 聊天请求数据
 * @param {Function} onMessage 接收消息的回调函数
 * @param {Function} onError 错误处理回调函数
 * @param {Function} onComplete 完成回调函数
 * @returns {Promise} 返回取消函数的Promise
 */
export function chatWithAiStreamFetch(data, { onMessage, onError, onComplete } = {}) {
  let abortController = new AbortController();

  // 直接返回取消函数，不使用Promise包装
  const cancelFn = () => {
    abortController.abort();
  };

  // 立即开始流式处理
  (async () => {
    const baseURL = import.meta.env.VITE_APP_BASE_API;
    const url = `${baseURL}/ai/chat/stream`;

    try {
      console.log('开始流式请求:', url, data);

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getToken() || ''}`
        },
        body: JSON.stringify(data),
        signal: abortController.signal
      });

      console.log('流式响应状态:', response.status, response.headers.get('content-type'));

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let fullResponse = '';
      let messageId = null;
      let buffer = '';
      let currentEvent = null; // 当前SSE事件类型
      let lastActivityTime = Date.now(); // 最后活动时间
      let hasReceivedMessage = false; // 是否已接收到消息内容

      console.log('开始读取流数据...');

      // 设置超时检查，如果长时间没有活动，自动完成
      const timeoutCheck = setInterval(() => {
        const now = Date.now();
        if (now - lastActivityTime > 30000) { // 30秒无活动
          console.log('流式响应超时，自动完成');
          clearInterval(timeoutCheck);
          if (hasReceivedMessage && fullResponse) {
            onComplete && onComplete({
              type: 'end',
              messageId: messageId || Date.now().toString(),
              fullContent: fullResponse
            });
          } else {
            onError && onError('响应超时，请稍后重试');
          }
        }
      }, 5000); // 每5秒检查一次

      try {
        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            console.log('流数据读取完成');
            clearInterval(timeoutCheck); // 清理超时检查
            onComplete && onComplete({
              type: 'end',
              messageId,
              fullContent: fullResponse
            });
            break;
          }

          // 将新数据添加到缓冲区
          const chunk = decoder.decode(value, { stream: true });
          buffer += chunk;

          console.log('接收到数据块:', chunk);

          // 按行分割数据
          const lines = buffer.split('\n');

          // 保留最后一行（可能不完整）
          buffer = lines.pop() || '';

          for (const line of lines) {
            if (line.trim() === '') {
              // 空行表示一个SSE事件结束，处理当前事件
              if (currentEvent && currentEvent.type) {
                console.log('🎯 处理完整SSE事件:', currentEvent);
                lastActivityTime = Date.now(); // 更新活动时间
                const context = { messageId, fullResponse, hasReceivedMessage, timeoutCheck };
                handleSseEvent(currentEvent, { onMessage, onError, onComplete }, context);
                // 更新上下文变量
                messageId = context.messageId;
                fullResponse = context.fullResponse;
                hasReceivedMessage = context.hasReceivedMessage;
                currentEvent = null;
              }
              continue;
            }

            console.log('🎯 处理行数据:', line);

            // 正确处理SSE格式
            if (line.startsWith('event:')) {
              // 处理SSE事件类型
              const eventType = line.slice(6).trim();
              console.log('🎯 SSE事件类型:', eventType);

              if (!currentEvent) {
                currentEvent = {};
              }
              currentEvent.type = eventType;

            } else if (line.startsWith('data:')) {
              // 处理SSE数据
              const eventData = line.slice(5); // 注意：不要trim，保留原始数据
              console.log('🎯 SSE数据:', eventData);

              if (!currentEvent) {
                currentEvent = {};
              }

              // 如果已经有数据，追加到现有数据
              if (currentEvent.data !== undefined) {
                currentEvent.data += '\n' + eventData;
              } else {
                currentEvent.data = eventData;
              }

            } else {
              // 其他格式的行，可能是注释或其他内容
              console.log('🎯 忽略未知格式行:', line);
            }
          }
        }
      } catch (error) {
        clearInterval(timeoutCheck); // 清理超时检查
        if (error.name === 'AbortError') {
          console.log('流式请求被取消');
          return;
        }
        console.error('读取流数据错误:', error);
        onError && onError(error.message);
      }

    } catch (error) {
      clearInterval(timeoutCheck); // 清理超时检查
      if (error.name === 'AbortError') {
        console.log('请求被取消');
        return;
      }
      console.error('流式请求错误:', error);
      onError && onError(error.message);
    }
  })();

  // 返回取消函数
  return cancelFn;
}

/**
 * 处理SSE事件
 */
function handleSseEvent(event, callbacks, context) {
  const { onMessage, onError, onComplete } = callbacks;
  let { messageId, fullResponse } = context;

  if (!event || !event.type) return;

  console.log('处理SSE事件:', event.type, event.data);

  switch (event.type) {
    case 'start':
      messageId = Date.now().toString();
      context.messageId = messageId;
      console.log('开始流式消息，messageId:', messageId);
      onMessage && onMessage({
        type: 'start',
        messageId,
        content: ''
      });
      break;

    case 'info':
      // 处理信息事件，显示处理进度
      console.log('🎯 处理进度信息:', event.data);
      if (event.data && event.data.trim()) {
        // 只有在没有接收到实际AI回复时才显示进度信息
        if (!context.hasReceivedMessage) {
          context.fullResponse += event.data + '\n';
          console.log('🎯 添加进度信息到流式内容:', event.data);
          onMessage && onMessage({
            type: 'chunk',
            messageId: context.messageId || Date.now().toString(),
            content: event.data + '\n',
            fullContent: context.fullResponse
          });
        }
      }
      break;

    case 'message':
      // 这是实际的AI回复内容
      console.log('接收到AI消息:', event.data);
      if (event.data) {
        // 清空之前的进度信息，只显示AI回复
        context.fullResponse = event.data;
        context.hasReceivedMessage = true; // 标记已接收到消息
        onMessage && onMessage({
          type: 'chunk',
          messageId: context.messageId || Date.now().toString(),
          content: event.data,
          fullContent: context.fullResponse
        });
      }
      break;

    case 'complete':
    case 'done':
      console.log('接收到完成事件，最终内容:', context.fullResponse);
      if (context.timeoutCheck) {
        clearInterval(context.timeoutCheck); // 清理超时检查
      }
      onComplete && onComplete({
        type: 'end',
        messageId: context.messageId,
        fullContent: context.fullResponse
      });
      break;

    case 'error':
      console.error('接收到错误事件:', event.data);
      if (context.timeoutCheck) {
        clearInterval(context.timeoutCheck); // 清理超时检查
      }
      onError && onError(event.data || '服务器返回错误');
      break;

    default:
      console.log('未知SSE事件类型:', event.type, '数据:', event.data);
      // 对于未知事件类型，如果有数据，也尝试作为消息处理
      if (event.data && event.data.trim()) {
        context.fullResponse += event.data + '\n';
        onMessage && onMessage({
          type: 'chunk',
          messageId: context.messageId || Date.now().toString(),
          content: event.data + '\n',
          fullContent: context.fullResponse
        });
      }
      break;
  }
}
/**
 * 执行AI函数
 * @param {Object} data 执行请求数据
 * @param {string} data.functionName 函数名称
 * @param {Object} data.arguments 函数参数
 * @param {Array} data.conversationHistory 对话历史
 * @param {string} data.sessionId 会话ID（可选）
 * @param {string} data.confirmationToken 确认令牌（可选）
 */
export function executeAiFunction(data) {
  return request({
    url: '/ai/execute',
    method: 'post',
    data,
    timeout: 100000
  })
}

/**
 * 获取可用AI函数列表
 */
export function getAiFunctions() {
  return request({
    url: '/ai/functions',
    method: 'get'
  })
}

/**
 * 获取AI函数统计信息
 */
export function getAiFunctionStatistics() {
  return request({
    url: '/ai/functions/statistics',
    method: 'get'
  })
}

/**
 * AI服务健康检查
 */
export function checkAiHealth() {
  return request({
    url: '/ai/health',
    method: 'get'
  })
}

/**
 * 测试ReAct智能体
 * @param {Object} data 聊天请求数据
 * @param {string} data.message 用户消息
 * @param {Array} data.conversationHistory 对话历史
 */
export function testReAct(data) {
  return request({
    url: '/ai/test-react',
    method: 'post',
    data,
    timeout: 120000 // ReAct可能需要更长时间
  })
}
