<template>
  <div :class="layoutClasses" :style="cssVars" class="app-container">
    <!-- 侧边栏布局（默认） -->
    <a-layout v-if="!layoutSettings.navMode || layoutSettings.navMode === 'side'" class="layout-container layout-side">
      <SideMenu
          v-model:collapsed="collapsed"
          :layout-settings="layoutSettings"
          :menu-data="sidebarRouters"
          @menu-select="handleMenuSelect"
          ref="bearRef"
      />
      <a-layout style="background: #fafbfc;width: 100%">
        <HeaderBar
            v-model:collapsed="collapsed"
            :current-father-menu-title="headerInfo.currentFatherMenuTitle"
            :current-menu-title="headerInfo.currentMenuTitle"
            :layout-settings="layoutSettings"
            :loading="loading"
            @logout="handleLogout"
            @show-settings="showDrawer"
            @personal-center="enterPersonalCenter"
            @refresh-page="refreshCurrentPage"
        />
        <HistoryNav class="layout-container__history"/>
        <a-layout-content class="layout-container__content">
          <div class="content-wrapper" style="height: calc(100vh - 64px);">
            <a-config-provider :locale="zhCN">
              <router-view></router-view>
            </a-config-provider>
          </div>
        </a-layout-content>
      </a-layout>
    </a-layout>

    <!-- 顶部菜单布局 -->
    <a-layout v-else-if="layoutSettings.navMode === 'top'" class="layout-container layout-top">
      <HeaderBar
          v-model:collapsed="collapsed"
          :current-father-menu-title="headerInfo.currentFatherMenuTitle"
          :current-menu-title="headerInfo.currentMenuTitle"
          :layout-settings="layoutSettings"
          :loading="loading"
          :menu-data="sidebarRouters"
          @logout="handleLogout"
          @show-settings="showDrawer"
          @personal-center="enterPersonalCenter"
          @refresh-page="refreshCurrentPage"
          @menu-select="handleMenuSelect"
      />
      <HistoryNav class="layout-container__history"/>
      <a-layout-content class="layout-container__content">
        <div class="content-wrapper" style="height: calc(100vh - 64px);">
          <a-config-provider :locale="zhCN">
            <router-view></router-view>
          </a-config-provider>
        </div>
      </a-layout-content>
    </a-layout>

    <!-- 混合布局 -->
    <a-layout v-else-if="layoutSettings.navMode === 'mix'" class="layout-container layout-mix">
      <HeaderBar
          v-model:collapsed="collapsed"
          :current-father-menu-title="headerInfo.currentFatherMenuTitle"
          :current-first-level-menu="currentFirstLevelMenu"
          :current-menu-title="headerInfo.currentMenuTitle"
          :layout-settings="layoutSettings"
          :loading="loading"
          :menu-data="sidebarRouters"
          @logout="handleLogout"
          @show-settings="showDrawer"
          @personal-center="enterPersonalCenter"
          @refresh-page="refreshCurrentPage"
          @menu-select="handleMenuSelect"
          @first-level-menu-select="handleFirstLevelMenuSelect"
      />
      <a-layout>
        <MixSideMenu
            v-model:collapsed="collapsed"
            :current-first-level-menu="currentFirstLevelMenu"
            :layout-settings="layoutSettings"
            @menu-select="handleMenuSelect"
        />
        <a-layout style="background: #fafbfc;">
          <HistoryNav class="layout-container__history"/>
          <a-layout-content class="layout-container__content">
            <div class="content-wrapper" style="height: calc(100vh - 64px);">
              <a-config-provider :locale="zhCN">
                <router-view></router-view>
              </a-config-provider>
            </div>
          </a-layout-content>
        </a-layout>
      </a-layout>
    </a-layout>

    <!-- 分栏布局 -->
    <a-layout v-else-if="layoutSettings.navMode === 'column'" class="layout-container layout-column">

      <a-layout>
        <!-- 一级菜单栏 -->
        <a-layout-sider class="first-level-menu" theme="light" width="80">
          <div class="column-first-menu">
            <!-- Logo 区域 -->
            <div class="menu-logo">
              <img src="/src/assets/images/logo.png" alt="BearJia Logo" class="logo-img" />
            </div>
            <a-menu :selectedKeys="firstLevelSelectedKeys" mode="inline">
              <a-menu-item
                  v-for="(router, index) in sidebarRouters"
                  :key="`${router.path}-${index}`"
                  @click="handleFirstLevelMenuSelect(router)"
              >
                <a-tooltip :title="router.meta?.title" placement="right">
                  <BearJiaIcon :icon="router.meta?.icon || 'MenuOutlined'"/>
                </a-tooltip>
              </a-menu-item>
            </a-menu>
          </div>
        </a-layout-sider>
        <!-- 二级菜单栏 -->
        <a-layout-sider class="second-level-menu" theme="light" width="200">
          <div class="column-second-menu">
            <!-- 系统名称区域 -->
            <div class="system-title">
              <span class="title-text">BearJia Admin</span>
            </div>
            <a-menu mode="inline">
              <!-- 工作台菜单 -->
              <a-menu-item v-if="!currentFirstLevelMenu" key="workbench" @click="handleWorkbenchClick">
                <template #icon>
                  <BearJiaIcon icon="HomeOutlined"/>
                </template>
                工作台
              </a-menu-item>

              <!-- 当前一级菜单的子菜单 -->
              <template v-for="(children, childIndex) in currentFirstLevelMenu.children" v-if="currentFirstLevelMenu"
                        :key="`${children.path}-${childIndex}`">
                <a-sub-menu
                    v-if="children.children && children.children.length > 0"
                    :key="`${children.path}-${childIndex}`"
                >
                  <template #icon>
                    <BearJiaIcon :icon="children.meta?.icon || 'AppstoreOutlined'"/>
                  </template>
                  <template #title>{{ children.meta?.title }}</template>
                  <a-menu-item
                      v-for="(threeLevelChildren, threeIndex) in children.children"
                      :key="`${threeLevelChildren?.path}-${threeIndex}`"
                      @click="handleThreeLevelMenuClick(currentFirstLevelMenu.path, children, threeLevelChildren)"
                  >
                    <BearJiaIcon :icon="threeLevelChildren.meta?.icon || 'BarsOutlined'"/>
                    {{ threeLevelChildren.meta?.title }}
                  </a-menu-item>
                </a-sub-menu>
                <a-menu-item
                    v-else
                    :key="`${children.path}-${childIndex}`"
                    @click="handleMenuClick(currentFirstLevelMenu.name, currentFirstLevelMenu.path, currentFirstLevelMenu.meta?.title, children.name, children.path, children.meta?.title, children.component)"
                >
                  <BearJiaIcon :icon="children.meta?.icon || 'BarsOutlined'"/>
                  {{ children.meta?.title }}
                </a-menu-item>
              </template>

              <!-- 没有选择菜单时的提示 -->
              <div v-if="!currentFirstLevelMenu && !firstLevelSelectedKeys.includes('workbench')" class="no-menu-tip">
                <p>请选择左侧菜单</p>
              </div>
            </a-menu>
          </div>
        </a-layout-sider>


        <!-- 内容区域 -->
        <a-layout style="background: #fafbfc;">
          <!-- 分栏布局专用头部 -->
          <a-layout-header class="column-header">
            <HeaderBar
                v-model:collapsed="collapsed"
                :current-father-menu-title="headerInfo.currentFatherMenuTitle"
                :current-menu-title="headerInfo.currentMenuTitle"
                :layout-settings="layoutSettings"
                :loading="loading"
                @logout="handleLogout"
                @show-settings="showDrawer"
                @personal-center="enterPersonalCenter"
                @refresh-page="refreshCurrentPage"
            />
          </a-layout-header>
          <HistoryNav class="layout-container__history column-history"/>
          <a-layout-content class="layout-container__content">
            <div class="content-wrapper" style="height: calc(100vh - 64px);">
              <a-config-provider :locale="zhCN">
                <router-view></router-view>
              </a-config-provider>
            </div>
          </a-layout-content>
        </a-layout>
      </a-layout>
    </a-layout>

    <!-- 抽屉布局 -->
    <a-layout v-else-if="layoutSettings.navMode === 'drawer'" class="layout-container layout-drawer">
      <HeaderBar
          :current-father-menu-title="headerInfo.currentFatherMenuTitle"
          :current-menu-title="headerInfo.currentMenuTitle"
          :layout-settings="layoutSettings"
          :loading="loading"
          @logout="handleLogout"
          @show-settings="showDrawer"
          @personal-center="enterPersonalCenter"
          @refresh-page="refreshCurrentPage"
          @toggle-menu="toggleDrawerMenu"
      />
      <a-layout style="background: #fafbfc;">
        <HistoryNav class="layout-container__history drawer-history"/>
        <a-layout-content class="layout-container__content">
          <div class="content-wrapper" style="height: calc(100vh - 64px);">
            <a-config-provider :locale="zhCN">
              <router-view></router-view>
            </a-config-provider>
          </div>
        </a-layout-content>
      </a-layout>

      <!-- 抽屉菜单 -->
      <a-drawer
          v-model:open="drawerMenuVisible"
          :body-style="{ padding: 0 }"
          :closable="true"
          class="drawer-menu"
          placement="left"
          title="导航菜单"
          width="280"
      >
        <div class="drawer-menu-content">
          <SideMenu
              :collapsed="false"
              :layout-settings="layoutSettings"
              :menu-data="sidebarRouters"
              @menu-select="handleMenuSelectAndClose"
          />
        </div>
      </a-drawer>
    </a-layout>

    <!-- 设置抽屉组件 -->
    <SettingDrawer
        v-model:visible="drawerVisible"
        :settings="layoutSettings"
        @update:settings="handleSettingsChange"
    />
  </div>
</template>

<script setup>
import {computed, nextTick, onMounted, reactive, ref, watch} from 'vue';
import {useRoute, useRouter} from 'vue-router';
import {useUserStore} from '@/stores/user';
import {useAppStore} from '@/stores/app';
import {usePermissionStore} from '@/stores/permission';
import {useTagsViewStore} from '@/stores/tagsView';
// 国际化：显示中文
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import {message} from 'ant-design-vue';

// 将侧边栏和头部组件拆分
import SideMenu from '@/components/layout/SideMenu.vue';
import MixSideMenu from '@/components/layout/MixSideMenu.vue';
import HeaderBar from '@/components/layout/HeaderBar.vue';
import HistoryNav from '@/components/layout/HistoryNav.vue';
// 将布局设置抽屉组件拆分
import SettingDrawer from '@/components/layout/SettingDrawer.vue';
// 导入图标组件
import {BearJiaIcon} from '@/utils/BearJiaIcon.js';

dayjs.locale('zh-cn');

const vueRouter = useRouter();
const vueRoute = useRoute();
const userRouters = vueRouter.getRoutes();

// 获取 store
const userStore = useUserStore();
const appStore = useAppStore();
const permissionStore = usePermissionStore();
const tagsViewStore = useTagsViewStore();

// 左侧边栏是否折叠：默认不折叠
const collapsed = ref(false);
const loading = ref(false);
const drawerVisible = ref(false);

// 初始化 headerInfo
const headerInfo = reactive({
  currentFatherMenuTitle: '主页',
  currentMenuTitle: '工作台'
});

// 菜单相关状态数据
const menuState = reactive({
  // 所有一级菜单节点
  rootSubmenuKeys: [],
  // 展开的父菜单节点
  openedFaterMenuKeys: [],
  // 当前选中的菜单项
  selectedMenuKeys: [],
});

// 混合模式状态
const currentFirstLevelMenu = ref(null); // 当前选中的一级菜单

// 分栏布局状态
const firstLevelSelectedKeys = ref(['workbench']); // 一级菜单选中状态

// 抽屉布局状态
const drawerMenuVisible = ref(false); // 抽屉菜单显示状态

// 用户信息
const userAvatar = computed(() => userStore.avatar || '../../assets/images/profile.jpg');
const userName = computed(() => userStore.nickName || '用户');

// 通过从后端获取的用户可以访问的菜单信息生产前端菜单列表
const sidebarRouters = computed(() => permissionStore.sidebarRouters || []);

// 布局设置
const layoutSettings = computed(() => appStore.layoutSettings || {
  primaryColor: '#1677ff',
  darkMode: false,
  navMode: 'side',
  menuTheme: 'light',
  layout: 'mix',
  contentWidth: 'fluid',
  fixedHeader: true,
  fixedSidebar: true,
  splitMenus: false,
  colorWeak: false,
  multiTab: true
});

// 计算主题相关的 CSS 变量
const themeVars = computed(() => {
  const primaryColor = layoutSettings.value?.primaryColor || '#1677ff';
  const primaryColorHover = `color-mix(in srgb, ${primaryColor} 90%, white)`;
  const primaryColorActive = `color-mix(in srgb, ${primaryColor} 110%, black)`;
  const primary1 = `color-mix(in srgb, ${primaryColor} 20%, white)`;

  // 更新根变量
  const root = document.documentElement;

  // 更新 Ant Design 基础变量
  root.style.setProperty('--ant-primary-color', primaryColor);
  root.style.setProperty('--ant-primary-color-hover', primaryColorHover);
  root.style.setProperty('--ant-primary-color-active', primaryColorActive);
  root.style.setProperty('--ant-primary-1', primary1);

  // 更新主题色变量
  root.style.setProperty('--primary-color', primaryColor);
  root.style.setProperty('--primary-color-hover', primaryColorHover);
  root.style.setProperty('--primary-color-active', primaryColorActive);
  root.style.setProperty('--primary-1', primary1);

  // 更新按钮相关变量
  root.style.setProperty('--btn-primary-bg', primaryColor);
  root.style.setProperty('--btn-hover-bg', primaryColorHover);
  root.style.setProperty('--btn-active-bg', primaryColorActive);

  return {
    primaryColor,
    primaryColorHover,
    primaryColorActive,
    primary1
  };
});

// 计算 CSS 变量
const cssVars = computed(() => ({})); // 移除所有变量，现在通过 root 设置

// 计算布局类名
const layoutClasses = computed(() => {
  return {
    'layout-side': layoutSettings.value.navMode === 'side',
    'layout-top': layoutSettings.value.navMode === 'top',
    'layout-mix': layoutSettings.value.navMode === 'mix',
    'layout-column': layoutSettings.value.navMode === 'column',
    'layout-drawer': layoutSettings.value.navMode === 'drawer',
    'fixed-header': layoutSettings.value.fixedHeader,
    'fixed-sidebar': layoutSettings.value.fixedSidebar,
    'dark-theme': layoutSettings.value.darkMode,
    'color-weak': layoutSettings.value.colorWeak,
  };
});

// 设置一级父菜单
const initRootMenus = () => {
  if (sidebarRouters.value && Array.isArray(sidebarRouters.value)) {
    sidebarRouters.value.forEach((element) => {
      menuState.rootSubmenuKeys.push(element.path);
    });
  }
  // 将工作台菜单添加到一级菜单节点数组中
  menuState.rootSubmenuKeys.push('workbench');
};

// 初始化路由状态
const initRouteState = () => {
  if (vueRoute.path) {
    let arr = vueRoute.path.split('/');
    // 如果有父菜单，获取父菜单key
    if (arr.length > 2) {
      menuState.openedFaterMenuKeys = [];
      menuState.openedFaterMenuKeys.push('/' + arr[1]);
      menuState.selectedMenuKeys = [];
      menuState.selectedMenuKeys.push(arr[2]);
    } else {
      // 如果没有父菜单
      if ('/home' === vueRoute.path) {
        // 如果是主页路由，则选中工作台菜单
        menuState.selectedMenuKeys.push('workbench');
      } else {
        menuState.openedFaterMenuKeys = [];
        menuState.selectedMenuKeys = [];
        menuState.selectedMenuKeys.push(arr[1]);
      }
    }

    // 刷新页面后，重新设置顶部的菜单名称
    if (sidebarRouters.value && Array.isArray(sidebarRouters.value)) {
      sidebarRouters.value.forEach((element) => {
        if (element.path === '/' + arr[1]) {
          headerInfo.currentFatherMenuTitle = element.meta?.title || '主页';
          if (element.children) {
            element.children.forEach((child) => {
              if (child.path === arr[2]) {
                headerInfo.currentMenuTitle = child.meta?.title || '';
              }
            });
          }
        }
      });
    }
  }
};

// 添加错误边界处理
const handleError = (error) => {
  console.error('页面发生错误:', error);
  message.error('操作失败，请稍后重试');
};

// 添加导航守卫
const handleRouteChange = (to) => {
  // 检查路由是否存在
  if (to.path === '/home' || to.path === '/login' || to.path === '/') {
    return true;
  }

  // 检查是否是有效的子路由
  const isValidRoute = userRouters.some(route => {
    if (route.path === to.path) return true;
    if (route.children) {
      return route.children.some(child => child.path === to.path);
    }
    return false;
  });

  if (!isValidRoute) {
    message.error('页面不存在');
    return false;
  }
  return true;
};

// 处理混合模式一级菜单选择
const handleFirstLevelMenuSelect = (menuInfo) => {
  try {
    console.log('一级菜单选择:', menuInfo);
    currentFirstLevelMenu.value = menuInfo;

    // 更新分栏布局的选中状态
    if (menuInfo) {
      const menuIndex = sidebarRouters.value.findIndex(item => item.path === menuInfo.path);
      firstLevelSelectedKeys.value = [`${menuInfo.path}-${menuIndex}`];
    } else {
      firstLevelSelectedKeys.value = ['workbench'];
      currentFirstLevelMenu.value = null;
    }
  } catch (error) {
    console.error('一级菜单选择错误:', error);
  }
};

// 处理工作台点击
const handleWorkbenchClick = () => {
  firstLevelSelectedKeys.value = ['workbench'];
  currentFirstLevelMenu.value = null;

  // 跳转到工作台
  handleMenuSelect({
    fatherName: 'HomePage',
    fatherPath: '/home',
    fatherTitle: '主页',
    name: 'Workbench',
    path: '/home/<USER>',
    title: '工作台',
    component: null
  });
};

// 处理抽屉菜单切换
const toggleDrawerMenu = () => {
  drawerMenuVisible.value = !drawerMenuVisible.value;
};

// 处理菜单选择并关闭抽屉
const handleMenuSelectAndClose = (menuInfo) => {
  handleMenuSelect(menuInfo);
  drawerMenuVisible.value = false;
};

// 处理二级菜单点击（分栏布局使用）
const handleMenuClick = (fatherName, fatherPath, fatherTitle, name, path, title, component) => {
  try {
    handleMenuSelect({
      fatherName,
      fatherPath,
      fatherTitle,
      name,
      path,
      title,
      component
    });
  } catch (error) {
    console.error('菜单点击错误:', error);
  }
};

// 处理三级菜单点击（分栏布局使用）
const handleThreeLevelMenuClick = (greatFatherPath, father, menu) => {
  try {
    handleMenuSelect({
      greatFatherPath,
      father,
      menu
    });
  } catch (error) {
    console.error('三级菜单点击错误:', error);
  }
};

// 处理菜单选择
const handleMenuSelect = (menuInfo) => {
  try {
    console.log('菜单选择信息:', menuInfo);

    // 如果没有子路径，说明是父菜单，不进行导航
    if (!menuInfo.path && !menuInfo.menu?.path) {
      return;
    }

    if (menuInfo.greatFatherPath) {
      // 处理三级菜单
      console.log('处理三级菜单');
      clickThreeLevelMenuItem(
          menuInfo.greatFatherPath,
          menuInfo.father.name,
          menuInfo.father.path,
          menuInfo.father.meta.title,
          menuInfo.menu.name,
          menuInfo.menu.path,
          menuInfo.menu.meta.title,
          menuInfo.menu.component
      );
    } else {
      // 处理二级菜单
      console.log('处理二级菜单');
      clickMenuItem(
          menuInfo.fatherName,
          menuInfo.fatherPath,
          menuInfo.fatherTitle,
          menuInfo.name,
          menuInfo.path,
          menuInfo.title,
          menuInfo.component
      );
    }
  } catch (error) {
    console.error('菜单选择错误:', error);
    message.error('菜单选择失败，请重试');
  }
};

// 优化路由跳转错误处理
const clickMenuItem = async (fatherMenuName, fatherMenuPath, fatherTitle, menuName, menuPath, menuTitle, menuComponent) => {
  try {
    loading.value = true;
    // 确保路径有效
    if (!fatherMenuPath || !menuPath) {
      throw new Error('无效的菜单路径');
    }

    if (menuPath === 'workbench') {
      //如果点击的是工作台菜单，则关闭其他已展开的父菜单
      menuState.openedFaterMenuKeys = [];
      await vueRouter.push({
        path: '/home/<USER>',
        meta: {title: '工作台'}
      });
    } else {
      let routePathStr = fatherMenuPath + '/' + menuPath;
      console.log('点击菜单后请求路由=' + routePathStr);
      await vueRouter.push({
        path: routePathStr,
        meta: {title: menuTitle}
      });

      headerInfo.currentFatherMenuTitle = fatherTitle;
      headerInfo.currentMenuTitle = menuTitle;
    }
  } catch (error) {
    handleError(error);
  } finally {
    loading.value = false;
  }
};

// 点击菜单项：跳转到对应的路由
const clickThreeLevelMenuItem = async (greatFatherMenuPath, fatherMenuName, fatherMenuPath, fatherTitle, menuName, menuPath, menuTitle, menuComponent) => {
  try {
    loading.value = true;

    console.log('三级菜单点击参数:', {
      greatFatherMenuPath,
      fatherMenuName,
      fatherMenuPath,
      fatherTitle,
      menuName,
      menuPath,
      menuTitle,
      menuComponent
    });

    // 检查当前注册的路由
    const routes = vueRouter.getRoutes();
    console.log('当前注册的路由:', routes.map(r => r.path));

    // 确保路径有效
    if (!menuPath) {
      throw new Error('无效的菜单路径');
    }

    headerInfo.currentFatherMenuTitle = fatherTitle;
    headerInfo.currentMenuTitle = menuTitle;

    // 根据后端路由扁平化处理，三级菜单的路径应该是完整的扁平路径
    // 例如：/monitor/operlog 而不是 /monitor/log/operlog
    let routePathStr;

    // 如果 menuPath 已经是完整路径（包含父路径），直接使用
    if (menuPath.includes('/')) {
      routePathStr = menuPath.startsWith('/') ? menuPath : '/' + menuPath;
    } else {
      // 否则拼接完整路径
      routePathStr = `/${greatFatherMenuPath}/${fatherMenuPath}/${menuPath}`.replace(/\/+/g, '/');
    }

    console.log('点击菜单后请求路由=' + routePathStr);

    // 检查路由是否存在
    const targetRoute = routes.find(r => r.path === routePathStr);
    if (!targetRoute) {
      console.error('路由不存在:', routePathStr);
      console.log('可用路由:', routes.filter(r => r.path.includes('monitor') || r.path.includes('operlog') || r.path.includes('logininfor')));
    }

    await vueRouter.push({
      path: routePathStr,
      meta: {title: menuTitle}
    });
  } catch (error) {
    console.error('三级菜单路由跳转错误:', error);
    handleError(error);
  } finally {
    loading.value = false;
  }
};

// 显示设置抽屉
const showDrawer = () => {
  drawerVisible.value = true;
};

// 优化的退出登录处理
const handleLogout = async () => {
  try {
    loading.value = true;
    await userStore.logout();
    message.success('退出登录成功');
    vueRouter.push({path: '/'});
  } catch (error) {
    message.error('退出登录失败');
  } finally {
    loading.value = false;
  }
};

// 进入个人中心
const enterPersonalCenter = () => {
  vueRouter.push({path: '/system/user/profile'});
};

// 初始化加载
onMounted(() => {
  // 应用主题设置
  appStore.applyTheme();

  // 立即应用主题变量
  nextTick(() => {
    // 触发 themeVars 计算
    themeVars.value;

    // 设置初始主题
    if (layoutSettings.value.darkMode) {
      document.documentElement.classList.add('dark-theme');
    }

    // 设置系统标题
    if (appStore.systemConfig.title) {
      document.title = appStore.systemConfig.title;
    }
  });

  // 添加全局导航守卫
  vueRouter.beforeEach((to, from, next) => {
    if (handleRouteChange(to)) {
      next();
    } else {
      next('/404');
    }
  });

  initRootMenus();
  initRouteState();
});

// 监听布局设置变化并保存
watch(layoutSettings, (newSettings) => {
  try {
    localStorage.setItem('layoutSettings', JSON.stringify(newSettings));

    // 应用主题变化
    appStore.applyTheme();

    // 强制重新计算主题变量
    nextTick(() => {
      themeVars.value;
    });
  } catch (error) {
    console.error('布局设置变化处理失败:', error);
  }
}, {deep: true});

// 处理设置变更
const handleSettingsChange = (newSettings) => {
  try {
    console.log('BaseLayout 接收到设置变更:', newSettings);
    appStore.updateSettings(newSettings);

    // 立即应用主题变量
    nextTick(() => {
      // 触发 themeVars 计算
      themeVars.value;
      console.log('当前布局设置:', layoutSettings.value);
    });
  } catch (error) {
    console.error('设置变更处理失败:', error);
  }
};
// 刷新当前页面内容
const refreshCurrentPage = async () => {
  try {
    loading.value = true;

    // 使用provide/inject方式刷新页面，避免路由重定向
    // 生成一个随机key，强制router-view重新渲染
    const timestamp = new Date().getTime();
    // 保存当前滚动位置
    const scrollPosition = document.documentElement.scrollTop || document.body.scrollTop;

    // 强制重新获取路由数据
    if (vueRoute.meta && vueRoute.meta.keepAlive === false) {
      // 如果页面配置了不缓存，则重新加载数据
      const currentComponent = vueRoute.matched[vueRoute.matched.length - 1].instances.default;
      if (currentComponent && typeof currentComponent.fetchData === 'function') {
        await currentComponent.fetchData();
      }
    } else {
      // 通过修改查询参数的方式刷新页面
      const {fullPath, query} = vueRoute;
      const newQuery = {...query, _refresh: timestamp};
      await vueRouter.replace({path: vueRoute.path, query: newQuery});
    }

    // 恢复滚动位置
    nextTick(() => {
      window.scrollTo(0, scrollPosition);
    });

    message.success('页面刷新成功');
  } catch (error) {
    console.error('页面刷新失败:', error);
    message.error('页面刷新失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 替换 mapState 和 mapGetters
computed(() => appStore.sidebar);
const device = computed(() => appStore.device);
const avatar = computed(() => userStore.avatar);
const name = computed(() => userStore.name);
const roles = computed(() => userStore.roles);
const permissions = computed(() => userStore.permissions);
const routes = computed(() => permissionStore.routes);
const visitedViews = computed(() => tagsViewStore.visitedViews);
const cachedViews = computed(() => tagsViewStore.cachedViews);
</script>

<style lang="less" scoped>
.app-container {
  height: 100vh;

  .layout-container {
    height: 100%;
    background: #fafbfc;

    &__content {
      margin: 16px;
      overflow-y: auto;

      .content-wrapper {
        background: #fafbfc;
        border-radius: 16px;
        min-height: calc(100vh - 170px);
        padding: 2px;
      }
    }
  }
}

// 分栏布局logo样式
.menu-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;

  .logo-img {
    height: 40px;
    width: auto;
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.05);
    }
  }
}
</style>
