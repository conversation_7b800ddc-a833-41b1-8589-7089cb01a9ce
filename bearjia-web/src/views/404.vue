<template>
  <div class="not-found">
    <div class="not-found-content">
      <h1 class="error-code">404</h1>
      <h2 class="error-title">页面未找到</h2>
      <p class="error-desc">抱歉，您访问的页面不存在</p>
      <a-button type="primary" @click="goHome">返回首页</a-button>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
  router.push('/home')
}
</script>

<style lang="less" scoped>
.not-found {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 500px;
  padding: 32px;
  
  .not-found-content {
    text-align: center;
    
    .error-code {
      font-size: 120px;
      font-weight: bold;
      color: #1890ff;
      margin: 0;
      line-height: 1.2;
    }
    
    .error-title {
      font-size: 24px;
      color: rgba(0, 0, 0, 0.85);
      margin: 16px 0;
    }
    
    .error-desc {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.45);
      margin-bottom: 24px;
    }
  }
}
</style>
