<template>
  <div class="error-page">
    <div class="error-content">
      <h1>404</h1>
      <h2>页面未找到</h2>
      <p>抱歉，您访问的页面不存在或已被移除</p>
      <a-button type="primary" @click="goHome">返回首页</a-button>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';

const router = useRouter();

const goHome = () => {
  router.push('/');
};
</script>

<style lang="less" scoped>
.error-page {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f0f2f5;

  .error-content {
    text-align: center;

    h1 {
      font-size: 120px;
      color: #1890ff;
      margin-bottom: 16px;
    }

    h2 {
      font-size: 24px;
      color: rgba(0, 0, 0, 0.85);
      margin-bottom: 16px;
    }

    p {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.45);
      margin-bottom: 24px;
    }
  }
}
</style> 