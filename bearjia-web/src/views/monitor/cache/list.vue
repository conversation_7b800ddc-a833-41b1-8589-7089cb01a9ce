<template>
  <page-header-wrapper>
    <a-space direction="vertical" style="width: 100%" :size="16">
      <a-row :gutter="24">
        <a-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
          <a-card 
            :loading="loading" 
            title="缓存列表" 
            :bordered="false"
            class="modern-card"
            :bodyStyle="{ padding: '12px' }">
            <template #extra>
              <a-popconfirm
                ok-text="是"
                cancel-text="否"
                @confirm="handleClearCacheAll"
              >
                <template #title>确认<b>清除全部缓存</b>吗?</template>
                <a-button type="link" danger size="small" class="action-btn">
                  <delete-outlined />
                </a-button>
              </a-popconfirm>
              <a-divider type="vertical" />
              <a-button type="link" size="small" @click="refreshCacheNames" class="action-btn">
                <sync-outlined :spin="loading" />
              </a-button>
            </template>
            <a-table
              :loading="loading"
              size="middle"
              rowKey="cacheName"
              :columns="cacheColumns"
              :data-source="cacheNames"
              :customRow="customRow"
              :pagination="false"
              :bordered="false"
              class="modern-table">
              <template #cacheName="{ record }">
                {{ nameFormatter(record) }}
              </template>
              <template #operation="{ record }">
                <a-popconfirm
                  ok-text="是"
                  cancel-text="否"
                  @confirm="handleClearCacheName(record)"
                >
                  <template #title>确认<b>删除</b>缓存: {{ record.cacheName }} 吗?</template>
                  <a-button type="link" danger size="small" class="action-btn">
                    <delete-outlined />
                  </a-button>
                </a-popconfirm>
              </template>
            </a-table>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
          <a-card 
            :loading="loading" 
            title="键名列表" 
            :bordered="false"
            class="modern-card"
            :bodyStyle="{ padding: '12px', maxHeight: '400px', overflowY: 'auto' }">
            <template #extra>
              <a-button type="link" size="small" @click="refreshCacheKeys" class="action-btn">
                <sync-outlined :spin="subLoading" />
              </a-button>
            </template>
            <a-list :data-source="cacheKeys" class="modern-list">
              <template #header>
                <div class="cache-name-header">{{ nowCacheName || '请选择缓存' }}</div>
              </template>
              <template #renderItem="item">
                <a-list-item>
                  <template #actions>
                    <a-popconfirm
                      ok-text="是"
                      cancel-text="否"
                      @confirm="handleClearCacheKey(item)"
                    >
                      <template #title>确认<b>删除</b>缓存: {{ item }} 吗?</template>
                      <a-button type="link" danger size="small" class="action-btn">
                        <delete-outlined />
                      </a-button>
                    </a-popconfirm>
                  </template>
                  <a-list-item-meta @click="handleCacheValue(item)" class="clickable-item">
                    <template #description>
                      <ellipsis :length="35">
                        {{ keyFormatter(item) }}
                      </ellipsis>
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
          <a-card 
            :loading="loading" 
            title="缓存内容" 
            :bordered="false"
            class="modern-card"
            :bodyStyle="{ padding: '16px' }">
            <a-descriptions size="middle" style="word-break: break-all;" layout="vertical" :column="3" bordered class="modern-descriptions">
              <a-descriptions-item label="缓存名称" :span="3">
                <div class="description-content">{{ cacheForm.cacheName || '-' }}</div>
              </a-descriptions-item>
              <a-descriptions-item label="缓存键名" :span="3">
                <div class="description-content">{{ cacheForm.cacheKey || '-' }}</div>
              </a-descriptions-item>
              <a-descriptions-item label="缓存内容" :span="3">
                <div class="description-content">{{ cacheForm.cacheValue || '-' }}</div>
              </a-descriptions-item>
            </a-descriptions>
          </a-card>
        </a-col>
      </a-row>
    </a-space>
  </page-header-wrapper>
</template>

<script setup name="CacheList">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { listCacheName, listCacheKey, getCacheValue, clearCacheName, clearCacheKey, clearCacheAll } from '@/api/monitor/cache'
import { Ellipsis } from '@/components/Ellipsis'
import { DeleteOutlined, SyncOutlined } from '@ant-design/icons-vue'

const loading = ref(true)
const subLoading = ref(false)
const nowCacheName = ref('')

const cacheColumns = [
  {
    title: '缓存名称',
    dataIndex: 'cacheName',
    slots: { customRender: 'cacheName' },
    ellipsis: true,
    align: 'center'
  },
  {
    title: '备注',
    dataIndex: 'remark',
    ellipsis: true,
    align: 'center'
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: '20%',
    slots: { customRender: 'operation' },
    align: 'center'
  }
]

const cacheSubColumns = [
  {
    title: '缓存键名',
    dataIndex: 'cacheKey',
    slots: { customRender: 'cacheKey' },
    ellipsis: true,
    align: 'center'
  },
  {
    title: '操作',
    dataIndex: 'operation',
    slots: { customRender: 'operation' },
    align: 'center'
  }
]

const cacheNames = ref([])
const cacheKeys = ref([])
const cacheForm = reactive({})

/** 查询缓存名称列表 */
const getCacheNames = () => {
  loading.value = true
  listCacheName().then(response => {
    cacheNames.value = response.data
    loading.value = false
  })
}

const customRow = (record) => {
  const style = record.cacheName === nowCacheName.value ? {} : { opacity: 0.7 }
  return {
    on: {
      click: () => {
        getCacheKeys(record)
      }
    },
    style
  }
}

/** 刷新缓存名称列表 */
const refreshCacheNames = () => {
  getCacheNames()
  message.success('刷新缓存列表成功', 3)
}

/** 清理指定名称缓存 */
const handleClearCacheName = (row) => {
  clearCacheName(row.cacheName).then(response => {
    message.success('清理缓存名称[' + nowCacheName.value + ']成功', 3)
    getCacheKeys()
  })
}

/** 查询缓存键名列表 */
const getCacheKeys = (row) => {
  const cacheName = row !== undefined ? row.cacheName : nowCacheName.value
  if (cacheName === '') {
    return
  }
  subLoading.value = true
  listCacheKey(cacheName).then(response => {
    cacheKeys.value = response.data
    subLoading.value = false
    nowCacheName.value = cacheName
  })
}

/** 刷新缓存键名列表 */
const refreshCacheKeys = () => {
  getCacheKeys()
  message.success('刷新键名列表成功', 3)
}

/** 清理指定键名缓存 */
const handleClearCacheKey = (cacheKey) => {
  clearCacheKey(cacheKey).then(response => {
    message.success('清理缓存键名[' + cacheKey + ']成功', 3)
    getCacheKeys()
  })
}

/** 列表前缀去除 */
const nameFormatter = (row) => {
  return row.cacheName.replace(':', '')
}

/** 键名前缀去除 */
const keyFormatter = (cacheKey) => {
  return cacheKey.replace(nowCacheName.value, '')
}

/** 查询缓存内容详细 */
const handleCacheValue = (cacheKey) => {
  getCacheValue(nowCacheName.value, cacheKey).then(response => {
    Object.assign(cacheForm, response.data)
  })
}

/** 清理全部缓存 */
const handleClearCacheAll = () => {
  clearCacheAll().then(response => {
    message.success('清理全部缓存成功', 3)
  })
}

// 初始化加载
onMounted(() => {
  getCacheNames()
})
</script>

<style scoped>
.modern-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
  transition: all 0.3s;
  margin-bottom: 16px;
}

.modern-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.modern-table {
  border-radius: 8px;
}

.modern-list {
  border-radius: 8px;
}

.action-btn {
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s;
}

.action-btn:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.clickable-item {
  cursor: pointer;
  transition: all 0.3s;
  padding: 4px;
  border-radius: 4px;
}

.clickable-item:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.cache-name-header {
  font-weight: 500;
  color: #1890ff;
  padding: 8px 0;
}

.modern-descriptions :deep(.ant-descriptions-item-label) {
  background-color: #fafafa;
  font-weight: 500;
}

.description-content {
  padding: 8px 0;
  min-height: 32px;
  max-height: 200px;
  overflow-y: auto;
}
</style>
