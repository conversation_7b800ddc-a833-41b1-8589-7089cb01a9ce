<template>
  <a-spin :spinning="loading" tip="加载中...">
    <div :style="'height:'+ height">
      <iframe :src="src" frameborder="no" style="width: 100%;height: 100%" scrolling="auto" />
    </div>
  </a-spin>
</template>

<script setup name="Druid">
import { ref, onMounted } from 'vue'

const src = ref(process.env.VUE_APP_BASE_API + '/druid/login.html')
const height = ref(document.documentElement.clientHeight - 94.5 + 'px;')
const loading = ref(true)

onMounted(() => {
  setTimeout(() => {
    loading.value = false
  }, 230)

  window.onresize = () => {
    height.value = document.documentElement.clientHeight - 94.5 + 'px;'
  }
})
</script>
