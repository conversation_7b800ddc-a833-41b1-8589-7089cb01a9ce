<template>
  <div class="simple-react-test">
    <h2>🧪 ReAct智能体简单测试</h2>
    
    <div class="test-section">
      <h3>测试输入</h3>
      <a-input 
        v-model:value="testInput" 
        placeholder="输入测试消息，例如：查询系统有几个用户"
        @press-enter="runTest"
      />
      <a-button 
        type="primary" 
        @click="runTest" 
        :loading="testing"
        style="margin-top: 10px;"
      >
        🚀 测试ReAct
      </a-button>
    </div>

    <div v-if="testResult" class="result-section">
      <h3>测试结果</h3>
      
      <div class="result-basic">
        <p><strong>成功状态：</strong> 
          <a-tag :color="testResult.success ? 'green' : 'red'">
            {{ testResult.success ? '成功' : '失败' }}
          </a-tag>
        </p>
        <p><strong>响应消息：</strong> {{ testResult.message }}</p>
        <p><strong>ReAct历史数量：</strong> {{ testResult.historyCount }}</p>
      </div>

      <div v-if="testResult.reActHistory && testResult.reActHistory.length > 0" class="react-history">
        <h4>🔄 ReAct执行过程</h4>
        <div 
          v-for="(turn, index) in testResult.reActHistory" 
          :key="index"
          class="turn-card"
        >
          <div class="turn-header">
            <span class="turn-number">第{{ turn.turnNumber || (index + 1) }}轮</span>
            <span v-if="turn.duration" class="turn-duration">{{ turn.duration }}ms</span>
          </div>

          <div v-if="turn.thought" class="turn-item">
            <strong>💭 思考：</strong>
            <div class="turn-content">{{ turn.thought }}</div>
          </div>

          <div v-if="turn.action" class="turn-item">
            <strong>🎯 行动：</strong>
            <div class="turn-content">
              <div>函数：<code>{{ turn.action.functionName }}</code></div>
              <div>参数：<pre>{{ JSON.stringify(turn.action.extractedParams || turn.action.params || {}, null, 2) }}</pre></div>
            </div>
          </div>

          <div v-if="turn.observation" class="turn-item">
            <strong>👀 观察：</strong>
            <div class="turn-content">{{ turn.observation }}</div>
          </div>

          <div v-if="turn.finalAnswer" class="turn-item final">
            <strong>✅ 最终答案：</strong>
            <div class="turn-content">{{ turn.finalAnswer }}</div>
          </div>

          <div v-if="turn.isError || turn.error" class="turn-item error">
            <strong>❌ 错误：</strong>
            <div class="turn-content">{{ turn.errorMessage || '未知错误' }}</div>
          </div>
        </div>
      </div>

      <div class="raw-response">
        <h4>📄 原始响应</h4>
        <pre>{{ JSON.stringify(testResult.rawResponse, null, 2) }}</pre>
      </div>
    </div>

    <div class="quick-tests">
      <h3>快速测试用例</h3>
      <div class="test-buttons">
        <a-button @click="quickTest('查询系统有几个用户')" :loading="testing">
          简单查询
        </a-button>
        <a-button @click="quickTest('查询所有状态为正常的用户')" :loading="testing">
          复杂查询
        </a-button>
        <a-button @click="quickTest('使用react智能体查询用户列表')" :loading="testing">
          强制ReAct
        </a-button>
        <a-button @click="quickTest('删除用户名为测试的用户')" :loading="testing">
          多步操作
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import { testReAct } from '@/api/ai'

const testInput = ref('查询系统有几个用户，分别是谁')
const testing = ref(false)
const testResult = ref(null)

const runTest = async () => {
  if (!testInput.value.trim()) {
    message.warning('请输入测试消息')
    return
  }

  testing.value = true
  testResult.value = null

  try {
    console.log('🧪 开始ReAct测试:', testInput.value)
    
    const response = await testReAct({
      message: testInput.value,
      conversationHistory: []
    })

    console.log('🧪 ReAct响应:', response)

    // 解析响应
    const success = response && response.code === 200
    const data = response.data || {}
    
    testResult.value = {
      success: success,
      message: data.message || '无响应消息',
      historyCount: data.reActHistory ? data.reActHistory.length : 0,
      reActHistory: data.reActHistory || [],
      rawResponse: response
    }

    if (success) {
      message.success('ReAct测试完成')
    } else {
      message.error('ReAct测试失败: ' + (response.msg || '未知错误'))
    }

  } catch (error) {
    console.error('🧪 ReAct测试异常:', error)
    
    testResult.value = {
      success: false,
      message: '测试异常: ' + error.message,
      historyCount: 0,
      reActHistory: [],
      rawResponse: { error: error.message }
    }

    message.error('测试异常: ' + error.message)
  } finally {
    testing.value = false
  }
}

const quickTest = (testMessage) => {
  testInput.value = testMessage
  runTest()
}
</script>

<style scoped>
.simple-react-test {
  padding: 24px;
  max-width: 1000px;
  margin: 0 auto;
}

.simple-react-test h2 {
  color: #1890ff;
  text-align: center;
  margin-bottom: 32px;
}

.test-section {
  background-color: #fafafa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 24px;
}

.result-section {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
}

.result-basic {
  margin-bottom: 20px;
}

.result-basic p {
  margin-bottom: 8px;
}

.react-history {
  margin-bottom: 20px;
}

.turn-card {
  background-color: white;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 12px;
}

.turn-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.turn-number {
  font-weight: bold;
  color: #1890ff;
}

.turn-duration {
  font-size: 12px;
  color: #666;
}

.turn-item {
  margin-bottom: 12px;
}

.turn-item:last-child {
  margin-bottom: 0;
}

.turn-content {
  margin-top: 4px;
  padding-left: 16px;
  color: #666;
  line-height: 1.6;
}

.turn-item.final .turn-content {
  background-color: #f6ffed;
  padding: 8px;
  border-radius: 4px;
  border-left: 4px solid #52c41a;
  color: #333;
}

.turn-item.error .turn-content {
  background-color: #fff2f0;
  padding: 8px;
  border-radius: 4px;
  border-left: 4px solid #ff4d4f;
  color: #ff4d4f;
}

.turn-content code {
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

.turn-content pre {
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
  margin-top: 4px;
}

.raw-response {
  margin-top: 20px;
}

.raw-response pre {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
  max-height: 300px;
  overflow-y: auto;
}

.quick-tests {
  background-color: #f0f8ff;
  padding: 20px;
  border-radius: 8px;
}

.test-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.test-buttons .ant-btn {
  margin-bottom: 8px;
}
</style>
