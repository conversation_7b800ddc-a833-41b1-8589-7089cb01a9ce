<template>
  <div class="react-test">
    <div class="test-header">
      <h2>🧪 ReAct智能体测试</h2>
      <p>用于调试和测试ReAct智能体的功能</p>
    </div>

    <div class="test-content">
      <!-- 测试输入 -->
      <div class="test-input">
        <h3>测试输入</h3>
        <a-textarea
          v-model:value="testMessage"
          placeholder="输入测试消息，例如：查询系统有几个用户"
          :rows="3"
        />
        <div class="test-buttons">
          <a-button type="primary" @click="testReAct" :loading="testing">
            🚀 测试ReAct
          </a-button>
          <a-button @click="testNormal" :loading="testing">
            📝 测试普通模式
          </a-button>
          <a-button @click="clearResults">
            🗑️ 清空结果
          </a-button>
        </div>
      </div>

      <!-- 测试结果 -->
      <div v-if="testResults.length > 0" class="test-results">
        <h3>测试结果</h3>
        <div 
          v-for="(result, index) in testResults" 
          :key="index"
          class="result-item"
          :class="{ success: result.success, error: !result.success }"
        >
          <div class="result-header">
            <span class="result-title">{{ result.title }}</span>
            <span class="result-time">{{ result.timestamp }}</span>
            <a-tag :color="result.success ? 'green' : 'red'">
              {{ result.success ? '成功' : '失败' }}
            </a-tag>
          </div>

          <div class="result-content">
            <!-- 基本信息 -->
            <div class="result-section">
              <h4>基本信息</h4>
              <pre>{{ JSON.stringify(result.basicInfo, null, 2) }}</pre>
            </div>

            <!-- ReAct历史 -->
            <div v-if="result.reActHistory && result.reActHistory.length > 0" class="result-section">
              <h4>ReAct执行历史 ({{ result.reActHistory.length }}轮)</h4>
              <div class="react-history">
                <div 
                  v-for="(turn, turnIndex) in result.reActHistory" 
                  :key="turnIndex"
                  class="turn-item"
                >
                  <div class="turn-header">
                    <span class="turn-number">第{{ turn.turnNumber || (turnIndex + 1) }}轮</span>
                    <span class="turn-duration" v-if="turn.duration">{{ turn.duration }}ms</span>
                  </div>

                  <div v-if="turn.thought" class="turn-section">
                    <strong>💭 思考：</strong>
                    <div class="turn-content">{{ turn.thought }}</div>
                  </div>

                  <div v-if="turn.action" class="turn-section">
                    <strong>🎯 行动：</strong>
                    <div class="turn-content">
                      <div>函数：{{ turn.action.functionName }}</div>
                      <div>参数：{{ JSON.stringify(turn.action.extractedParams || turn.action.params, null, 2) }}</div>
                    </div>
                  </div>

                  <div v-if="turn.observation" class="turn-section">
                    <strong>👀 观察：</strong>
                    <div class="turn-content">{{ turn.observation }}</div>
                  </div>

                  <div v-if="turn.finalAnswer" class="turn-section">
                    <strong>✅ 最终答案：</strong>
                    <div class="turn-content final-answer">{{ turn.finalAnswer }}</div>
                  </div>

                  <div v-if="turn.isError || turn.error" class="turn-section error">
                    <strong>❌ 错误：</strong>
                    <div class="turn-content">{{ turn.errorMessage }}</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 原始响应 -->
            <div class="result-section">
              <h4>原始响应</h4>
              <pre class="raw-response">{{ JSON.stringify(result.rawResponse, null, 2) }}</pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import { chatWithAi } from '@/api/ai'
import request from '@/utils/request'

const testMessage = ref('查询系统有几个用户，分别是谁')
const testing = ref(false)
const testResults = ref([])

// 测试ReAct模式
const testReAct = async () => {
  if (!testMessage.value.trim()) {
    message.warning('请输入测试消息')
    return
  }

  testing.value = true
  try {
    console.log('开始测试ReAct模式')
    
    const response = await request({
      url: '/ai/test-react',
      method: 'post',
      data: {
        message: testMessage.value,
        conversationHistory: []
      }
    })

    console.log('ReAct测试响应:', response)

    const result = {
      title: 'ReAct智能体测试',
      timestamp: new Date().toLocaleString(),
      success: response.code === 200,
      basicInfo: {
        success: response.data?.success,
        message: response.data?.message,
        hasReActHistory: response.data?.reActHistory ? response.data.reActHistory.length : 0,
        responseType: response.data?.type
      },
      reActHistory: response.data?.reActHistory || [],
      rawResponse: response
    }

    testResults.value.unshift(result)
    
    if (result.success) {
      message.success('ReAct测试完成')
    } else {
      message.error('ReAct测试失败')
    }

  } catch (error) {
    console.error('ReAct测试异常:', error)
    
    const result = {
      title: 'ReAct智能体测试 (异常)',
      timestamp: new Date().toLocaleString(),
      success: false,
      basicInfo: {
        error: error.message,
        stack: error.stack
      },
      reActHistory: [],
      rawResponse: { error: error.message }
    }

    testResults.value.unshift(result)
    message.error('ReAct测试异常: ' + error.message)
  } finally {
    testing.value = false
  }
}

// 测试普通模式
const testNormal = async () => {
  if (!testMessage.value.trim()) {
    message.warning('请输入测试消息')
    return
  }

  testing.value = true
  try {
    console.log('开始测试普通模式')
    
    const response = await chatWithAi({
      message: testMessage.value,
      conversationHistory: []
    })

    console.log('普通模式测试响应:', response)

    const result = {
      title: '普通模式测试',
      timestamp: new Date().toLocaleString(),
      success: response && (response.code === 200 || response.success !== false),
      basicInfo: {
        success: response?.success,
        message: response?.message || response?.data?.message,
        responseType: response?.type || response?.data?.type
      },
      reActHistory: [],
      rawResponse: response
    }

    testResults.value.unshift(result)
    
    if (result.success) {
      message.success('普通模式测试完成')
    } else {
      message.error('普通模式测试失败')
    }

  } catch (error) {
    console.error('普通模式测试异常:', error)
    
    const result = {
      title: '普通模式测试 (异常)',
      timestamp: new Date().toLocaleString(),
      success: false,
      basicInfo: {
        error: error.message
      },
      reActHistory: [],
      rawResponse: { error: error.message }
    }

    testResults.value.unshift(result)
    message.error('普通模式测试异常: ' + error.message)
  } finally {
    testing.value = false
  }
}

// 清空结果
const clearResults = () => {
  testResults.value = []
  message.info('已清空测试结果')
}
</script>

<style scoped>
.react-test {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-header {
  text-align: center;
  margin-bottom: 32px;
}

.test-header h2 {
  color: #1890ff;
  margin-bottom: 8px;
}

.test-input {
  background-color: #fafafa;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 24px;
}

.test-input h3 {
  margin-bottom: 16px;
}

.test-buttons {
  margin-top: 16px;
  display: flex;
  gap: 12px;
}

.test-results {
  margin-top: 24px;
}

.result-item {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;
}

.result-item.success {
  border-color: #52c41a;
}

.result-item.error {
  border-color: #ff4d4f;
}

.result-header {
  background-color: #f5f5f5;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.result-title {
  font-weight: bold;
}

.result-time {
  font-size: 12px;
  color: #666;
}

.result-content {
  padding: 16px;
}

.result-section {
  margin-bottom: 24px;
}

.result-section h4 {
  margin-bottom: 8px;
  color: #333;
}

.result-section pre {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
}

.react-history {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
}

.turn-item {
  border-bottom: 1px solid #e8e8e8;
  padding: 16px;
}

.turn-item:last-child {
  border-bottom: none;
}

.turn-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.turn-number {
  font-weight: bold;
  color: #1890ff;
}

.turn-duration {
  font-size: 12px;
  color: #666;
}

.turn-section {
  margin-bottom: 12px;
}

.turn-section:last-child {
  margin-bottom: 0;
}

.turn-content {
  margin-top: 4px;
  padding-left: 16px;
  color: #666;
  line-height: 1.6;
}

.final-answer {
  background-color: #f6ffed;
  padding: 8px;
  border-radius: 4px;
  border-left: 4px solid #52c41a;
}

.turn-section.error .turn-content {
  color: #ff4d4f;
  background-color: #fff2f0;
  padding: 8px;
  border-radius: 4px;
}

.raw-response {
  max-height: 300px;
  overflow-y: auto;
}
</style>
