<template>
  <div class="simple-stream-test">
    <h2>🌊 简单流式测试</h2>
    
    <div class="test-input">
      <a-input 
        v-model:value="testMessage" 
        placeholder="输入测试消息"
        @press-enter="startTest"
      />
      <a-button 
        type="primary" 
        @click="startTest" 
        :loading="testing"
        style="margin-left: 10px;"
      >
        测试
      </a-button>
    </div>

    <div class="messages" v-if="messages.length > 0">
      <h3>消息流 ({{ messages.length }}条)</h3>
      <div 
        v-for="(msg, index) in messages" 
        :key="index"
        class="message"
        :class="msg.type"
      >
        <span class="type">{{ msg.type }}</span>
        <span class="content">{{ msg.data }}</span>
        <span class="time">{{ msg.time }}</span>
      </div>
    </div>

    <div class="status">
      <p>状态: {{ testing ? '测试中...' : '空闲' }}</p>
      <p v-if="error">错误: {{ error }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { message } from 'ant-design-vue'

const testMessage = ref('系统中有多少条通知公告')
const testing = ref(false)
const messages = ref([])
const error = ref('')

const startTest = async () => {
  if (!testMessage.value.trim()) {
    message.warning('请输入测试消息')
    return
  }

  testing.value = true
  messages.value = []
  error.value = ''

  try {
    console.log('开始流式测试:', testMessage.value)
    
    const response = await fetch('/dev-api/ai/chat/stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream'
      },
      body: JSON.stringify({
        message: testMessage.value,
        conversationHistory: []
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const reader = response.body.getReader()
    const decoder = new TextDecoder()

    while (true) {
      const { done, value } = await reader.read()
      
      if (done) {
        console.log('流式响应结束')
        testing.value = false
        break
      }

      const chunk = decoder.decode(value, { stream: true })
      console.log('收到数据块:', chunk)
      
      const lines = chunk.split('\n')

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          try {
            const data = line.substring(6).trim()
            if (data === '') continue
            
            const eventData = JSON.parse(data)
            
            messages.value.push({
              type: eventData.name || 'unknown',
              data: eventData.data || '',
              time: new Date().toLocaleTimeString()
            })

            console.log('解析事件:', eventData)

            // 如果收到完成或错误信号，结束测试
            if (eventData.name === 'done' || eventData.name === 'complete') {
              console.log('收到完成信号')
              testing.value = false
              message.success('测试完成')
              return
            }

            if (eventData.name === 'error') {
              console.error('收到错误信号:', eventData.data)
              error.value = eventData.data
              testing.value = false
              message.error('测试出错')
              return
            }

          } catch (e) {
            console.warn('解析SSE数据失败:', line, e)
          }
        }
      }
    }

  } catch (err) {
    console.error('流式测试异常:', err)
    error.value = err.message
    testing.value = false
    message.error('测试异常: ' + err.message)
  }
}
</script>

<style scoped>
.simple-stream-test {
  padding: 24px;
  max-width: 800px;
  margin: 0 auto;
}

.test-input {
  margin-bottom: 24px;
  display: flex;
  align-items: center;
}

.messages {
  margin-bottom: 24px;
}

.message {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 4px;
  border-radius: 4px;
  background-color: #f5f5f5;
}

.message.info {
  background-color: #e6f7ff;
  border-left: 4px solid #1890ff;
}

.message.message {
  background-color: #f6ffed;
  border-left: 4px solid #52c41a;
}

.message.error {
  background-color: #fff2f0;
  border-left: 4px solid #ff4d4f;
}

.message.done {
  background-color: #f6ffed;
  border-left: 4px solid #52c41a;
}

.type {
  font-weight: bold;
  min-width: 80px;
  font-size: 12px;
}

.content {
  flex: 1;
  margin: 0 12px;
}

.time {
  font-size: 11px;
  color: #666;
  min-width: 80px;
  text-align: right;
}

.status {
  padding: 16px;
  background-color: #fafafa;
  border-radius: 4px;
}
</style>
