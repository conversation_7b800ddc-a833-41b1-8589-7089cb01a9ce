<template>
  <div class="react-demo">
    <div class="demo-header">
      <h2>🤖 ReAct智能体演示</h2>
      <p class="demo-description">
        体验AI智能体的思考-行动-观察循环，观察AI如何自主规划并执行复杂任务。
      </p>
    </div>

    <div class="demo-content">
      <!-- 预设复杂场景 -->
      <div class="complex-scenarios">
        <h3>🎯 复杂任务场景</h3>
        <div class="scenario-grid">
          <div 
            v-for="scenario in complexScenarios" 
            :key="scenario.id"
            class="scenario-card"
            @click="selectScenario(scenario)"
            :class="{ active: selectedScenario?.id === scenario.id }"
          >
            <div class="scenario-icon">{{ scenario.icon }}</div>
            <div class="scenario-title">{{ scenario.title }}</div>
            <div class="scenario-desc">{{ scenario.description }}</div>
            <div class="scenario-complexity">
              <span class="complexity-badge" :class="scenario.complexity">
                {{ scenario.complexity === 'high' ? '高复杂度' : scenario.complexity === 'medium' ? '中复杂度' : '低复杂度' }}
              </span>
              <span class="steps-count">预计{{ scenario.expectedSteps }}步</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="input-section">
        <h3>💬 输入复杂任务</h3>
        <div class="input-group">
          <a-textarea
            v-model:value="userInput"
            placeholder="请输入复杂任务，例如：删除研发部所有状态为禁用的用户"
            :rows="3"
            class="user-input"
          />
          <a-button 
            type="primary" 
            @click="startReActProcess"
            :loading="processing"
            class="start-btn"
            size="large"
          >
            🚀 启动智能体
          </a-button>
        </div>
      </div>

      <!-- ReAct执行过程 -->
      <div v-if="reActHistory.length > 0" class="react-process">
        <h3>🔄 智能体执行过程</h3>
        
        <div class="process-timeline">
          <div 
            v-for="(turn, index) in reActHistory" 
            :key="index"
            class="turn-item"
            :class="{ 
              active: index === currentTurn,
              completed: turn.status === 'completed',
              error: turn.status === 'error'
            }"
          >
            <div class="turn-header">
              <div class="turn-number">{{ index + 1 }}</div>
              <div class="turn-title">第{{ index + 1 }}轮</div>
              <div class="turn-status">
                <a-tag :color="getStatusColor(turn.status)">
                  {{ getStatusText(turn.status) }}
                </a-tag>
              </div>
              <div class="turn-duration" v-if="turn.duration">
                {{ turn.duration }}ms
              </div>
            </div>

            <!-- 思考过程 -->
            <div v-if="turn.thought" class="turn-section thought-section">
              <div class="section-header">
                <BulbOutlined class="section-icon" />
                <span class="section-title">思考</span>
              </div>
              <div class="section-content">
                {{ turn.thought }}
              </div>
            </div>

            <!-- 行动 -->
            <div v-if="turn.action" class="turn-section action-section">
              <div class="section-header">
                <ThunderboltOutlined class="section-icon" />
                <span class="section-title">行动</span>
              </div>
              <div class="section-content">
                <div class="action-info">
                  <span class="function-name">{{ turn.action.functionName }}</span>
                  <a-tag v-if="turn.action.dangerous" color="red">危险操作</a-tag>
                </div>
                <div class="action-params">
                  <pre>{{ JSON.stringify(turn.action.params, null, 2) }}</pre>
                </div>
              </div>
            </div>

            <!-- 观察结果 -->
            <div v-if="turn.observation" class="turn-section observation-section">
              <div class="section-header">
                <EyeOutlined class="section-icon" />
                <span class="section-title">观察</span>
              </div>
              <div class="section-content">
                <div class="observation-result" :class="{ error: turn.observation.includes('错误') }">
                  {{ turn.observation }}
                </div>
              </div>
            </div>

            <!-- 最终答案 -->
            <div v-if="turn.finalAnswer" class="turn-section final-section">
              <div class="section-header">
                <CheckCircleOutlined class="section-icon" />
                <span class="section-title">最终答案</span>
              </div>
              <div class="section-content final-answer">
                {{ turn.finalAnswer }}
              </div>
            </div>
          </div>
        </div>

        <!-- 执行统计 -->
        <div class="execution-stats">
          <div class="stat-item">
            <span class="stat-label">总轮数：</span>
            <span class="stat-value">{{ reActHistory.length }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">总耗时：</span>
            <span class="stat-value">{{ totalDuration }}ms</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">成功率：</span>
            <span class="stat-value">{{ successRate }}%</span>
          </div>
        </div>
      </div>

      <!-- 控制按钮 -->
      <div v-if="processing || reActHistory.length > 0" class="control-buttons">
        <a-button @click="clearHistory" :disabled="processing">
          清空历史
        </a-button>
        <a-button v-if="processing" @click="stopProcess" type="danger">
          停止执行
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  BulbOutlined,
  ThunderboltOutlined,
  EyeOutlined,
  CheckCircleOutlined
} from '@ant-design/icons-vue'
import { chatWithAi, testReAct } from '@/api/ai'

// 响应式数据
const userInput = ref('')
const processing = ref(false)
const reActHistory = ref([])
const currentTurn = ref(-1)
const selectedScenario = ref(null)

// 复杂场景预设
const complexScenarios = reactive([
  {
    id: 1,
    icon: '👥',
    title: '批量用户管理',
    description: '删除研发部所有禁用状态的用户',
    input: '删除研发部所有状态为禁用的用户',
    complexity: 'high',
    expectedSteps: 4
  },
  {
    id: 2,
    icon: '📊',
    title: '数据统计分析',
    description: '统计各部门用户分布情况',
    input: '统计各部门的用户数量分布，并按人数排序',
    complexity: 'medium',
    expectedSteps: 3
  },
  {
    id: 3,
    icon: '🔄',
    title: '批量状态更新',
    description: '批量修改用户状态',
    input: '将所有临时用户的状态改为正常',
    complexity: 'medium',
    expectedSteps: 3
  },
  {
    id: 4,
    icon: '🎯',
    title: '条件查询操作',
    description: '查找并处理特定条件的数据',
    input: '查找注册时间超过一年且从未登录的用户，并发送提醒',
    complexity: 'high',
    expectedSteps: 5
  },
  {
    id: 5,
    icon: '📋',
    title: '报告生成',
    description: '生成综合性数据报告',
    input: '生成本月用户活跃度报告，包括新增用户、活跃用户和流失用户统计',
    complexity: 'high',
    expectedSteps: 6
  },
  {
    id: 6,
    icon: '🔧',
    title: '系统维护',
    description: '执行系统维护任务',
    input: '清理所有过期的临时数据，并更新系统配置',
    complexity: 'medium',
    expectedSteps: 4
  }
])

// 计算属性
const totalDuration = computed(() => {
  return reActHistory.value.reduce((total, turn) => total + (turn.duration || 0), 0)
})

const successRate = computed(() => {
  if (reActHistory.value.length === 0) return 0
  const successCount = reActHistory.value.filter(turn => turn.status === 'completed').length
  return Math.round((successCount / reActHistory.value.length) * 100)
})

// 方法
const selectScenario = (scenario) => {
  selectedScenario.value = scenario
  userInput.value = scenario.input
}

const startReActProcess = async () => {
  if (!userInput.value.trim()) {
    message.warning('请输入要执行的任务')
    return
  }

  processing.value = true
  reActHistory.value = []
  currentTurn.value = -1

  try {
    console.log('开始ReAct处理:', userInput.value)

    // 调用ReAct测试接口
    const response = await testReAct({
      message: userInput.value,
      conversationHistory: []
    })

    console.log('AI响应:', response)

    // 检查响应格式
    if (response && (response.code === 200 || response.success !== false)) {
      const responseData = response.data || response

      // 解析ReAct执行历史
      parseReActHistory(responseData)

      if (reActHistory.value.length > 0) {
        message.success('智能体执行完成')
      } else {
        message.warning('未获取到执行历史')
      }
    } else {
      console.error('API响应错误:', response)
      message.error('智能体执行失败：' + (response.msg || response.message || '未知错误'))

      // 创建错误历史记录
      reActHistory.value = [{
        turnNumber: 1,
        thought: '尝试处理用户请求',
        action: null,
        observation: '',
        finalAnswer: '',
        status: 'error',
        duration: 0,
        error: true,
        errorMessage: response.msg || response.message || '执行失败'
      }]
    }
  } catch (error) {
    console.error('ReAct执行异常:', error)
    message.error('执行过程中发生异常: ' + error.message)

    // 创建异常历史记录
    reActHistory.value = [{
      turnNumber: 1,
      thought: '尝试处理用户请求',
      action: null,
      observation: '',
      finalAnswer: '',
      status: 'error',
      duration: 0,
      error: true,
      errorMessage: '网络异常或服务不可用'
    }]
  } finally {
    processing.value = false
  }
}

const parseReActHistory = (aiResponse) => {
  console.log('解析ReAct历史:', aiResponse)

  // 检查是否有ReAct执行历史
  if (aiResponse.reActHistory && Array.isArray(aiResponse.reActHistory)) {
    reActHistory.value = aiResponse.reActHistory.map((turn, index) => ({
      turnNumber: turn.turnNumber || (index + 1),
      thought: turn.thought || '',
      action: turn.action ? {
        functionName: turn.action.functionName,
        params: turn.action.extractedParams || turn.action.params || {},
        dangerous: turn.action.dangerous || false
      } : null,
      observation: turn.observation || '',
      finalAnswer: turn.finalAnswer || '',
      status: turn.isError ? 'error' :
              turn.finalAnswer ? 'completed' :
              turn.observation ? 'completed' : 'processing',
      duration: turn.duration || (turn.endTime && turn.startTime ? turn.endTime - turn.startTime : 0),
      error: turn.isError,
      errorMessage: turn.errorMessage || ''
    }))

    console.log('解析后的ReAct历史:', reActHistory.value)
  } else {
    // 如果没有详细历史，但有响应内容，创建一个简单的记录
    const hasContent = aiResponse.message || aiResponse.content
    if (hasContent) {
      reActHistory.value = [{
        turnNumber: 1,
        thought: '分析用户请求并制定执行计划',
        action: {
          functionName: '智能处理',
          params: {},
          dangerous: false
        },
        observation: '任务处理完成',
        finalAnswer: aiResponse.message || aiResponse.content,
        status: 'completed',
        duration: 1000
      }]
    } else {
      // 完全没有有效响应
      reActHistory.value = [{
        turnNumber: 1,
        thought: '尝试处理用户请求',
        action: null,
        observation: '',
        finalAnswer: '',
        status: 'error',
        duration: 0,
        error: true,
        errorMessage: '处理失败，请稍后重试'
      }]
    }
  }
}

const getStatusColor = (status) => {
  switch (status) {
    case 'completed': return 'green'
    case 'error': return 'red'
    case 'processing': return 'blue'
    default: return 'default'
  }
}

const getStatusText = (status) => {
  switch (status) {
    case 'completed': return '已完成'
    case 'error': return '出错'
    case 'processing': return '执行中'
    default: return '待执行'
  }
}

const clearHistory = () => {
  reActHistory.value = []
  currentTurn.value = -1
  selectedScenario.value = null
  userInput.value = ''
}

const stopProcess = () => {
  processing.value = false
  message.info('已停止执行')
}
</script>

<style scoped>
.react-demo {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.demo-header {
  text-align: center;
  margin-bottom: 32px;
}

.demo-header h2 {
  color: #1890ff;
  margin-bottom: 8px;
}

.demo-description {
  color: #666;
  font-size: 14px;
  line-height: 1.6;
}

.complex-scenarios {
  margin-bottom: 32px;
}

.scenario-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.scenario-card {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
}

.scenario-card:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.scenario-card.active {
  border-color: #1890ff;
  background-color: #f0f8ff;
}

.scenario-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.scenario-title {
  font-weight: bold;
  margin-bottom: 4px;
}

.scenario-desc {
  color: #666;
  font-size: 12px;
  margin-bottom: 12px;
}

.scenario-complexity {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.complexity-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  color: white;
}

.complexity-badge.high {
  background-color: #ff4d4f;
}

.complexity-badge.medium {
  background-color: #faad14;
}

.complexity-badge.low {
  background-color: #52c41a;
}

.steps-count {
  font-size: 10px;
  color: #1890ff;
}

.input-section {
  margin-bottom: 32px;
}

.input-group {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.user-input {
  flex: 1;
}

.start-btn {
  white-space: nowrap;
}

.react-process {
  background-color: #fafafa;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
}

.process-timeline {
  margin-bottom: 24px;
}

.turn-item {
  background-color: white;
  border-radius: 8px;
  margin-bottom: 16px;
  border: 1px solid #d9d9d9;
  overflow: hidden;
}

.turn-item.active {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.turn-item.completed {
  border-color: #52c41a;
}

.turn-item.error {
  border-color: #ff4d4f;
}

.turn-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #d9d9d9;
}

.turn-number {
  background-color: #1890ff;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  margin-right: 12px;
}

.turn-title {
  font-weight: bold;
  margin-right: auto;
}

.turn-duration {
  font-size: 12px;
  color: #666;
  margin-left: 12px;
}

.turn-section {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.turn-section:last-child {
  border-bottom: none;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.section-icon {
  margin-right: 8px;
  color: #1890ff;
}

.section-title {
  font-weight: bold;
  color: #333;
}

.section-content {
  color: #666;
  line-height: 1.6;
}

.action-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.function-name {
  font-family: 'Courier New', monospace;
  background-color: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.action-params {
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
}

.action-params pre {
  margin: 0;
  font-family: 'Courier New', monospace;
}

.observation-result.error {
  color: #ff4d4f;
}

.final-answer {
  background-color: #f6ffed;
  padding: 12px;
  border-radius: 4px;
  border-left: 4px solid #52c41a;
}

.execution-stats {
  display: flex;
  gap: 24px;
  padding: 16px;
  background-color: white;
  border-radius: 8px;
  border: 1px solid #d9d9d9;
}

.stat-item {
  display: flex;
  align-items: center;
}

.stat-label {
  color: #666;
  margin-right: 4px;
}

.stat-value {
  font-weight: bold;
  color: #1890ff;
}

.control-buttons {
  display: flex;
  gap: 12px;
  justify-content: center;
}
</style>
