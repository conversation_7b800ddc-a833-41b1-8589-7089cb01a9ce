<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-content">
        <h2 class="welcome-text">欢迎回来</h2>
        <p class="sub-title">登录您的账户以继续</p>

        <a-form id="loginForm" ref="loginFormRef" :model="loginFormModel" :rules="loginFormRules" :scrollToFirstError="true">
          <a-form-item name="username">
            <a-input v-model:value="loginFormModel.username" size="large" placeholder="请输入用户名">
              <template #prefix>
                <UserOutlined class="site-form-item-icon" />
              </template>
            </a-input>
          </a-form-item>

          <a-form-item name="password">
            <a-input-password v-model:value="loginFormModel.password" size="large" placeholder="请输入密码">
              <template #prefix>
                <LockOutlined class="site-form-item-icon" />
              </template>
            </a-input-password>
          </a-form-item>

          <a-form-item name="code">
            <div class="verification-code">
              <a-input v-model:value="loginFormModel.code" size="large" placeholder="验证码">
                <template #prefix>
                  <SecurityScanOutlined class="site-form-item-icon" />
                </template>
              </a-input>
              <div class="code-image" @click="getVerifyCode">
                <img :src="loginFormModel.codeUrl" alt="验证码" />
              </div>
            </div>
          </a-form-item>

          <div class="form-footer">
            <a-checkbox>记住我</a-checkbox>
            <a class="forgot-password">忘记密码？</a>
          </div>

          <a-button type="primary" block size="large" :loading="loginFormModel.loginButtonLoading" @click="submitForm">
            {{ loginFormModel.loginButtonName }}
          </a-button>

          <div class="other-actions">
            <span>还没有账号？</span>
            <a-button type="link" @click="goToRegister">立即注册</a-button>
          </div>

          <div class="switch-style">
            <a-button type="link" @click="switchLoginStyle">切换登录风格</a-button>
          </div>
        </a-form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getVerifyCodeImg } from '@/api/login.js';
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/stores/user';
import { UserOutlined, LockOutlined, SecurityScanOutlined } from '@ant-design/icons-vue';
import { usePermissionStore } from '@/stores/permission';

const vueRouter = useRouter();
const vueStore = useUserStore();
const loginFormRef = ref();

const loginFormModel = reactive({
  username: 'admin',
  password: 'admin123',
  code: '',
  uuid: '',
  codeUrl: '',
  loginButtonDisabled: false,
  loginButtonLoading: false,
  loginButtonName: '登录',
});

const loginFormRules = reactive({
  username: [{ required: true, message: '请输入用户名！', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码！', trigger: 'blur' }],
  code: [{ required: true, message: '请输入验证码！', trigger: 'blur' }],
});

const getVerifyCode = () => {
  getVerifyCodeImg().then((res) => {
    loginFormModel.codeUrl = 'data:image/gif;base64,' + res.img;
    loginFormModel.uuid = res.uuid;
  });
};

const submitForm = async () => {
  try {
    loginFormModel.loginButtonDisabled = true;
    loginFormModel.loginButtonLoading = true;
    loginFormModel.loginButtonName = '登录中...';

    await loginFormRef.value.validate();
    await vueStore.login(loginFormModel);
    await vueStore.getInfo();

    const permissionStore = usePermissionStore();
    const accessRoutes = await permissionStore.generateRoutes();
    accessRoutes.forEach(route => {
      vueRouter.addRoute(route);
    });

    await vueRouter.push({ path: '/home/<USER>' });
  } catch (error) {
    console.error('登录失败:', error);
    loginFormModel.loginButtonDisabled = false;
    loginFormModel.loginButtonLoading = false;
    loginFormModel.loginButtonName = '登录';
    getVerifyCode();
  }
};

const goToRegister = () => {
  vueRouter.push('/register');
};

const switchLoginStyle = () => {
  vueRouter.push('/login3');
};

getVerifyCode();
</script>

<style lang="less" scoped>
.login-container {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;

  .login-box {
    width: 420px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    padding: 40px;

    .login-content {
      .welcome-text {
        font-size: 28px;
        font-weight: 600;
        color: #1a1a1a;
        margin-bottom: 8px;
        text-align: center;
      }

      .sub-title {
        font-size: 16px;
        color: #666;
        margin-bottom: 32px;
        text-align: center;
      }

      .verification-code {
        display: flex;
        gap: 16px;

        .ant-input-affix-wrapper {
          flex: 1;
        }

        .code-image {
          width: 100px;
          height: 40px;
          cursor: pointer;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }

      .form-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 16px 0;

        .forgot-password {
          color: #1890ff;
          cursor: pointer;

          &:hover {
            color: #40a9ff;
          }
        }
      }

      .site-form-item-icon {
        color: #bfbfbf;
      }

      .other-actions {
        margin-top: 24px;
        text-align: center;
        color: #666;
      }

      .switch-style {
        margin-top: 16px;
        text-align: center;
      }
    }
  }
}
</style>