<template>
  <div>
    <ProTable
        ref="proTableRef"
        :api="tableApi"
        :columns="columns"
        :initialSearchParams="initialSearchParams"
        :searchFields="searchFields"
        rowKey="tableId"
    >
      <!-- 1. 自定义操作按钮 -->
      <template #actions="{ selectedRowKeys, delete: deleteRows }">
        <a-button type="primary" @click="openImportTablesModal">
          <BearJiaIcon icon="plus-outlined"/>导入表
        </a-button>
        <a-button :disabled="selectedRowKeys.length <= 0" danger type="primary"
                  @click="() => deleteRows()">
          <BearJiaIcon icon="delete-outlined"/>删除
        </a-button>
      </template>

      <!-- 2. 自定义列渲染 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'operateCol'">
          <span>
            <a @click="openUpdateModal(record)">修改</a>
            <a-divider type="vertical"/>
            <a @click="openPreviewModal(record)">预览</a>
            <a-divider type="vertical"/>
            <a @click="generateCode(record)">生成代码</a>
          </span>
        </template>
      </template>
    </ProTable>

    <!-- Modals -->
    <ImportTables ref="importTablesRef" @refreshFatherPageTable="() => proTableRef.refresh()"/>
    <GenCodeConfigUpdate ref="genCodeConfigUpdateRef" @refreshFatherPageTable="() => proTableRef.refresh()"/>
    <GenCodePreview ref="genCodePreviewRef"/>
  </div>
</template>

<script setup>
import {computed, ref} from 'vue';
import {listTable, delTable, genCode} from '@/api/tool/gen';
import BearJiaUtil from '@/utils/BearJiaUtil.js';

// 引入组件
import ProTable from '@/components/BearJiaProTable/index.vue';
import GenCodeConfigUpdate from './genCodeConfigUpdate.vue';
import GenCodePreview from './genCodePreview.vue';
import ImportTables from './importTables.vue';
import {BearJiaIcon} from '@/utils/BearJiaIcon.js';

const proTableRef = ref();

// --- ProTable 配置 ---
const tableApi = {list: listTable, delete: delTable};
const initialSearchParams = {tableName: null, tableComment: null, createTimeRange: null};
// 代码生成页面没有导出功能
const exportConfig = null;

const searchFields = computed(() => [
  {name: 'tableName', label: '表名称', type: 'input'},
  {name: 'tableComment', label: '表描述', type: 'input'},
  {name: 'createTimeRange', label: '创建时间', type: 'daterange'},
]);

const columns = [
  {title: '表名称', dataIndex: 'tableName', key: 'tableName'},
  {title: '表描述', dataIndex: 'tableComment', key: 'tableComment'},
  {title: '实体', dataIndex: 'className', key: 'className'},
  {title: '创建时间', dataIndex: 'createTime', key: 'createTime'},
  {title: '更新时间', dataIndex: 'updateTime', key: 'updateTime'},
  {title: '操作', key: 'operateCol', width: 200, fixed: 'right'},
];

// --- 页面特有逻辑 ---
const importTablesRef = ref();
const genCodeConfigUpdateRef = ref();
const genCodePreviewRef = ref();

const openImportTablesModal = () => importTablesRef.value.openModal();
const openUpdateModal = (record) => genCodeConfigUpdateRef.value.openUpdateModal(record);
const openPreviewModal = (record) => genCodePreviewRef.value.openPreviewModal(record);

// 生成代码
const generateCode = (record) => {
  const tableNames = record.tableName;

  if (record.genType === '1') {
    genCode(record.tableName).then((response) => {
      BearJiaUtil.messageSuccess('成功生成到自定义路径：' + record.genPath);
    });
  } else {
    BearJiaUtil.zip('/tool/gen/batchGenCode?tables=' + tableNames, '表[' + tableNames + ']代码文件.zip');
  }
};
</script>

<style lang="less"></style>
