<template>
  <a-row :gutter="[24, 24]">
    <a-col :span="8">
      <a-card title="个人信息" :bordered="false">
        <div class="text-center">
          <a-avatar :size="104" :src="user.avatar">
            <template #icon><UserOutlined /></template>
          </a-avatar>
          <div class="mt-2">{{ user.nickName }}</div>
        </div>
        <ul class="list-group list-group-striped mt-4 department-item">
          <li class="list-group-item department-item">
            <div class="item-label">
              <TeamOutlined class="icon" />
              <span>所属部门：</span>
            </div>
            <span class="department-value">{{ user.deptName || '暂无' }}</span>
          </li>

          <li class="list-group-item department-item">
            <div class="item-label">
              <span>所属角色：</span>
            </div>
            <span class="department-value">{{ user.roles.join(', ') || '暂无' }}</span>
          </li>
          <li class="list-group-item department-item">
            <div class="item-label">
              <ClockCircleOutlined class="icon" />
              <span>创建时间：</span>
            </div>
            <span class="department-value">{{ user.createTime || '暂无' }}</span>
          </li>
        </ul>
      </a-card>
    </a-col>
    <a-col :span="16">
      <a-card style="height: 600px;" title="个人信息" :bordered="false">
        <a-tabs v-model:activeKey="activeTabKey" tab-position="left">
        <a-tab-pane key="basic" tab="基本资料">
          <a-form :model="userForm" :rules="rules" ref="userFormRef" :label-col="{ span: 6 }" :wrapper-col="{ span: 14 }" class="modern-form">
            <a-form-item label="用户昵称" name="nickName" class="form-item-hover">
              <a-input v-model:value="userForm.nickName" placeholder="请输入用户昵称" class="modern-input" />
            </a-form-item>
            <a-form-item label="手机号码" name="phonenumber" class="form-item-hover">
              <a-input v-model:value="userForm.phonenumber" placeholder="请输入手机号码" class="modern-input" />
            </a-form-item>
            <a-form-item label="邮箱" name="email" class="form-item-hover">
              <a-input v-model:value="userForm.email" placeholder="请输入邮箱" class="modern-input" />
            </a-form-item>
            <a-form-item :wrapper-col="{ offset: 6, span: 14 }">
              <a-button type="primary" @click="submitForm" class="modern-button">保存</a-button>
            </a-form-item>
          </a-form>
        </a-tab-pane>
        <a-tab-pane key="avatar" tab="修改头像">
          <a-upload
            name="avatar"
            list-type="picture-card"
            class="avatar-uploader"
            :show-upload-list="false"
            :before-upload="beforeAvatarUpload"
            @change="handleAvatarChange"
          >
            <img v-if="imageUrl" :src="imageUrl" alt="avatar" style="width: 100%" />
            <div v-else>
              <loading-outlined v-if="loading"></loading-outlined>
              <plus-outlined v-else></plus-outlined>
              <div class="ant-upload-text">上传头像</div>
            </div>
          </a-upload>
        </a-tab-pane>
        <a-tab-pane key="password" tab="修改密码">
          <a-form :model="passwordForm" :rules="passwordRules" ref="passwordFormRef" :label-col="{ span: 6 }" :wrapper-col="{ span: 14 }">
            <a-form-item label="旧密码" name="oldPassword">
              <a-input-password v-model:value="passwordForm.oldPassword" placeholder="请输入旧密码" />
            </a-form-item>
            <a-form-item label="新密码" name="newPassword">
              <a-input-password v-model:value="passwordForm.newPassword" placeholder="请输入新密码" />
            </a-form-item>
            <a-form-item label="确认密码" name="confirmPassword">
              <a-input-password v-model:value="passwordForm.confirmPassword" placeholder="请确认新密码" />
            </a-form-item>
            <a-form-item :wrapper-col="{ offset: 6, span: 14 }">
              <a-button type="primary" @click="updatePassword">保存</a-button>
            </a-form-item>
          </a-form>
        </a-tab-pane>
      </a-tabs>
      </a-card>
    </a-col>
  </a-row>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';
import { useUserStore } from '@/stores/user';
import { message } from 'ant-design-vue';
import { UserOutlined, LoadingOutlined, PlusOutlined } from '@ant-design/icons-vue';
import BearJiaUtil from '@/utils/BearJiaUtil.js';

// 获取 store
const userStore = useUserStore();

// 替换 Vuex store 的使用
const user = computed(() => userStore);

const activeTabKey = ref('basic');
const userFormRef = ref();
const passwordFormRef = ref();

// 用户表单数据
const userForm = reactive({
  nickName: user.value.nickName,
  phonenumber: user.value.phonenumber,
  email: user.value.email
});

// 表单验证规则
const rules = {
  nickName: [{ required: true, message: '请输入用户昵称', trigger: 'blur' }],
  phonenumber: [{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }],
  email: [{ type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }]
};

// 密码表单数据
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
});

// 密码表单验证规则
const passwordRules = {
  oldPassword: [{ required: true, message: '请输入旧密码', trigger: 'blur' }],
  newPassword: [{ required: true, message: '请输入新密码', trigger: 'blur' }],
  confirmPassword: [{ required: true, message: '请确认新密码', trigger: 'blur' }]
};

// 头像上传相关
const loading = ref(false);
const imageUrl = ref('');

const beforeAvatarUpload = (file) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
  if (!isJpgOrPng) {
    message.error('只能上传 JPG/PNG 格式的图片！');
  }
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    message.error('图片大小不能超过 2MB！');
  }
  return isJpgOrPng && isLt2M;
};

const handleAvatarChange = (info) => {
  if (info.file.status === 'uploading') {
    loading.value = true;
    return;
  }
  if (info.file.status === 'done') {
    loading.value = false;
    imageUrl.value = info.file.response.url;
  }
};

// 提交基本资料表单
const submitForm = () => {
  userFormRef.value.validate().then(() => {
    // TODO: 调用更新用户信息的API
    message.success('保存成功');
  });
};

// 更新密码
const updatePassword = () => {
  passwordFormRef.value.validate().then(() => {
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      message.error('两次输入的密码不一致');
      return;
    }
    // TODO: 调用更新密码的API
    message.success('密码修改成功');
  });
};
</script>

<style lang="less">
@import './index.less';
</style>

<style scoped>
.text-center {
  text-align: center;
}

.mt-2 {
  margin-top: 12px;
}

.mt-4 {
  margin-top: 24px;
}

.list-group {
  padding: 0;
  margin: 0;
  list-style: none;
}

.list-group-item {
  padding: 10px 0;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #f0f0f0;
}

.avatar-uploader {
  text-align: center;
  margin-top: 20px;
}

.ant-upload-text {
  margin-top: 8px;
  color: #666;
}
.department-item {
  transition: all 0.3s ease;
  border-radius: 8px;
  margin-bottom: 8px;
  background-color: rgba(0, 0, 0, 0.02);
  padding: 12px 16px;
}

.department-item:hover {
  background-color: rgba(0, 0, 0, 0.04);
  transform: translateX(4px);
}

.item-label {
  display: flex;
  align-items: center;
  color: #666;
}

.item-label .icon {
  margin-right: 8px;
  color: #1890ff;
  font-size: 16px;
}

.department-value {
  color: #333;
  font-weight: 500;
}
.modern-form {
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.form-item-hover {
  transition: all 0.3s ease;
}

.form-item-hover:hover {
  transform: translateX(4px);
}

.modern-input {
  border-radius: 6px;
  transition: all 0.3s ease;
}

.modern-input:hover, .modern-input:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.modern-button {
  height: 40px;
  padding: 0 32px;
  font-size: 16px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.modern-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
}
</style>