import {createApp} from 'vue';
import {createPinia} from 'pinia';
import App from './App.vue';
import router from './router';
import Antd from 'ant-design-vue';
import directive from './directive';
import VueHighlightJS from 'vue3-highlightjs';
import 'vue3-highlightjs/styles/solarized-light.css';
import 'ant-design-vue/dist/reset.css';
import ErrorHandler from './plugins/errorHandler';
import tablePlugin from '@/plugins/table';

// 导入全局样式 (包含所有主题样式)
import './style/global.less';
// 应用挂载后初始化配置
import {initAllConfigs, watchConfigChanges} from '@/utils/configInit';
// 注册全局组件
import GlobalLoading from './components/common/GlobalLoading.vue';
import DictTag from './components/DictTag/index.vue';
// 导入工具函数

// 导入字典管理函数
import {useDict} from '@/composables/useDict.js';

const app = createApp(App);
const pinia = createPinia();

app.use(pinia)
    .use(router)
    .use(Antd)
    .use(directive)
    .use(VueHighlightJS)
    .use(ErrorHandler)
    .use(tablePlugin);

// 挂载应用
app.mount('#app');

setTimeout(() => {
    initAllConfigs();
    watchConfigChanges();
}, 100);

app.component('GlobalLoading', GlobalLoading);
app.component('DictTag', DictTag);

// 添加全局属性
app.config.globalProperties.useDict = useDict;