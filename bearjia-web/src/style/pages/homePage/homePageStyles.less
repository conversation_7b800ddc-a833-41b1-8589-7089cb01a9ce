#components-layout-demo-custom-trigger .trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}

#components-layout-demo-custom-trigger .trigger:hover {
  color: #1890ff;
}

#components-layout-demo-custom-trigger .logo {
  height: 32px;
  background: rgba(255, 255, 255, 0.3);
  margin: 16px;
}

.site-layout .site-layout-background {
  background: #fff;
}

.homePage-layout-header {
  background: #2f54eb;
  padding: 0;
  height: 50px;
  line-height: 50px;
}

.homePage-menu-fold-outlined {
  font-size: large;
  color: rgb(253, 253, 253);
  padding-left: 10px;
}

// 重写antd样式
.card-container .ant-tabs-tab {
  border-bottom-width: 1.5px !important;
  border-bottom-color: #e4acac !important;
  margin-left: 6px !important;
}

.card-container .ant-tabs-tab-active {
  // 设置激活的tab底边颜色
  border-top-color: rgb(92, 92, 236) !important;
  border-bottom-color: blue !important;
  border-bottom-width: 1.5px !important;
  // background-color: rgb(221, 183, 243) !important;
}

.card-container .ant-tabs-tab-active .ant-tabs-tab-btn {
  color: blue !important;
}
.card-container .ant-tabs .ant-tabs-nav .ant-tabs-nav-wrap {
  background: #fff;
  padding-left: 10px;
  padding-top: 8px;
}

.card-container .ant-tabs-content {
  padding-left: 15px;
}
