// 主题相关的全局变量
:root {
  // Ant Design 基础变量
  --ant-primary-color: #1677ff;
  --ant-primary-color-hover: color-mix(in srgb, var(--ant-primary-color) 90%, white);
  --ant-primary-color-active: color-mix(in srgb, var(--ant-primary-color) 110%, black);
  --ant-primary-1: color-mix(in srgb, var(--ant-primary-color) 20%, white);
  
  // 主题色
  --primary-color: var(--ant-primary-color);
  --primary-color-hover: var(--ant-primary-color-hover);
  --primary-color-active: var(--ant-primary-color-active);
  --primary-1: var(--ant-primary-1);
  
  // 背景色
  --bg-color: #f0f2f5;
  --component-background: #fff;
  
  // 文字颜色
  --text-color: rgba(0, 0, 0, 0.85);
  --text-color-secondary: rgba(0, 0, 0, 0.45);
  
  // 边框颜色
  --border-color-base: #d9d9d9;
  --border-color-split: #f0f0f0;
  
  // 菜单相关
  --menu-bg: var(--menu-bg-light);
  --menu-bg-light: #fff;
  --menu-bg-dark: #001529;
  --menu-item-color: var(--menu-item-color-light);
  --menu-item-color-light: rgba(0, 0, 0, 0.85);
  --menu-item-color-dark: rgba(255, 255, 255, 0.65);
  --menu-item-hover-color: var(--menu-item-hover-color-light);
  --menu-item-hover-color-light: var(--primary-color);
  --menu-item-hover-color-dark: #fff;
  --menu-item-active-color: var(--menu-item-active-color-light);
  --menu-item-active-color-light: var(--primary-color);
  --menu-item-active-color-dark: #fff;
  --menu-item-active-bg: var(--menu-item-active-bg-light);
  --menu-item-active-bg-light: var(--primary-1);
  --menu-item-active-bg-dark: var(--primary-color);
  
  // 按钮相关
  --btn-primary-bg: var(--primary-color);
  --btn-primary-color: #fff;
  --btn-hover-bg: var(--primary-color-hover);
  --btn-active-bg: var(--primary-color-active);
}

// 暗色主题
.dark-theme {
  // 基础颜色
  --bg-color: #141414;
  --component-background: #1f1f1f;
  --text-color: rgba(255, 255, 255, 0.85);
  --text-color-secondary: rgba(255, 255, 255, 0.45);
  --border-color-base: #434343;
  --border-color-split: #303030;
  
  // 菜单暗色主题
  --menu-bg: var(--menu-bg-dark);
  --menu-item-color: var(--menu-item-color-dark);
  --menu-item-hover-color: var(--menu-item-hover-color-dark);
  --menu-item-active-color: var(--menu-item-active-color-dark);
  --menu-item-active-bg: var(--menu-item-active-bg-dark);
  
  // 按钮暗色主题
  --btn-primary-color: #fff;
}

// 主题切换动画
.theme-transition {
  transition: all 0.3s ease-in-out;
}

/* 色弱模式样式 */
.color-weak {
  filter: invert(80%);

  .ant-layout-header,
  .ant-layout-footer,
  .ant-layout-sider {
    filter: invert(100%);
  }

  // 确保图片和图标不被过度处理
  img,
  .anticon,
  .ant-avatar {
    filter: invert(100%);
  }
}