/* 新增布局模式样式 */

/* 分栏布局 */
.layout-column {
  // 分栏布局专用头部
  .column-header {
    background: #fff;
    border-bottom: 1px solid #f0f0f0;
    padding: 0;
    height: 56px;
    line-height: 56px;
    //margin-left: 300px; // 80px (一级菜单) + 220px (二级菜单)
    position: relative;
    z-index: 100;

    .column-header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 100%;
      padding: 0 24px;
    }

    .column-header-left {
      .header-logo {
        display: flex;
        align-items: center;

        .logo-title {
          font-size: 20px;
          font-weight: 600;
          margin-left: 80px;
          color: var(--primary-color);
        }
      }
    }

    .column-header-right {
      .header-actions {
        display: flex;
        align-items: center;
        gap: 16px;

        .action-icon {
          font-size: 16px;
          color: #666;
          cursor: pointer;
          padding: 8px;
          border-radius: 4px;
          transition: all 0.3s;

          &:hover {
            color: var(--primary-color);
            background: #f0f9ff;
          }
        }

        .user-info {
          display: flex;
          align-items: center;
          gap: 8px;
          cursor: pointer;
          padding: 4px 12px;
          border-radius: 6px;
          transition: background 0.3s;

          &:hover {
            background: #f0f9ff;
          }

          .user-name {
            font-size: 14px;
            color: #333;
          }
        }
      }
    }
  }

  .first-level-menu {
    border-right: 1px solid #f0f0f0;
    background: #fafafa;

    .column-first-menu {
      height: 100%;

      .menu-logo {
        padding: 16px;
        text-align: center;
        border-bottom: 1px solid #f0f0f0;
        background: #fff;

        .logo-img {
          width: 32px;
          height: 32px;
          object-fit: contain;
        }
      }

      .ant-menu {
        border-right: none;
        background: transparent;
      }

      .ant-menu-item {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        height: 60px;
        padding: 12px 8px !important;
        margin: 8px 4px;
        border-radius: 8px;
        text-align: center;
        border: 1px solid transparent;

        &:hover {
          background: #e6f7ff;
          border-color: #91d5ff;
        }

        &.ant-menu-item-selected {
          background: var(--primary-color);
          color: #fff;

          .anticon {
            color: #fff !important;
          }
        }

        .anticon {
          font-size: 20px !important;
          display: block !important;
          margin: 0 !important;
        }

        // 隐藏文字，只显示图标
        span {
          display: none !important;
        }

        // 确保 tooltip 包装器也居中
        .ant-tooltip-open {
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
        }
      }
    }
  }

  // HistoryNav 宽度调整
  .column-history {
    //margin-left: 300px !important; // 80px (一级菜单) + 220px (二级菜单)
  }

  .second-level-menu {
    border-right: 1px solid #f0f0f0;
    background: #fff;

    .column-second-menu {
      height: 100%;

      .system-title {
        padding: 16px;
        text-align: center;
        border-bottom: 1px solid #f0f0f0;
        background: #fff;

        .title-text {
          font-size: 16px;
          font-weight: 600;
          color: var(--primary-color);
        }
      }

      .ant-menu {
        border-right: none;
        background: transparent;
      }

      .ant-menu-item {
        margin: 4px 8px;
        border-radius: 6px;

        &:hover {
          background: #f0f9ff;
        }

        &.ant-menu-item-selected {
          background: var(--primary-color);
          color: #fff;

          .anticon {
            color: #fff;
          }
        }
      }

      .ant-menu-submenu {
        margin: 4px 8px;
        border-radius: 6px;

        .ant-menu-submenu-title {
          border-radius: 6px;

          &:hover {
            background: #f0f9ff;
          }
        }

        .ant-menu-item {
          margin: 2px 4px;
        }
      }

      .no-menu-tip {
        padding: 60px 20px;
        text-align: center;
        color: #999;

        p {
          margin: 0;
          font-size: 14px;
        }
      }
    }
  }
}

/* 抽屉布局 */
.layout-drawer {
  .header-bar {
    background: #fff;
    border-bottom: 1px solid #f0f0f0;

    .header-left {
      .trigger {
        font-size: 18px;
        padding: 0 24px;
        cursor: pointer;
        transition: color 0.3s;
        line-height: 56px;

        &:hover {
          color: var(--primary-color);
        }
      }

      .header-title {
        margin-left: 16px;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
      }
    }
  }

  // HistoryNav 全宽
  .drawer-history {
    margin-left: 0;
  }

  .drawer-menu {
    .ant-drawer-header {
      border-bottom: 1px solid #f0f0f0;
      padding: 16px 24px;
      background: #fafafa;

      .ant-drawer-title {
        font-weight: 600;
        font-size: 16px;
        color: #333;
      }
    }

    .drawer-menu-content {
      height: 100%;

      .ant-layout-sider {
        height: 100% !important;
        background: #fff !important;

        .layout-logo {
          display: none; // 隐藏 Logo
        }

        .ant-menu {
          border-right: none;
          height: 100%;
        }
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .layout-column {
    .column-header {
      margin-left: 250px; // 70px + 180px
    }

    .first-level-menu {
      width: 70px !important;
      min-width: 70px !important;

      .ant-menu-item {
        height: 50px;
        padding: 8px 4px;
        margin: 4px 2px;

        .anticon {
          font-size: 16px;
        }
      }
    }

    .second-level-menu {
      width: 180px !important;
      min-width: 180px !important;
    }

    .column-history {
      margin-left: 250px; // 调整后的宽度
    }
  }
}

@media (max-width: 768px) {
  .layout-column {
    .first-level-menu {
      display: none;
    }

    .second-level-menu {
      width: 100% !important;
      min-width: auto !important;
    }
  }

  .layout-drawer {
    .drawer-menu {
      width: 100% !important;
    }
  }
}

/* 暗色主题适配 */
.dark-theme {
  &.layout-column {
    .column-header {
      background: #1f1f1f;
      border-bottom-color: #434343;

      .column-header-left {
        .logo-title {
          color: var(--primary-color);
        }
      }

      .column-header-right {
        .action-icon {
          color: rgba(255, 255, 255, 0.65);

          &:hover {
            color: var(--primary-color);
            background: rgba(255, 255, 255, 0.04);
          }
        }

        .user-info {
          &:hover {
            background: rgba(255, 255, 255, 0.04);
          }

          .user-name {
            color: rgba(255, 255, 255, 0.85);
          }
        }
      }
    }

    .first-level-menu {
      background: #1f1f1f;
      border-right-color: #434343;

      .column-first-menu {
        .menu-logo {
          background: #141414;
          border-bottom-color: #434343;
        }

        .ant-menu-item {
          color: rgba(255, 255, 255, 0.65);

          &:hover {
            background: #111b26;
            border-color: #177ddc;
            color: #177ddc;
          }

          &.ant-menu-item-selected {
            background: var(--primary-color);
            color: #fff;
          }
        }
      }
    }

    .second-level-menu {
      background: #1f1f1f;
      border-right-color: #434343;

      .column-second-menu {
        .system-title {
          background: #141414;
          border-bottom-color: #434343;

          .title-text {
            color: var(--primary-color);
          }
        }

        .ant-menu {
          background: transparent;
          color: rgba(255, 255, 255, 0.65);
        }

        .no-menu-tip {
          color: rgba(255, 255, 255, 0.45);
        }
      }
    }
  }

  &.layout-drawer {
    .drawer-menu {
      .ant-drawer-header {
        background: #1f1f1f;
        border-bottom-color: #434343;

        .ant-drawer-title {
          color: rgba(255, 255, 255, 0.85);
        }
      }

      .ant-drawer-body {
        background: #1f1f1f;
      }
    }
  }
}
