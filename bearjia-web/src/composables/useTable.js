import {computed, onMounted, reactive} from 'vue';
import {message, Modal} from 'ant-design-vue';
import {useTableConfigStore} from '@/stores/tableConfig';
// 假设您有这样的工具库
import BearJiaUtil from '@/utils/BearJiaUtil';
import dayjs from 'dayjs';

/**
 * 通用表格页面逻辑 Hook
 * @param {object} options 配置项
 * @param {object} options.api API接口对象，必须包含 list, delete(可选), exportUrl(可选)
 * @param {Array} options.columns - 表格的列定义
 * @param {object} options.initialSearchParams - 初始的查询表单参数
 * @param {string} [options.rowKey='id'] - 表格行的 Key
 * @param {string} [options.exportFileName='导出数据'] - 默认的导出文件名
 */
export function useTable(options) {
    const {api, columns, initialSearchParams, rowKey = 'id', exportFileName = '导出数据', isTreeTable = false} = options;

    // 实例化 Pinia Store
    const tableConfigStore = useTableConfigStore();

    // 搜索表单数据
    const searchFormData = reactive({...initialSearchParams});

    // 查询参数（包含分页）
    const queryParams = reactive({
        pageNum: 1,
        pageSize: tableConfigStore.pageSize, // 从 Store 获取初始 pageSize
        ...initialSearchParams,
    });

    // 表格状态
    const tableState = reactive({
        dataSource: [],
        total: 0,
        loading: false,
        selectedRowKeys: [],
        columns: columns,
    });

    // --- 方法 ---

    const queryTableData = async () => {
        tableState.loading = true;
        try {
            // 对于树形数据，不需要分页参数
            const requestParams = isTreeTable ?
                {...searchFormData} :
                {...queryParams, ...searchFormData};

            // 调用API获取数据
            const response = await api.list(requestParams);

            // 处理不同的响应格式
            let rows, total;
            if (response.rows !== undefined) {
                // 标准分页格式 { rows, total }
                rows = response.rows;
                total = response.total;
            } else if (Array.isArray(response)) {
                // 直接返回数组（如树形数据）
                rows = response;
                total = response.length;
            } else if (response.data) {
                // 其他格式
                rows = response.data;
                total = response.total || rows.length;
            } else {
                rows = [];
                total = 0;
            }

            // 如果有自定义数据处理函数，则使用它
            if (api.processListData && typeof api.processListData === 'function') {
                rows = api.processListData(rows);
                // 树形数据处理后重新计算总数
                if (isTreeTable) {
                    total = rows.length;
                }
            }

            tableState.dataSource = rows;
            tableState.total = total;
        } catch (error) {
            console.error("Failed to fetch table data:", error);
            message.error('数据加载失败');
        } finally {
            tableState.loading = false;
        }
    };

    // 首次加载
    onMounted(() => {
        queryTableData();
    });

    const handleSearch = () => {
        queryParams.pageNum = 1;
        queryTableData();
    };

    const handleReset = () => {
        Object.keys(initialSearchParams).forEach(key => {
            searchFormData[key] = initialSearchParams[key];
        });
        handleSearch();
    };

    const handleTableChange = (pagination) => {
        queryParams.pageNum = pagination.current;
        queryParams.pageSize = pagination.pageSize;
        queryTableData();
    };

    const onSelectionChange = (keys) => {
        tableState.selectedRowKeys = keys;
    };

    const handleDelete = (ids) => {
        if (!api.delete) {
            console.error("API 'delete' function is not provided.");
            return;
        }
        const targetIds = ids || tableState.selectedRowKeys;
        if (!targetIds || targetIds.length === 0) {
            message.warning('请至少选择一条数据');
            return;
        }
        Modal.confirm({
            title: '确认删除',
            content: `您确定要删除选中的 ${targetIds.length} 条数据吗？`,
            onOk: async () => {
                await api.delete(targetIds);
                message.success('删除成功');
                tableState.selectedRowKeys = [];
                // 为了更好的用户体验，删除后留在当前页刷新
                queryTableData();
            },
        });
    };

    const handleExport = () => {
        if (!api.exportUrl) {
            console.error("API 'exportUrl' is not provided.");
            return;
        }
        const finalExportFileName = `${exportFileName}_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.xlsx`;
        BearJiaUtil.download(api.exportUrl, queryParams, finalExportFileName);
    };

    // --- 计算属性 ---

    const pagination = computed(() => {
        // 如果是树形数据或者总数小于等于分页大小，则不显示分页
        if (isTreeTable || tableState.total <= queryParams.pageSize) {
            return false;
        }

        return {
            total: tableState.total,
            current: queryParams.pageNum,
            pageSize: queryParams.pageSize,
            showSizeChanger: tableConfigStore.showSizeChanger,
            showQuickJumper: tableConfigStore.showQuickJumper,
            pageSizeOptions: tableConfigStore.pageSizeOptions,
            showTotal: tableConfigStore.showTotal ? (total) => `共 ${total} 条` : undefined,
        };
    });

    const rowSelection = computed(() => ({
        selectedRowKeys: tableState.selectedRowKeys,
        onChange: onSelectionChange,
    }));

    return {
        searchFormData,
        tableState,
        pagination,
        rowSelection,
        queryTableData,
        handleSearch,
        handleReset,
        handleTableChange,
        handleDelete,
        handleExport,
    };
}